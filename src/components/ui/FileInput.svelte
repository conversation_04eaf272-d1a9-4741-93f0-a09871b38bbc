<script lang="ts">
    import { onMount } from "svelte";
    import type { FormEventHandler } from "svelte/elements";

    import IconamoonSignTimesBold from "~icons/iconamoon/sign-times-bold";
    import MaterialSymbolsAttachFile from "~icons/material-symbols/attach-file";
    import Button from "$components/ui/Button.svelte";
    import { t } from "$lib/i18n";
    import { cn } from "$lib/utils/style";

    export let value: File[] = [];
    export let preview = false;
    export let limit = 3;

    let attachButtonElement: HTMLInputElement;
    let previews: string[] = [];

    const onFileInput: FormEventHandler<HTMLInputElement> = (event) => {
        const fileList = (event.target as HTMLInputElement).files;

        if (!fileList) {
            return;
        }

        const _files = Array.from(fileList).filter((file) => !value.some((f) => f.name === file.name));

        value = [...value, ..._files].slice(0, limit);

        if (preview) {
            previews = [
                ...previews,
                ...Array.from(_files).map((file) => URL.createObjectURL(file)),
            ].slice(0, limit);
        }

        attachButtonElement.value = "";
    };

    const removeFile = (index: number) => {
        value = value.filter((_, i) => i !== index);
        previews = previews.filter((_, i) => i !== index);
    };

    onMount(() => {
        return () => {
            previews.forEach((src) => URL.revokeObjectURL(src));
        };
    });
</script>

<div>
    <Button
        on:click={() => attachButtonElement.click()}
        disabled={value.length >= limit || $$restProps.disabled}
        class={cn($$restProps.class)}
    >
        <slot>
            <MaterialSymbolsAttachFile />
            <span>Attach files</span>
        </slot>
    </Button>

    {#if $$restProps.multiple}
        <p class="text-sm text-center opacity-50 mt-3">{limit} {$t("common.files_maximum")}</p>
    {/if}

    {#if preview}
        <ul class="grid grid-cols-3 gap-3 mt-3">
            {#each previews as src, index}
                <li class="relative col-span-1 border border-dashed border-slate-200 rounded-2xl aspect-square flex items-center justify-center p-3">
                    <img
                        {src}
                        alt="file"
                        class="w-full h-full object-contain"
                    />
                    <button
                        on:click={() => removeFile(index)}
                        class="absolute -top-1 -right-1 bg-white rounded-full border border-slate-200 disabled:opacity-50 p-0.5"
                        disabled={$$restProps.disabled}
                    >
                        <IconamoonSignTimesBold
                            width="22"
                            height="22"
                        />
                    </button>
                </li>
            {/each}
        </ul>
    {/if}

    <input
        bind:this={attachButtonElement}
        on:input={onFileInput}
        type="file"
        accept={$$restProps.accept}
        multiple={$$restProps.multiple}
        class="hidden"
    />
</div>
