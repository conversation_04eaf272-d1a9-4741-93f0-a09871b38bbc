<script lang="ts">
    import { createEventDispatcher } from "svelte";

    import SolarSadSquareLineDuotone from "~icons/solar/sad-square-line-duotone";
    import Button from "$components/ui/Button.svelte";
    import Dialog from "$components/ui/Dialog.svelte";
    import { t } from "$lib/i18n";

    export let visible: boolean;

    const dispatch = createEventDispatcher();

    const onClose = (() => {
        visible = false;
        dispatch("close");
    });
</script>

<Dialog
    bind:visible={visible}
    contentClass="bg-orange-400"
>
    <div class="flex-1 flex flex-col text-center text-white">
        <h3 class="text-3xl font-bold">
            <slot name="title">{$t("common.oops")}</slot>
        </h3>
        <div class="flex justify-center mt-16">
            <SolarSadSquareLineDuotone class="w-full h-full max-w-[20dvh]" />
        </div>

        <div class="mt-auto mb-16">
            <p class="text-xl font-medium">
                <slot name="content">{$t("common.something_wrong_happened")}</slot>
            </p>
        </div>

        <Button
            on:click={onClose}
            class="w-full bg-white active:bg-slate-100 text-black"
        >
            <slot name="button-text">{$t("common.dismiss")}</slot>
        </Button>
    </div>
</Dialog>
