<script lang="ts">
    export let value: string;
</script>

<div class="relative">
    <textarea
        bind:value={value}
        placeholder={$$restProps.placeholder}
        maxlength={$$restProps.maxlength}
        rows={$$restProps.rows}
        class="block w-full rounded-2xl outline-none shadow-app focus:shadow-md border-2 border-white bg-slate-50 transition-shadow focus:border-primary-500 resize-none p-3"
    />
    {#if $$restProps.maxlength}
        <div class="absolute bottom-2 right-1">
            <p class="text-xs font-medium text-black/50 leading-none mt-1 mr-1">{value.length} / {$$restProps.maxlength}</p>
        </div>
    {/if}
</div>
