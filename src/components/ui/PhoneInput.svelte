<script lang="ts">
    import { AsYouType, isValidPhoneNumber } from "libphonenumber-js";
    import { createEventDispatcher, SvelteComponent } from "svelte";
    import type { FormEventHandler, SvelteHTMLElements } from "svelte/elements";
    import { slide } from "svelte/transition";

    import FlagpackCa from "~icons/flagpack/ca";
    import FlagpackUs from "~icons/flagpack/us";
    import MaterialSymbolsArrowDropDown from "~icons/material-symbols/arrow-drop-down";
    import { cn } from "$lib/utils/style";

    type Events = {
        validation: boolean
        focus: void
        blur: void
    };

    type Country = "US" | "CA";

    type Flag = {
        title: string
        value: Country
        icon: typeof SvelteComponent<SvelteHTMLElements["svg"]>
    };

    const flags: Flag[] = [
        {
            title: "United States (+1)",
            value: "US",
            icon: FlagpackUs,
        },
        {
            title: "Canada (+1)",
            value: "CA",
            icon: FlagpackCa,
        },
    ];

    export let value: string;

    const dispatch = createEventDispatcher<Events>();

    let focusing = false;
    let isStateValid = true;
    let isFlagListVisible = false;
    let country: Country = "CA";

    $: selectedFlagIcon = flags.find((flag) => flag.value === country)?.icon ?? null;

    $: {
        const formattedNumber = new AsYouType(country).input(value);
        if (formattedNumber.charAt(formattedNumber.length - 1) === value.charAt(value.length - 1)) {
            value = formattedNumber;
        }
    }

    $: {
        dispatch("validation", isValidPhoneNumber(value, country));
    }

    const onInput: FormEventHandler<HTMLInputElement> = (event) => {
        value = (event.target as HTMLInputElement).value;
    };

    const onFocus = () => {
        focusing = true;
        isStateValid = true;
        dispatch("focus");
    };

    const onBlur = () => {
        focusing = false;
        isStateValid = !value || isValidPhoneNumber(value, country);
        dispatch("blur");
    };

    const selectCountry = (c: Country) => {
        country = c;
        isFlagListVisible = false;
    };
</script>

<div
    class={cn("relative bg-white text-black text-3xl flex items-center rounded-2xl shadow-md", {
        "outline outline-2 outline-red-500/80": !isStateValid,
        "outline outline-2 outline-primary-200": focusing,
    })}
>
    <button
        on:click={() => isFlagListVisible = !isFlagListVisible}
        class="shrink-0 self-stretch flex items-center pl-3 pr-1"
    >
        <svelte:component this={selectedFlagIcon} />
        <MaterialSymbolsArrowDropDown
            class={cn("opacity-50 transition-all", {
                "-rotate-180": isFlagListVisible,
            })}
        />
    </button>
    <input
        {...$$restProps}
        {value}
        on:input={onInput}
        on:focus={onFocus}
        on:blur={onBlur}
        class={cn("flex-1 w-full font-theme outline-none bg-slate-50 rounded-r-2xl placeholder:text-primary-500/50 py-4 px-3", {
            "placeholder:text-red-500/80": !isStateValid,
        })}
        type="tel"
    />

    {#if isFlagListVisible}
        <ul
            transition:slide={{ axis: "y", duration: 150 }}
            class="absolute bottom-[120%] left-0 z-[1] w-full bg-slate-50 shadow-app rounded-2xl max-h-56 overflow-auto"
        >
            {#each flags as flag}
                <li class="border-b last-of-type:border-b-0 active:bg-white first-of-type:rounded-t-2xl last-of-type:rounded-b-2xl">
                    <button
                        on:click={() => selectCountry(flag.value)}
                        class="flex items-center w-full gap-3 p-3"
                    >
                        <svelte:component this={flag.icon} />
                        <span>{flag.title}</span>
                    </button>
                </li>
            {/each}
        </ul>
    {/if}
</div>
