<script lang="ts">
    import dayjs from "dayjs";
    import { onMount } from "svelte";
    import { derived, writable } from "svelte/store";
    import { fly } from "svelte/transition";

    import MaterialSymbolsInstallMobileRounded from "~icons/material-symbols/install-mobile-rounded";
    import Button from "$components/ui/Button.svelte";
    import HikoLogoWhite from "$lib/assets/images/hiko-white.png";
    import { useInstallPrompt } from "$lib/composables/useInstallPrompt";
    import { useModal } from "$lib/composables/useModal";
    import { t } from "$lib/i18n";

    const SKIPPED_STORAGE_KEY = "install-prompt-skipped-at";

    const isSkipped = writable(true);

    const { isVisible } = useModal();

    const {
        isInstallable,
        isInstalled,
        installApp,
    } = useInstallPrompt();

    const canInstall = derived([isInstallable, isInstalled], ([installable, installed]) => installable && !installed);

    const onSkip = () => {
        isSkipped.set(true);
        localStorage.setItem(SKIPPED_STORAGE_KEY, new Date().toISOString());
    };

    onMount(() => {
        const cachedSkippedAt = localStorage.getItem(SKIPPED_STORAGE_KEY);

        if (!cachedSkippedAt || dayjs(cachedSkippedAt).add(10, "minutes").isBefore(dayjs())) {
            isSkipped.set(false);
        }

        const unsubscribe = canInstall.subscribe((value) => isVisible.set(value));

        return () => {
            unsubscribe();
        };
    });
</script>

{#if $isVisible && !$isSkipped}
    <aside
        transition:fly={{ duration: 300, y: 300 }}
        class="fixed z-10 top-0 left-0 w-full h-full bg-black/50 flex justify-center"
    >
        <div class="flex flex-col flex-1 w-full max-w-app bg-primary-500 px-3 py-6">
            <div class="flex justify-center">
                <img
                    src={HikoLogoWhite}
                    alt="HIKO"
                    width="240"
                />
            </div>
            <p class="font-theme uppercase text-3xl text-center leading-snug font-medium text-white my-auto px-3">{$t("common.installation.message")}</p>
            <div class="grid grid-cols-1 gap-6 mt-auto">
                <Button
                    on:click={installApp}
                    class="w-full bg-white text-primary-500 font-theme uppercase text-3xl"
                >
                    <MaterialSymbolsInstallMobileRounded />
                    <span>{$t("common.installation.install")}</span>
                </Button>
                <button
                    on:click={onSkip}
                    class="text-white font-medium"
                >
                    {$t("common.installation.skip")}
                </button>
            </div>
        </div>
    </aside>
{/if}
