<script lang="ts">
    import { cn } from "$lib/utils/style";

    export let step = 0;
    export let max = 0;

    $: steps = new Array(max).fill(0).map((_, index) => index);
</script>

<div class={cn("flex items-center justify-center gap-3", $$restProps.class)}>
    {#each steps as index}
        <div
            class={cn("rounded-full w-3 h-3", {
                "bg-white/30": index !== step,
                "bg-white": index === step,
            })}
        />
    {/each}
</div>
