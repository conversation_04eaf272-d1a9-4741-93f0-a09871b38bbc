<script lang="ts">
    import { slide } from "svelte/transition";

    import HeroiconsChevronDown from "~icons/heroicons/chevron-down";
    import Card from "$components/ui/Card.svelte";
    import { cn } from "$lib/utils/style";

    let open = false;
</script>

<Card>
    <button
        on:click={() => open = !open}
        class="flex items-center gap-2 w-full"
    >
        <span class="text-left flex-1 font-medium">
            <slot />
        </span>
        <HeroiconsChevronDown
            width="22"
            height="22"
            class={cn("shrink-0 transition-all", {
                "-rotate-180": open,
            })}
        />
    </button>
    {#if open}
        <div
            transition:slide={{ axis: "y", duration: 150 }}
            class="text-xs md:text-sm text-justify opacity-60 mt-3"
        >
            <slot name="content" />
        </div>
    {/if}
</Card>
