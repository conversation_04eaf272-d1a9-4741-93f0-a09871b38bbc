<script lang="ts">
    import { createEventDispatcher, onMount } from "svelte";

    type Events = {
        complete: void
        focus: void
        blur: void
    };

    export let value: string;

    const values: string[] = new Array(6).fill("");

    const dispatch = createEventDispatcher<Events>();

    $: {
        const chars = value.split("");
        for (let i = 0; i < chars.length && i < 6; i++) {
            values[i] = chars[i].trim();
        }
    }

    $: {
        if (value.trim().length === 6) {
            dispatch("complete");
        }
    }

    const onFocus = (event: FocusEvent) => {
        (event.target as HTMLInputElement).select();
        dispatch("focus");
    };

    const onBlur = () => {
        dispatch("blur");
    };

    const onPaste = (event: ClipboardEvent, index: number) => {
        event.preventDefault();
        const paste = event.clipboardData?.getData("text") ?? "";

        const chars = paste.split("");
        for (let i = index; i < index + chars.length && i < values.length; i++) {
            values[i] = chars[i - index];
        }
    };

    const onKeyUp = (event: KeyboardEvent) => {
        const el = event.target as HTMLInputElement;
        if (event.key === "Backspace" && el.previousSibling) {
            (el.previousSibling as HTMLInputElement).focus();
        }
    };

    const updateValue = () => {
        const localValues = values.map((val) => val ? val : " ");
        value = localValues.join("");
    };

    const setupListeners = () => {
        const elements = document.querySelectorAll<HTMLInputElement>(".otp-input");
        const listeners: ((event: Event) => void)[] = [];

        elements.forEach((el, index) => {
            const onInput = (event: Event) => {
                if (
                    isNaN(+(event.target as HTMLInputElement).value)
                        || (event as InputEvent).data === " "
                ) {
                    values[index] = "";
                    updateValue();
                    return;
                }

                updateValue();

                if (el.value.length > 0 && el.nextSibling) {
                    (el.nextSibling as HTMLInputElement).focus();
                }
            };

            listeners.push(onInput);

            el.addEventListener("input", onInput);
        });

        return () => {
            elements.forEach((el, index) => {
                el.removeEventListener("input", listeners[index]);
            });
        };
    };

    onMount(() => {
        const unsubscribe = setupListeners();

        return () => {
            unsubscribe();
        };
    });
</script>

<div class="grid grid-cols-6 gap-2 text-black">
    {#each values as value, index}
        <input
            bind:value={value}
            on:focus={onFocus}
            on:blur={onBlur}
            on:paste={(event) => onPaste(event, index)}
            on:keyup={onKeyUp}
            name="token"
            type="text"
            maxlength="1"
            inputmode="numeric"
            pattern="[0-9]*"
            autocomplete="one-time-code"
            class="otp-input grid-cols-1 text-xl max-w-16 aspect-square rounded-2xl border-2 border-white text-center shadow-app bg-slate-50 outline-none focus:border-primary-400 p-3"
        />
    {/each}
</div>
