<script lang="ts">
    import { createEventDispatcher } from "svelte";
    import { fly } from "svelte/transition";

    import Button from "$components/ui/Button.svelte";
    import { useModal } from "$lib/composables/useModal";
    import { t } from "$lib/i18n";
    import { cn } from "$lib/utils/style";

    type Events = {
        confirm: void;
        cancel: void;
    };

    export let visible: boolean;
    export let loading = false;
    export let dangerMode = false;

    const { isVisible } = useModal();

    $: isVisible.set(visible);

    const dispatch = createEventDispatcher<Events>();

    const onConfirm = () => {
        dispatch("confirm");
    };

    const onCancel = () => {
        dispatch("cancel");
        visible = false;
    };
</script>

{#if visible}
    <aside class="fixed z-[1] top-0 left-0 w-full h-full flex items-center justify-center p-6">
        <button
            on:click={() => visible = false}
            class={cn("absolute top-0 left-0 w-full h-full bg-black/50 backdrop-blur", { "pointer-events-none": loading })}
        />
        <div
            transition:fly={{ duration: 300, y: 300 }}
            class="relative bg-white rounded-2xl w-full max-w-[450px] shadow-app"
        >
            <header class="text-2xl font-bold border-b text-center p-5">
                {$t("common.are_you_sure")}
            </header>
            <main class="border-b px-3 py-5">
                <slot />
            </main>
            <footer class="grid grid-cols-2 gap-3 p-3">
                <Button
                    on:click={onCancel}
                    class={cn("col-span-1 bg-white active:bg-slate-100 text-black text-lg px-4 py-2", { "pointer-events-none": loading })}
                >
                    {$t("common.cancel")}
                </Button>
                <Button
                    on:click={onConfirm}
                    {loading}
                    class={cn("col-span-1 text-lg px-4 py-2", { "pointer-events-none": loading, "bg-red-500 active:bg-red-400": dangerMode })}
                >
                    {$t("common.confirm")}
                </Button>
            </footer>
        </div>
    </aside>
{/if}
