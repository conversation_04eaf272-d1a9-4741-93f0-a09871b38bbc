<script lang="ts">
    import { cn } from "$lib/utils/style";

    export let progress = 0;
    export let max = 0;
</script>

<div
    class={cn("relative grid gap-0.5 h-3 w-full rounded-2xl", $$restProps.class)}
    style={`grid-template-columns: repeat(${max}, minmax(0, 1fr));`}
>
    {#each Array.from({ length: max }).map((_, index) => index) as i}
        <div
            class={cn("bg-slate-200 col-span-1 first-of-type:rounded-l-2xl last-of-type:rounded-r-2xl", {
                "bg-primary-500": i < progress,
            })}
        />
    {/each}
</div>
