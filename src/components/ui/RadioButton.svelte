<script lang="ts">
    import { v4 as uuidv4 } from "uuid";

    import { cn } from "$lib/utils/style";

    export let value: string;
    export let name: string = "";
    export let id = uuidv4();
</script>

<article
    class={cn("border-2 border-white rounded-2xl shadow-app", {
        "border-primary-500": value === id,
    }, $$restProps.class)}
>
    <input
        bind:group={value}
        type="radio"
        value={id}
        {name}
        {id}
        class="pointer-events-none hidden"
    />
    <label
        class="cursor-pointer flex gap-4 text-left py-4 px-3"
        for={id}
    >
        <span
            class={cn("border-2 border-black/20 rounded-full w-5 h-5 shrink-0 flex items-center justify-center mt-1", {
                "border-primary-500": value === id,
            })}
        >
            {#if value === id}
                <span class="w-2.5 h-2.5 rounded-full bg-primary-500" />
            {/if}
        </span>
        <span>
            <slot />
        </span>
    </label>
</article>
