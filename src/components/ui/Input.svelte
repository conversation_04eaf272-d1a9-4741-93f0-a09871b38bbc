<script lang="ts">
    import { slide } from "svelte/transition";

    import { cn } from "$lib/utils/style";

    type Rule = boolean | string;

    export let value: string;
    export let rules: Rule[] = [];

    let hasInteracted = false;

    $: isStateValid = !hasInteracted || rules.every((rule) => typeof rule !== "string");
    $: stateError = (hasInteracted && rules.find((rule) => typeof rule === "string")) || "";

    const onInput = (event: Event) => {
        value = (event.target as HTMLInputElement).value;
    };

    const onBlur = () => {
        hasInteracted = true;
    };
</script>

<div class="relative text-black">
    <!-- svelte-ignore a11y-autofocus -->
    <input
        {value}
        on:input={onInput}
        on:blur={onBlur}
        class={cn("block w-full rounded-2xl outline-none shadow-app focus:shadow-md border-2 border-white bg-slate-50 transition-shadow focus:border-primary-500 p-3", {
            "border-red-400": !isStateValid,
            "placeholder:text-red-400/80": !isStateValid,
        })}
        id={$$restProps.id}
        type={$$restProps.type}
        name={$$restProps.name}
        required={$$restProps.required}
        placeholder={$$restProps.placeholder}
        autofocus={$$restProps.autofocus}
        minlength={$$restProps.minlength}
        maxlength={$$restProps.maxlength}
        autocomplete={$$restProps.autocomplete}
        inputmode={$$restProps.inputmode}
        pattern={$$restProps.pattern}
    />
</div>
{#if !isStateValid}
    <div
        transition:slide={{ axis: "y" }}
        class="absolute px-1 mt-1">
        <p class="text-sm text-red-400/80">{stateError}</p>
    </div>
{/if}
