<script lang="ts">
    import type { LeafletEvent, Map, Marker } from "leaflet";
    import { createEventDispatcher, onMount } from "svelte";

    import UserDot from "$lib/assets/images/dot.png";
    import { latitude, longitude } from "$lib/stores/user";
    import { cn } from "$lib/utils/style";

    type Events = {
        click: void
        zoom: number
        load: { map: Map, userMarker: Marker, leaflet: typeof import("leaflet") }
    };

    const DEFAULT_ZOOM = 18;
    const MIN_ZOOM = 10;
    const MAX_ZOOM = 22;

    const dispatch = createEventDispatcher<Events>();

    const onZoomStart = () => {
        const markerEl = document.querySelector(".user-marker");
        markerEl?.classList.toggle("smooth", false);
    };

    const onZoomEnd = () => {
        const markerEl = document.querySelector(".user-marker");
        markerEl?.classList.toggle("smooth", true);
    };

    const onZoom = (event: LeafletEvent) => {
        dispatch("zoom", event.target._zoom);
    };

    const loadMap = async () => {
        const leaflet = await import("leaflet");

        const map = leaflet
            .map("map-view")
            .setView([$latitude, $longitude], DEFAULT_ZOOM)
            .setMinZoom(MIN_ZOOM)
            .on("zoomstart", onZoomStart)
            .on("zoomend", onZoomEnd)
            .on("zoom", onZoom)
            .on("click", () => dispatch("click"));

        leaflet.tileLayer("https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png", {
            subdomains: "abcd",
            maxZoom: MAX_ZOOM,
        }).addTo(map);

        const userMarker = leaflet.marker([$latitude, $longitude], {
            icon: leaflet.icon({
                iconSize: [24, 24],
                iconUrl: UserDot,
                iconAnchor: [12, 12],
                className: "map-marker user-marker",
            }),
        }).addTo(map);

        dispatch("load", { map, userMarker, leaflet });
    };

    onMount(() => {
        loadMap();
    });
</script>

<div
    id="map-view"
    class={cn("w-full min-h-[400px]", $$restProps.class)}
/>

<style>
    :global(.leaflet-control-attribution, .leaflet-control-container) {
        display: none;
    }

    :global(.map-marker) {
        filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
    }

    :global(.user-marker) {
        z-index: 1000 !important;
        filter: drop-shadow(0 0 4px rgba(17, 181, 228, 0.8));
        pointer-events: none !important;
    }

    :global(.smooth) {
        transition: transform 200ms linear;
    }
</style>
