<script lang="ts">
    import { createEventDispatcher } from "svelte";

    import { cn } from "$lib/utils/style";

    type Events = {
        change: number
    };

    export let value: number;
    export let items: string[] = [];

    $: widthStyle = items.length > 0 ? `width: ${(100 / items.length).toFixed(0)}%` : "";
    $: translateStyle = `transform: translateX(${100 * value}%)`;

    $: anchorStyle = `${widthStyle}; ${translateStyle};`;

    const dispatch = createEventDispatcher<Events>();

    const onClickItem = (index: number) => {
        const prevValue = value;
        value = index;

        if (prevValue !== index) {
            dispatch("change", index);
        }
    };
</script>

<div class={cn("rounded-2xl p-2", $$restProps.class)}>
    <div class={cn("relative grid", `grid-cols-${items.length}`)}>
        <div
            class={cn("absolute top-0 left-0 bg-primary-500 h-full rounded-xl transition-all")}
            style={anchorStyle}
        />
        {#each items as item, index}
            <button
                on:click={() => onClickItem(index)}
                class={cn(
                    "relative rounded-2xl transition-all p-2",
                    {
                        "text-white font-medium": index === value,
                    },
                    $$restProps.buttonClass,
                )}
            >
                {item}
            </button>
        {/each}
    </div>
</div>
