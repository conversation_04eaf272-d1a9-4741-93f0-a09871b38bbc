<script lang="ts">
    import { derived, writable } from "svelte/store";

    import { invalidateAll } from "$app/navigation";
    import Tabs from "$components/ui/Tabs.svelte";
    import { locale, locales, setLocale, t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { cn } from "$lib/utils/style";

    let isLoading = false;

    const selectedTabIndex = writable(Math.max(0, $locales.indexOf($locale)));
    const tabItems = derived(locales, (value) => value.map((lang) => $t(`lang.${lang}`)));

    const updateLanguage = async (event: CustomEvent<number>) => {
        const language = $locales[event.detail];

        try {
            if (language === $locale || isLoading) {
                return;
            }

            setLocale(language);
            isLoading = true;

            const { data: { session } } = await supabase.auth.getSession();

            if (session) {
                await supabase.auth.updateUser({
                    data: {
                        language,
                    },
                });
            }

            setTimeout(async () => {
                await invalidateAll();
                window.location.reload();
            }, 0);
        } catch (err) {
            console.error(err);

            setLocale($locale);
            selectedTabIndex.set($locales.indexOf($locale));
        } finally {
            isLoading = false;
        }
    };
</script>

<Tabs
    {...$$restProps}
    bind:value={$selectedTabIndex}
    on:change={updateLanguage}
    items={$tabItems}
    class={cn("bg-white rounded-2xl p-2", { "pointer-events-none": isLoading }, $$restProps.class)}
/>
