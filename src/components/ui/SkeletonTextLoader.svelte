<script lang="ts">
    import { cn } from "$lib/utils/style";

    export let loading: boolean = false;
    export let length = 15;

    $: str = new Array(length).fill("_").join("");
</script>

<div class={cn($$restProps.class)}>
    {#if loading}
        <span class="animate-pulse bg-slate-100 rounded-md px-2">
            <span class="opacity-0">{str}</span>
        </span>
    {:else}
        <slot />
    {/if}
</div>
