<script lang="ts">
    import MingcuteInformationFill from "~icons/mingcute/information-fill";
    import { cn } from "$lib/utils/style";

    type AlertType = "info" | "success" | "warning" | "error";

    export let color: AlertType = "info";
    let colorClass = "";

    $: {
        switch (color) {
            case "info":
                colorClass = "bg-blue-500/10 text-blue-500 border-blue-600/50";
                break;
            case "success":
                colorClass = "bg-green-500/10 text-primary-600 border-primary-600/50";
                break;
            case "warning":
                colorClass = "bg-orange-500/10 text-orange-600 border-orange-600/50";
                break;
            case "error":
                colorClass = "bg-red-500/10 text-red-500 border-red-600/50";
                break;
        }
    }
</script>

<article class={cn("rounded-xl border p-3", colorClass, $$restProps.class)}>
    <div class="flex gap-2">
        <MingcuteInformationFill
            width="16"
            height="16"
            class="shrink-0 mt-1.5"
        />
        <div>
            <p class="text-lg font-medium">
                <slot name="title" />
            </p>
            <slot />
        </div>
    </div>
</article>
