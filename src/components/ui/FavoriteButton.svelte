<script lang="ts">
    import { createEventDispatcher } from "svelte";

    import PhHeartFill from "~icons/ph/heart-fill";
    import PhHeartLight from "~icons/ph/heart-light";

    type Events = {
        like: void;
        unlike: void;
    }

    export let value: boolean;

    const dispatch = createEventDispatcher<Events>();

    const onClick = () => {
        value ? dispatch("unlike") : dispatch("like");
    };
</script>

<button
    on:click|stopPropagation|preventDefault={onClick}
    {...$$restProps}
>
    {#if value}
        <PhHeartFill class="text-primary-500" />
    {:else}
        <PhHeartLight class="opacity-30" />
    {/if}
</button>
