<script lang="ts">
    import HeroiconsChevronLeft16Solid from "~icons/heroicons/chevron-left-16-solid";
    import HikoLogoWhite from "$lib/assets/images/hiko-white.png";
    import { backUrl, cssClass,isBackButtonVisible, isVisible } from "$lib/composables/useTopBar";
    import { cn } from "$lib/utils/style";
</script>

{#if $isVisible}
    <nav class={cn("sticky top-0 z-[1] w-full max-w-app mx-auto bg-primary-500 text-white flex justify-center py-3", $cssClass)}>
        {#if $isBackButtonVisible}
            <a
                href={$backUrl}
                class="absolute left-3 top-1/2 -translate-y-1/2">
                <HeroiconsChevronLeft16Solid
                    width="35"
                    height="35"
                />
            </a>
        {/if}
        <img
            src={HikoLogoWhite}
            alt="HIKO"
            width="120"
            height="44"
        />
    </nav>
{/if}
