<script lang="ts">
    import { createEventDispatcher, onMount } from "svelte";
    import type { FormEventHandler } from "svelte/elements";

    type Events = {
        complete: void
    };

    export let value: string;
    export let length: number = 6;

    let inputElement: HTMLInputElement;

    $: {
        if (value.trim().length === 6) {
            dispatch("complete");
        }
    }

    const dispatch = createEventDispatcher<Events>();

    const onInput: FormEventHandler<HTMLInputElement> = (event) => {
        const target = event.currentTarget;

        if (!isNaN(+target.value)) {
            value = target.value;
        } else {
            target.value = value;
        }
    };

    const onPaste = (event: ClipboardEvent) => {
        event.preventDefault();
        const paste = event.clipboardData?.getData("text") ?? "";

        if (!isNaN(+paste)) {
            value = paste;
        }
    };

    onMount(() => {
        if ($$restProps.autofocus) {
            inputElement?.focus();
        }
    });
</script>

<input
    bind:this={inputElement}
    type="text"
    {value}
    on:input={onInput}
    on:paste={onPaste}
    name="token"
    maxlength={length}
    inputmode="numeric"
    pattern="[0-9]*"
    autocomplete="one-time-code"
    placeholder="1 2 3 4 5 6"
    class="
        w-full text-center text-3xl text-black tracking-[0.75rem] shadow-md font-theme outline-none bg-slate-50 rounded-2xl py-4 px-3
        placeholder:text-center placeholder:tracking-[0.2rem] placeholder:text-primary-500/50
    "
/>
