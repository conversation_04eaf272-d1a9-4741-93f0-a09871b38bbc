<script lang="ts">
    import { fly } from "svelte/transition";

    import { useModal } from "$lib/composables/useModal";
    import { cn } from "$lib/utils/style";

    export let visible: boolean;

    const { isVisible } = useModal();

    $: isVisible.set(visible);
</script>

{#if visible}
    <aside
        transition:fly={{ duration: 300, y: 300 }}
        class={cn("fixed z-[1] bottom-0 left-0 w-full h-full flex flex-col", $$restProps.class)}
    >
        <div class={cn("flex-1 flex flex-col bg-primary-500 w-full max-w-app overflow-auto mx-auto p-6", $$restProps.contentClass)}>
            <slot />
        </div>
    </aside>
{/if}
