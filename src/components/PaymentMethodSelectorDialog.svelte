<script lang="ts">
    import type { PaymentMethod } from "@stripe/stripe-js";
    import { createEventDispatcher } from "svelte";

    import { goto } from "$app/navigation";
    import CreditCardItem from "$components/CreditCardItem.svelte";
    import Button from "$components/ui/Button.svelte";
    import Dialog from "$components/ui/Dialog.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { usePaymentMethods } from "$lib/composables/usePaymentMethods";
    import { t } from "$lib/i18n";
    import { Route } from "$types/routes";

    type Events = {
        select: PaymentMethod
    };

    export let visible: boolean;

    let isLoaded = false;

    const {
        load,
        sortedPaymentMethods,
        defaultPaymentMethodId,
        isLoading,
    } = usePaymentMethods(false);

    $: {
        if (visible && !isLoaded) {
            load();
            isLoaded = true;
        }
    }

    const dispatch = createEventDispatcher<Events>();

    const selectPaymentMethod = (paymentMethod: PaymentMethod) => {
        dispatch("select", paymentMethod);
        visible = false;
    };

    const navigateToAddPaymentMethod = async () => {
        visible = false;
        await goto(Route.PaymentMethodsCreate, {
            state: {
                returnTo: Route.Home,
            },
        });
    };
</script>

<Dialog
    bind:visible={visible}
    contentClass="p-0"
>
    <div class="px-3 py-6">
        <h2 class="font-bold text-2xl text-center text-white mb-6">{$t("home.select_payment_method")}</h2>
        {#if $isLoading}
            <div class="flex justify-center mt-16">
                <Loader class="text-white/80" />
            </div>
        {:else if $sortedPaymentMethods.length === 0}
            <div class="mt-16">
                <EmptyPlaceholder
                    class="text-white"
                    iconContainerClass="opacity-80"
                >
                    <p>{$t("home.no_payment_methods")}</p>
                </EmptyPlaceholder>
            </div>
        {:else}
            <ul class="grid grid-cols-1 gap-3">
                {#each $sortedPaymentMethods as paymentMethod}
                    {#if paymentMethod.card}
                        <li>
                            <button
                                on:click={() => selectPaymentMethod(paymentMethod)}
                                class="w-full text-left"
                            >
                                <CreditCardItem
                                    card={paymentMethod.card}
                                    isDefault={paymentMethod.id === $defaultPaymentMethodId}
                                    class="border-none"
                                />
                            </button>
                        </li>
                    {/if}
                {/each}
            </ul>
        {/if}
    </div>
    <div class="sticky bottom-0 bg-primary-500 mt-auto px-3 py-6">
        <Button
            on:click={navigateToAddPaymentMethod}
            class="bg-transparent active:bg-black/10 border-2 w-full mb-3"
        >
            <span class="truncate">{$t("home.add_payment_method")}</span>
        </Button>
        <Button
            on:click={() => visible = false}
            class="bg-white active:bg-slate-100 text-black w-full"
        >
            {$t("home.cancel")}
        </Button>
    </div>
</Dialog>
