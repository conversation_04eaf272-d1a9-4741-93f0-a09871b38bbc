<script lang="ts">
    import { createEventDispatcher } from "svelte";

    import IcOutlineRefresh from "~icons/ic/outline-refresh";
    import { cn } from "$lib/utils/style.js";

    export let delay = 1000;

    const dispatch = createEventDispatcher();

    let loading = false;
    let time = 0;

    const onClick = (event: MouseEvent & {currentTarget: (EventTarget & HTMLButtonElement)}) => {
        if (Date.now() - time < delay) {
            return;
        }

        time = Date.now();
        dispatch("click", event);

        loading = true;
        setTimeout(() => {
            loading = false;
        }, delay);
    };
</script>

<button
    on:click={onClick}
    disabled={loading}
    class={cn(
        "bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 p-3",
        $$restProps.class,
    )}
>
    <IcOutlineRefresh
        width="22"
        height="22"
        class={cn({
            "animate-spin": loading,
        })}
    />
</button>
