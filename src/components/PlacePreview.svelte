<script lang="ts">
    import { fly } from "svelte/transition";

    import MaterialSymbolsDirections from "~icons/material-symbols/directions";
    import MaterialSymbolsFootprint from "~icons/material-symbols/footprint";
    import Button from "$components/ui/Button.svelte";
    import Card from "$components/ui/Card.svelte";
    import { locale, t } from "$lib/i18n";
    import { latitude, longitude } from "$lib/stores/user";
    import { formatDistance } from "$lib/utils/distance";
    import type { Address } from "$types/maps";

    export let address: Address;

    $: formattedDistance = formatDistance(address.distance_meters, {
        language: $locale,
        unitDisplay: "long",
    });
    $: directionUrl = `https://www.google.com/maps/dir/?api=1&origin=${$latitude},${$longitude}&destination_place_id=${address.place_id}&destination=${encodeURIComponent(address.fulltext)}`;
</script>

<aside
    transition:fly={{ duration: 300, y: 300 }}
    class="fixed z-[1] bottom-0 left-0 w-full pointer-events-none"
>
    <div class="grid grid-cols-1 gap-3 w-full max-w-app mx-auto px-3 pb-6">
        <Card class="pointer-events-auto">
            <p class="text-xl font-medium truncate mb-2">{address.text}</p>
            <p class="text-sm line-clamp-2">{address.fulltext}</p>

            <div class="flex items-center gap-1 opacity-30 text-xs mt-3">
                <MaterialSymbolsFootprint
                    width="18"
                    height="18"
                />
                <p>{$t("home.away", { value: formattedDistance })}</p>
            </div>
        </Card>
        <Button
            href={directionUrl}
            target="_blank"
            class="font-theme uppercase text-3xl w-full pointer-events-auto"
        >
            <MaterialSymbolsDirections />
            <span>{$t("home.see_directions")}</span>
        </Button>
    </div>
</aside>
