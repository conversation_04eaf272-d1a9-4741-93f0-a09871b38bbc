<script lang="ts">
    import type { Stripe, StripeCardElement, StripeElementLocale } from "@stripe/stripe-js";
    import { loadStripe } from "@stripe/stripe-js/pure";
    import { AxiosError } from "axios";
    import { onMount } from "svelte";

    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import Button from "$components/ui/Button.svelte";
    import { env } from "$env/dynamic/public";
    import { locale, t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { Route } from "$types/routes";

    let stripe: Stripe | null = null;
    let cardElement: StripeCardElement | null = null;
    let isCreatingPaymentMethod = false;
    let errorMessage: string | undefined = "";

    const initializeForm = async () => {
        stripe = await loadStripe(env.PUBLIC_STRIPE_KEY);

        if (!stripe) {
            return;
        }

        const elements = stripe.elements({
            locale: $locale as StripeElementLocale,
        });

        cardElement = elements.create("card", {
            hidePostalCode: true,
            classes: {
                base: "block w-full rounded-2xl outline-none shadow-app border-2 border-white bg-slate-50 transition-shadow p-3",
                invalid: "border-red-400",
                focus: "shadow-md border-primary-500",
            },
            style: {
                base: {
                    fontSize: "16px",
                    lineHeight: "1.375",
                    "::placeholder": {
                        fontSize: "16px",
                    },
                },
                invalid: {
                    color: "#ff3860",
                },
            },
        });

        cardElement.mount("#card-form");
    };

    const createPaymentMethod = async () => {
        if (!stripe || !cardElement) {
            return;
        }

        isCreatingPaymentMethod = true;

        const { paymentMethod, error } = await stripe.createPaymentMethod({
            type: "card",
            card: cardElement,
        });

        if (error) {
            errorMessage = error.message;
            isCreatingPaymentMethod = false;
            return;
        }

        try {
            await api.post("/payments/methods", {
                payment_method_id: paymentMethod.id,
            });
            await goto($page.state.returnTo ?? Route.PaymentMethods);
        } catch (error) {
            if (error instanceof AxiosError) {
                errorMessage = error.response?.data?.message;
            }
        }

        isCreatingPaymentMethod = false;
    };

    onMount(() => {
        initializeForm();
    });
</script>

<form on:submit={createPaymentMethod}>
    <div id="card-form" />
</form>
{#if errorMessage}
    <p class="text-red-500 mt-3">{errorMessage}</p>
{/if}
<Button
    on:click={createPaymentMethod}
    loading={isCreatingPaymentMethod}
    disabled={!stripe || isCreatingPaymentMethod}
    class="w-full mt-auto"
>
    <span class="font-theme uppercase text-3xl">{$t("payment_methods.create")}</span>
</Button>
