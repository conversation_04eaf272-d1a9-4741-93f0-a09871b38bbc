<script lang="ts">
    import type { PaymentMethod } from "@stripe/stripe-js";
    import PostHog from "posthog-js";
    import Stripe from "stripe";
    import { createEventDispatcher, onMount } from "svelte";
    import { derived } from "svelte/store";
    import { fly } from "svelte/transition";

    import AntDesignAndroidFilled from "~icons/ant-design/android-filled";
    import AntDesignAppleFilled from "~icons/ant-design/apple-filled";
    import SolarTicketSaleBold from "~icons/solar/ticket-sale-bold";
    import BiStripe from "~icons/bi/stripe";
    import CreditCardItem from "$components/CreditCardItem.svelte";
    import UsbC from "$components/icon/UsbC.svelte";
    import PaymentMethodSelectorDialog from "$components/PaymentMethodSelectorDialog.svelte";
    import Button from "$components/ui/Button.svelte";
    import Card from "$components/ui/Card.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import SkeletonLoader from "$components/ui/SkeletonLoader.svelte";
    import Tabs from "$components/ui/Tabs.svelte";
    import { useLocalStorage } from "$lib/composables/useLocalStorage";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { Sentry } from "$lib/services/sentry";
    import { getBatteryStationSlotId } from "$lib/stores/order";
    import { isStationRentPreviewVisible, station } from "$lib/stores/stations";
    import { BATTERY_PURCHASE_PRICE, getHourlyPriceCents, MAX_CHARGEABLE_HOURS_PER_DAY, MAX_DAYS_TO_KEEP } from "$lib/utils/pricing";
    import { getPromoLabel, loadSavedPromoCode } from "$lib/utils/promo-codes.client";
    import { cn } from "$lib/utils/style";
    import { BatteryCableType } from "$types/carku";
    import { PostHogEvent } from "$types/posthog";

    enum OrderTypeTabIndex {
        Rental = 0,
        Purchase = 1,
    }

    type Events = {
        rent: number
        purchase: number
    };

    const cableType = useLocalStorage<BatteryCableType>("cableType", BatteryCableType.USBTypeC);
    let paymentMethod: PaymentMethod | null = null;
    let isRenting = false;
    let isPurchasing = false;
    let isLoadingPaymentMethod = true;
    let showPaymentMethodSelector = false;
    let showErrorDialog = false;

    let errors = {
        missing_cable: false,
        missing_payment_method: false,
    };

    const cableOptions = [
        {
            type: BatteryCableType.Lightning,
            icon: AntDesignAppleFilled,
            label: "Apple",
        },
        {
            type: BatteryCableType.MicroUSB,
            icon: AntDesignAndroidFilled,
            label: "Android",
        },
        {
            type: BatteryCableType.USBTypeC,
            icon: UsbC,
            label: "Type-C",
        },
    ];

    let activeTab = OrderTypeTabIndex.Rental;
    const tabItems = [
        $t("home.rental"),
        $t("home.purchase"),
    ];
    const hourlyPrice = derived(station, (value) => getHourlyPriceCents(value) / 100);

    const dispatch = createEventDispatcher<Events>();

    const selectCableType = (type: BatteryCableType) => {
        cableType.set(type);
        errors.missing_cable = false;
    };

    const selectPaymentMethod = (event: CustomEvent<PaymentMethod>) => {
        paymentMethod = event.detail;
        errors.missing_payment_method = false;
    };

    const onClickAction = async () => {
        if (isLoadingPaymentMethod || isRenting || isPurchasing) {
            return;
        }

        if (!$cableType) {
            errors.missing_cable = true;
            return;
        }

        if (!paymentMethod) {
            errors.missing_payment_method = true;
            return;
        }

        if (activeTab === OrderTypeTabIndex.Rental) {
            await rentBattery();
        } else if (activeTab === OrderTypeTabIndex.Purchase) {
            await purchaseBattery();
        }
    };

    const rentBattery = async () => {
        if (!paymentMethod) {
            Sentry.captureMessage("Payment method is missing for rental");
            return;
        }

        try {
            isRenting = true;

            const slotIdPromise = getBatteryStationSlotId();
            await api.post("/payments/orders/rent", {
                station_id: $station.id,
                payment_method_id: paymentMethod.id,
                cable: $cableType,
                promo_code: promoCode?.code,
            });

            let slotId = -1;

            try {
                slotId = await slotIdPromise;
                dispatch("rent", slotId);
            } catch (error) {
                Sentry.captureException(error);
                window.location.reload();
            }

            PostHog.capture(PostHogEvent.OrderRental, {
                station_id: $station.id,
                payment_method_id: paymentMethod.id,
                cable: $cableType,
                slot_id: slotId,
            });

            isStationRentPreviewVisible.set(false);
        } catch (error) {
            Sentry.captureException(error);
            showErrorDialog = true;
        } finally {
            isRenting = false;
        }
    };

    const purchaseBattery = async () => {
        if (!paymentMethod) {
            Sentry.captureException("Payment method is missing for purchase");
            return;
        }

        try {
            isPurchasing = true;
            await api.post("/payments/orders/purchase", {
                station_id: $station.id,
                payment_method_id: paymentMethod.id,
                cable: $cableType,
            });

            let slotId = -1;

            try {
                slotId = await getBatteryStationSlotId();
                dispatch("purchase", slotId);
            } catch (error) {
                Sentry.captureException(error);
                window.location.reload();
            }

            PostHog.capture(PostHogEvent.OrderPurchase, {
                station_id: $station.id,
                payment_method_id: paymentMethod.id,
                cable: $cableType,
                slot_id: slotId,
            });

            isStationRentPreviewVisible.set(false);
        } catch (error) {
            Sentry.captureException(error);
            showErrorDialog = true;
        } finally {
            isPurchasing = false;
        }
    };

    const loadDefaultPaymentMethod = async () => {
        try {
            isLoadingPaymentMethod = true;
            const { data } = await api.get<PaymentMethod>("/payments/methods/default");
            paymentMethod = data;
        } catch (error) {
            Sentry.captureException(error);
        } finally {
            isLoadingPaymentMethod = false;
        }
    };

    let promoCode: Stripe.PromotionCode | null = null;
    $: promoLabel = promoCode ? getPromoLabel(promoCode) : "";

    const _loadSavedPromoCode = async () => {
        promoCode = await loadSavedPromoCode();
    };

    onMount(() => {
        loadDefaultPaymentMethod();
        _loadSavedPromoCode();
    });
</script>

<aside
    transition:fly={{ duration: 300, y: 300 }}
    class="fixed z-[1] bottom-0 left-0 w-full pointer-events-none"
>
    <div class="grid grid-cols-1 gap-3 w-full max-w-app mx-auto px-3 pb-6">
        <Card class="pointer-events-auto px-0 pb-0 pt-4">
            {#if false}
                <div class="bg-slate-100 rounded-t-2xl mb-4">
                    <Tabs
                        bind:value={activeTab}
                        items={tabItems}
                        class={cn({
                            "pointer-events-none": isRenting || isPurchasing,
                        })}
                    />
                </div>
            {/if}
            <div class="px-5 pb-5">
                <div class="flex items-center justify-between">
                    <p class="font-bold">{$t("home.price")}</p>
                    {#if activeTab === OrderTypeTabIndex.Rental}
                        {#if $hourlyPrice > 0}
                            <div class="text-right">
                                <p class="text-lg font-medium leading-snug">
                                    <span class="-mr-1">${$hourlyPrice}</span>
                                    <span class="font-normal opacity-50 text-xs">/{$t("home.hour")}</span>
                                </p>
                                {#if promoCode}
                                    <p class="text-xs font-medium text-primary-500 flex items-center gap-1">
                                        <SolarTicketSaleBold class="size-4" />
                                        <span>{promoLabel} {$t("home.on_total")}</span>
                                    </p>
                                {/if}
                            </div>
                        {:else}
                            <div class="text-lg font-medium text-primary-500 leading-snug">{$t("common.free")}</div>
                        {/if}
                    {:else if activeTab === OrderTypeTabIndex.Purchase}
                        <span class="text-lg font-medium leading-snug">
                            ${BATTERY_PURCHASE_PRICE}
                        </span>
                    {/if}
                </div>
                {#if false}
                <hr class="my-4" />
                    <div>
                        <div class="mb-3">
                            <p class="font-bold">{$t("home.cable")}</p>
                            {#if errors.missing_cable}
                                <p class="text-xs text-red-500">{$t("home.error_cable_type")}</p>
                            {/if}
                        </div>

                        <div class="flex items-center justify-between md:justify-evenly gap-3 place-items-center">
                            {#each cableOptions as cableOption}
                                <div>
                                    <button
                                        on:click={() => selectCableType(cableOption.type)}
                                        class={cn("border rounded-2xl aspect-square mb-1 p-3", {
                                            "bg-primary-500 text-white border-primary-500": $cableType === cableOption.type,
                                            "border-red-500": errors.missing_cable,
                                        })}
                                    >
                                        <svelte:component
                                            this={cableOption.icon}
                                            width="50"
                                            height="50"
                                        />
                                    </button>
                                </div>
                            {/each}
                        </div>
                    </div>
                {/if}
                <hr class="my-4" />
                <div>
                    <div class="mb-3">
                        <p class="font-bold">{$t("home.payment")}</p>
                        <p class="text-xs text-gray-500 mt-0.5">
                            <strong>{$t("home.deposit")}:</strong>
                            <span>{$t("home.deposit_info", { deposit: BATTERY_PURCHASE_PRICE })}</span>
                        </p>
                        <p class="text-xs text-gray-500 mt-0.5">
                            <strong>{$t("home.hourly_rate")}</strong>
                            <span>{$t("home.hourly_rate_info")}</span>
                        </p>
                        <p class="text-xs text-gray-500 mt-0.5">
                            <strong>{$t("home.battery_return")}:</strong>
                            <span>{$t("home.battery_return_info", { hours: MAX_DAYS_TO_KEEP * 24 })}</span>
                        </p>
                        {#if errors.missing_payment_method}
                            <p class="text-xs text-red-500 mt-1">{$t("home.error_payment_method")}</p>
                        {/if}
                    </div>

                    {#if isLoadingPaymentMethod}
                        <SkeletonLoader class="w-full h-[70px]" />
                    {:else if paymentMethod && paymentMethod.card}
                        <button
                            on:click={() => showPaymentMethodSelector = true}
                            class={cn("w-full text-left", {
                                "pointer-events-none": isRenting || isPurchasing,
                            })}
                        >
                            <CreditCardItem
                                card={paymentMethod.card}
                                class="shadow-none border border-black/10"
                            />
                        </button>
                    {:else}
                        <button
                            on:click={() => showPaymentMethodSelector = true}
                            class={cn("border border-dashed border-black/10 rounded-2xl h-[70px] flex items-center justify-center w-full gap-2 px-3", {
                                "border-red-500": errors.missing_payment_method,
                            })}
                        >
                            <BiStripe class="shrink-0 text-indigo-500" />
                            <span class="truncate">{$t("home.select_payment_method")}</span>
                        </button>
                    {/if}
                </div>
            </div>
        </Card>
        <Button
            on:click={onClickAction}
            loading={isRenting || isPurchasing}
            class="font-theme uppercase text-3xl w-full pointer-events-auto"
        >
            {#if activeTab === OrderTypeTabIndex.Rental}
                {$t("home.rent")}
            {:else if activeTab === OrderTypeTabIndex.Purchase}
                {$t("home.purchase")}
            {/if}
        </Button>
    </div>
</aside>

<PaymentMethodSelectorDialog
    bind:visible={showPaymentMethodSelector}
    on:select={selectPaymentMethod}
/>

<ErrorDialog bind:visible={showErrorDialog} />
