<script lang="ts">
    import dayjs from "dayjs";
    import { onMount } from "svelte";

    import IconamoonLightning2Fill from "~icons/iconamoon/lightning-2-fill";
    import Card from "$components/ui/Card.svelte";
    import { t } from "$lib/i18n";
    import { formatCentsToDollars, getHourlyPriceCents } from "$lib/utils/pricing";
    import { cn } from "$lib/utils/style";
    import { OrderStatus, OrderType } from "$types/orders";
    import type { Tables } from "$types/supabase";

    export let order: Tables<"orders">;
    export let location: Tables<"station_locations"> | null;

    let rentalTime = {
        hours: "00",
        minutes: "00",
        seconds: "00",
    };

    const orderTypeText: Record<string, string> = {
        [OrderType.Rental]: $t("history.rental"),
        [OrderType.Purchase]: $t("history.purchase"),
    };

    $: isActive = order.status === OrderStatus.Ongoing;
    $: hourlyPrice = getHourlyPriceCents(order) / 100;

    const updateRentalTime = () => {
        const elapsedTimeSeconds = dayjs().diff(dayjs(order.started_at), "second");

        const hours = Math.floor(elapsedTimeSeconds / 3600);
        const minutes = Math.floor((elapsedTimeSeconds % 3600) / 60);
        const remainingSeconds = elapsedTimeSeconds % 60;

        rentalTime.hours = hours.toString().padStart(2, "0");
        rentalTime.minutes = minutes.toString().padStart(2, "0");
        rentalTime.seconds = remainingSeconds.toString().padStart(2, "0");
    };

    const subscribeRentalTime = () => {
        if (!isActive) {
            return;
        }

        updateRentalTime();

        const interval = setInterval(() => updateRentalTime(), 1000);

        return () => clearInterval(interval);
    };

    onMount(() => {
        const subscription = subscribeRentalTime();

        return () => {
            subscription?.();
        };
    });
</script>

<Card class={cn($$restProps.class)}>
    <div class="flex items-center gap-2">
        <div class="flex items-center gap-2 shrink-0">
            <div
                class={cn("rounded-lg bg-black/20 text-white aspect-square shrink-0 p-1.5", {
                    "bg-primary-500": isActive,
                })}>
                <IconamoonLightning2Fill />
            </div>
            {#if isActive && order.station_slot_id}
                <div class="border border-primary-500 rounded-lg self-stretch flex items-center justify-center shrink-0 px-2">
                    <p class="text-xs font-medium text-primary-500 capitalize">{$t("home.slot")} {order.station_slot_id}</p>
                </div>
            {/if}
            <div class="border border-black/20 rounded-lg self-stretch flex items-center justify-center shrink-0 px-2">
                {#if isActive}
                    <p class="text-xs font-medium opacity-50">${hourlyPrice}/Hr</p>
                {:else}
                    <p class="text-xs opacity-50">{orderTypeText[order.type]}</p>
                {/if}
            </div>
            {#if order.status === OrderStatus.LateCompleted}
                <div class="border border-orange-300 rounded-lg self-stretch flex items-center justify-center shrink-0 px-2">
                    <p class="text-xs text-orange-400">Late</p>
                </div>
            {/if}
        </div>
        {#if location}
            <div class="text-right truncate ml-auto">
                <p class="text-sm truncate">{location.address}</p>
                <p class="text-xs opacity-50 truncate">{location.city}</p>
            </div>
        {/if}
    </div>
    <hr class="my-3" />
    {#if isActive}
        <div class="flex justify-between">
            <p class="opacity-50">{$t("history.rental_time")}</p>
            <div>
                <p class="font-sans font-bold text-lg flex items-center justify-around gap-0.5 leading-none">
                    <span>{rentalTime.hours}</span>
                    <span>:</span>
                    <span>{rentalTime.minutes}</span>
                    <span>:</span>
                    <span>{rentalTime.seconds}</span>
                </p>
                <p class="text-xs grid grid-cols-3 justify-items-center gap-1 opacity-50 font-medium">
                    <span>Hr</span>
                    <span>Min</span>
                    <span>Sec</span>
                </p>
            </div>
        </div>
    {:else}
        <div class="flex items-center">
            <p class="text-sm opacity-50">{dayjs(order.started_at).format("DD/MM/YYYY")}</p>
            <p class="text-sm opacity-50 ml-6">{dayjs(order.started_at).format("hh:mm A")}</p>
            <p class="font-medium ml-auto">{formatCentsToDollars(order.amount_charged_cents)}</p>
        </div>
    {/if}
    <slot />
</Card>
