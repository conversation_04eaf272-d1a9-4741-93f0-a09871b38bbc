<script lang="ts">
    import { createEventDispatcher } from "svelte";
    import { fly, slide } from "svelte/transition";

    import HeroiconsChevronDown20Solid from "~icons/heroicons/chevron-down-20-solid";
    import SolarCartLarge2Linear from "~icons/solar/cart-large-2-linear";
    import SolarShieldWarningLinear from "~icons/solar/shield-warning-linear";
    import { goto } from "$app/navigation";
    import OrderItem from "$components/OrderItem.svelte";
    import ConfirmDialog from "$components/ui/ConfirmDialog.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { BATTERY_PURCHASE_PRICE, formatCurrency } from "$lib/utils/pricing";
    import { cn } from "$lib/utils/style";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    type Events = {
        purchase: void;
    }

    export let order: Tables<"orders">;
    export let location: Tables<"station_locations"> | null;

    let showMoreOptions = false;
    let showConfirmPurchaseDialog = false;
    let showErrorDialog = false;
    let isPurchasing = false;

    const actions = [
        // {
        //     text: $t("history.purchase_battery"),
        //     icon: SolarCartLarge2Linear,
        //     onClick: () => {
        //         showConfirmPurchaseDialog = true;
        //     },
        // },
        {
            text: $t("history.report_issue"),
            icon: SolarShieldWarningLinear,
            onClick: async () => {
                await goto(Route.Report, {
                    state: {
                        returnTo: Route.Home,
                    },
                });
            },
        },
    ];

    const dispatch = createEventDispatcher<Events>();

    const purchaseBattery = async () => {
        try {
            isPurchasing = true;
            await api.post("/payments/orders/purchase-current");
            showConfirmPurchaseDialog = false;
            dispatch("purchase");
        } catch (error) {
            console.error(error);
            showErrorDialog = true;
        } finally {
            isPurchasing = false;
        }
    };
</script>

<aside
    transition:fly={{ duration: 300, y: 300 }}
    class="fixed z-[1] bottom-0 left-0 w-full pointer-events-none"
>
    <div class="w-full max-w-app mx-auto px-3 pb-6">
        <OrderItem
            order={order}
            location={location}
            class="pointer-events-auto"
        >
            <div class="flex items-center justify-center gap-4 mt-3">
                <hr class={cn("flex-1 opacity-0", { "opacity-100": showMoreOptions })} />
                <button
                    on:click={() => showMoreOptions = !showMoreOptions}
                    class="text-sm opacity-50 flex items-center gap-0.5 shrink-0"
                >
                    <span>{$t("common.more_options")}</span>
                    <HeroiconsChevronDown20Solid
                        class={cn("transition-all", {
                            "-rotate-180": showMoreOptions,
                        })}
                    />
                </button>
                <hr class={cn("flex-1 opacity-0", { "opacity-100": showMoreOptions })} />
            </div>
            {#if showMoreOptions}
                <div
                    transition:slide={{ axis: "y", duration: 150 }}
                    class="mt-3"
                >
                    <ul class="grid grid-cols-1 gap-2">
                        {#each actions as action}
                            <li>
                                <button
                                    on:click={action.onClick}
                                    class="flex items-center gap-3 w-full text-left bg-slate-100 active:bg-slate-200/80 rounded-lg select-none p-3"
                                >
                                    <svelte:component
                                        this={action.icon}
                                        class="opacity-80"
                                    />
                                    <span class="font-medium opacity-80">{action.text}</span>
                                </button>
                            </li>
                        {/each}
                    </ul>
                </div>
            {/if}
        </OrderItem>
    </div>
</aside>

<ConfirmDialog
    bind:visible={showConfirmPurchaseDialog}
    on:confirm={purchaseBattery}
    loading={isPurchasing}
>
    <p class="text-lg font-medium mb-6">{$t("history.cancel_rental_confirmation")}</p>
    <p class="mb-3">{$t("history.purchase_explanation")}</p>
    <p>{$t("history.you_will_be_charged")} {formatCurrency(BATTERY_PURCHASE_PRICE)}.</p>
</ConfirmDialog>

<ErrorDialog bind:visible={showErrorDialog} />
