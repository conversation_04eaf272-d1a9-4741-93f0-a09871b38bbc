<script lang="ts">
    import { type PaymentMethod } from "@stripe/stripe-js";

    import Badge from "$components/ui/Badge.svelte";
    import Card from "$components/ui/Card.svelte";
    import { t } from "$lib/i18n";
    import { CREDIT_CARD_IMAGES, CREDIT_CARD_NAMES, DEFAULT_CREDIT_CARD_IMAGE } from "$lib/utils/payment-methods";
    import { cn } from "$lib/utils/style";

    export let card: PaymentMethod.Card;
    export let isDefault = false;

    $: isExpired = new Date(card.exp_year, card.exp_month) < new Date();
</script>

<Card
    class={cn("relative flex items-center gap-3 border-2 border-white transition-all p-3", {
        "border-primary-500": isDefault,
    }, $$restProps.class)}
>
    <svelte:component
        this={CREDIT_CARD_IMAGES[card.brand] || DEFAULT_CREDIT_CARD_IMAGE}
        class="w-8 h-8 shrink-0"
    />
    <div class="truncate">
        <p class="font-medium capitalize">{CREDIT_CARD_NAMES[card.brand] || card.brand}</p>
        {#if isExpired}
            <span class="text-red-500 text-xs font-medium">{$t("payment_methods.card_expired")}</span>
        {:else}
            <div class="flex items-center gap-3 opacity-80 font-bold">
                <div class="flex items-center gap-1.5 shrink-0">
                    <p>•••• •••• ••••</p>
                    <p class="text-sm font-card">{card.last4}</p>
                </div>
                <p class="text-sm font-card truncate">
                    {card.exp_month}/{String(card.exp_year).slice(-2)}
                </p>
            </div>
        {/if}
    </div>
    {#if isDefault}
        <Badge class="shrink-0 ml-auto">
            <span>{$t("payment_methods.default")}</span>
        </Badge>
    {/if}
</Card>

<style>
    .font-card {
        font-family: 'Courier New', serif;
    }
</style>
