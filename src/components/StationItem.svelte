<script lang="ts">
    import { derived } from "svelte/store";

    import FavoriteButton from "$components/ui/FavoriteButton.svelte";
    import StationIcon from "$lib/assets/images/station/full.svg";
    import { locale } from "$lib/i18n";
    import { latitude, longitude } from "$lib/stores/user";
    import { formatDistance, getDistanceFromLatLngInMeters } from "$lib/utils/distance";
    import { addFavoriteStation, removeFavoriteStation } from "$lib/utils/favorites";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    export let station: Tables<"station_locations">;
    export let isFavorite: boolean;

    let isLoadingFavorite = false;

    $: stationAddress = [station.city, station.state, station.country].filter(Boolean).join(", ");

    const formattedDistance = derived([latitude, longitude], ([lat, lng]) => {
        return formatDistance(
            getDistanceFromLatLngInMeters(station.latitude ?? 0, station.longitude ?? 0, lat, lng),
            {
                language: $locale,
                unitDisplay: "short",
            },
        );
    });

    const toggleFavoriteStation = async (like: boolean) => {
        try {
            isLoadingFavorite = true;
            like
                ? await addFavoriteStation(station.station_id)
                : await removeFavoriteStation(station.station_id);
            isFavorite = like;
        } catch (error) {
            console.error(error);
        } finally {
            isLoadingFavorite = false;
        }
    };
</script>

<li class="border-t last-of-type:border-b">
    <a
        href={`${Route.Home}?station_id=${station.station_id}`}
        class="flex items-center gap-3 active:bg-slate-50 md:hover:bg-slate-50 px-3 py-2"
    >
        <div class="flex flex-col items-center overflow-hidden w-14">
            <div class="bg-primary-500 w-7 aspect-square rounded-full flex items-center justify-center">
                <img
                    src={StationIcon}
                    alt="station"
                    width={20}
                    height={20}
                />
            </div>
            {#if $formattedDistance}
                <p class="text-xs text-center opacity-80 truncate w-14 mt-1">{$formattedDistance}</p>
            {/if}
        </div>
        <div class="flex-1 overflow-hidden">
            <p class="font-medium truncate">{station.address}</p>
            <p class="text-sm font-light line-clamp-2">{stationAddress}</p>
        </div>
        <div>
            <FavoriteButton
                bind:value={isFavorite}
                on:like={() => toggleFavoriteStation(true)}
                on:unlike={() => toggleFavoriteStation(false)}
                disabled={isLoadingFavorite}
                class="text-2xl pr-2"
            />
        </div>
    </a>
</li>
