<script lang="ts">
    import LineMdConfirmCircle from "~icons/line-md/confirm-circle";
    import Button from "$components/ui/Button.svelte";
    import Dialog from "$components/ui/Dialog.svelte";
    import { t } from "$lib/i18n";
    import { cn } from "$lib/utils/style";
    import { OrderType } from "$types/orders";

    export let visible: boolean;
    export let type: OrderType;
    export let slotId: number | undefined;
</script>

<Dialog
    bind:visible={visible}
    contentClass="p-0"
>
    <div class="flex-1 flex flex-col text-center text-white px-3 py-6">
        <div class="text-4xl font-extrabold uppercase">
            <p>{$t("home.order")}</p>
            <p>{$t("home.confirmed")}</p>
        </div>
        <div class="flex justify-center my-6">
            <LineMdConfirmCircle class="w-full h-full max-w-[20dvh]" />
        </div>

        {#if slotId}
            <p class="text-2xl font-medium">{$t("home.your_battery_is_available_at")}</p>

            <div class="grid grid-cols-10 place-items-center border-4 rounded-2xl perspective w-full max-w-[400px] mx-auto p-3 mt-3">
                {#each Array.from({ length: 10 }, (_, i) => i + 1) as slot}
                    <div
                        class={cn("w-4 h-14 border-2 border-white/50 col-span-1 rounded transition-all", {
                            "pulse": slot === slotId,
                        })}
                    />
                {/each}
            </div>

            <div class="flex justify-center my-3">
                <div class="bg-white text-primary-500 rounded-2xl py-2 px-3">
                    <p class="text-xl font-bold uppercase">{$t("home.slot")} {slotId}</p>
                </div>
            </div>
        {/if}

        <div class="mt-auto mb-8">
            <p class="text-xl font-medium mb-3">
                {#if type === OrderType.Rental}
                    {$t("home.grab_your_battery")}
                {:else}
                    {$t("home.thank_you_purchase")}
                {/if}
            </p>
        </div>

        <Button
            on:click={() => visible = false}
            class="w-full bg-white active:bg-slate-100 text-primary-500 text-2xl"
        >
            {$t("home.understood")}
        </Button>
    </div>
</Dialog>

<style>
    @keyframes pulse {
        0%, 100% {
            background: rgba(255, 255, 255, 1);
        }
        50% {
            background: rgba(255, 255, 255, 0.5);
        }
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    .perspective {
        transform: perspective(2000px) rotateX(40deg);
    }
</style>
