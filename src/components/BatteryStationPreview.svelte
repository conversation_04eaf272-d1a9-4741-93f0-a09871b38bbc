<script lang="ts">
    import Stripe from "stripe";
    import { onMount } from "svelte";
    import { derived } from "svelte/store";
    import { fly } from "svelte/transition";

    import MaterialSymbolsFootprint from "~icons/material-symbols/footprint";
    import SolarTicketSaleBold from "~icons/solar/ticket-sale-bold";
    import Button from "$components/ui/Button.svelte";
    import Card from "$components/ui/Card.svelte";
    import FavoriteButton from "$components/ui/FavoriteButton.svelte";
    import ProgressSegments from "$components/ui/ProgressSegments.svelte";
    import SkeletonTextLoader from "$components/ui/SkeletonTextLoader.svelte";
    import { order as activeOrder } from "$lib/composables/useActiveOrder";
    import { locale, t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { isStationPreviewVisible, isStationRentPreviewVisible, station } from "$lib/stores/stations";
    import { latitude, longitude } from "$lib/stores/user";
    import { formatDistance, getDistanceFromLatLngInMeters } from "$lib/utils/distance";
    import { addFavoriteStation, loadFavoriteStations, removeFavoriteStation } from "$lib/utils/favorites";
    import { getHourlyPriceCents } from "$lib/utils/pricing";
    import { getPromoLabel, loadSavedPromoCode } from "$lib/utils/promo-codes.client";
    import type { Tables } from "$types/supabase";

    let stationId = "";
    let stationAddress: Tables<"station_locations"> | null = null;
    let isLoadingAddress = true;
    let isFavorite = false;
    let isLoadingFavorite = true;

    const formattedDistance = derived([station, latitude, longitude], ([s, lat, lng]) => {
        return formatDistance(
            getDistanceFromLatLngInMeters(s.latitude ?? 0, s.longitude ?? 0, lat, lng),
            {
                language: $locale,
                unitDisplay: "long",
            },
        );
    });
    const totalSlotsAmount = derived(station, (value) => value.total_batteries + value.empty_slots);
    const hasAvailableBattery = derived(station, (value) => value.usable_batteries > 0);
    const hourlyPrice = derived(station, (value) => getHourlyPriceCents(value) / 100);

    const openRentPreview = () => {
        isStationPreviewVisible.set(false);
        isStationRentPreviewVisible.set(true);
    };

    const loadStationAddress = async (_station: Tables<"stations">) => {
        isLoadingAddress = true;

        const { error, data } = await supabase
            .from("station_locations")
            .select()
            .eq("station_id", _station.id)
            .single();

        if (error) {
            console.error(error);
        }

        stationAddress = data;
        isLoadingAddress = false;
    };

    const loadStationAvailability = async (_station: Tables<"stations">) => {
        const { error, data } = await supabase.from("stations")
            .select("usable_batteries, empty_slots, total_batteries")
            .eq("id", _station.id)
            .single();

        if (error) {
            console.error(error);
            return;
        }

        station.update((value) => {
            return {
                ...value,
                usable_batteries: data?.usable_batteries ?? 0,
                empty_slots: data?.empty_slots ?? 0,
                total_batteries: data?.total_batteries ?? 0,
            };
        });
    };

    const loadFavoriteStation = async (stationId: string) => {
        try {
            isLoadingFavorite = true;
            const favoriteStations = await loadFavoriteStations([stationId]);
            isFavorite = favoriteStations[stationId];
        } catch (error) {
            console.error(error);
        } finally {
            isLoadingFavorite = false;
        }
    };

    const toggleFavoriteStation = async (like: boolean) => {
        try {
            isLoadingFavorite = true;
            like
                ? await addFavoriteStation(stationId)
                : await removeFavoriteStation(stationId);
            isFavorite = like;
        } catch (error) {
            console.error(error);
        } finally {
            isLoadingFavorite = false;
        }
    };

    let promoCode: Stripe.PromotionCode | null = null;
    $: promoLabel = promoCode ? getPromoLabel(promoCode) : "";

    const _loadSavedPromoCode = async () => {
        promoCode = await loadSavedPromoCode();
    };

    onMount(() => {
        const unsubscribe = station.subscribe(async (value) => {
            if (stationId === value.id) {
                return;
            }

            stationId = value.id;

            await Promise.all([
                loadStationAddress(value),
                loadStationAvailability(value),
                loadFavoriteStation(value.id),
            ]);
        });

        _loadSavedPromoCode();

        return () => {
            unsubscribe();
        };
    });
</script>

<aside
    transition:fly={{ duration: 300, y: 300 }}
    class="fixed z-[1] bottom-0 left-0 w-full pointer-events-none"
>
    <div class="grid grid-cols-1 gap-3 w-full max-w-app mx-auto px-3 pb-6">
        <Card class="pointer-events-auto">
            <div class="flex items-center justify-between gap-3">
                <div>
                    <p>
                        <span class="text-xl font-bold">
                            ${$hourlyPrice}<span class="font-normal opacity-50 text-xs">/{$t("home.hour")}</span>
                        </span>
                    </p>
                    {#if promoCode}
                        <p class="text-xs font-medium text-primary-500 flex items-center gap-1">
                            <SolarTicketSaleBold class="size-4" />
                            <span>{promoLabel} {$t("home.on_total")}</span>
                        </p>
                    {/if}
                    <!--                    <p class="text-xs font-medium text-primary-500">{$t("common.first_hour_free")}</p>-->
                </div>
                <div class="text-right truncate max-w-60">
                    <SkeletonTextLoader
                        loading={isLoadingAddress}
                        class="text-sm"
                    >
                        <p class="truncate">{stationAddress?.address ?? "‎"}</p>
                    </SkeletonTextLoader>
                    <SkeletonTextLoader
                        loading={isLoadingAddress}
                        length={5}
                        class="opacity-50 text-xs"
                    >
                        <p class="truncate">{stationAddress?.city ?? "‎"}</p>
                    </SkeletonTextLoader>
                </div>
            </div>

            <hr class="my-3" />

            <div class="mb-3">
                <p class="text-xs opacity-50">{$t("home.available_batteries")}</p>
                <div class="flex items-center gap-3">
                    <ProgressSegments
                        max={$totalSlotsAmount}
                        progress={$station.usable_batteries}
                        class="flex-1"
                    />
                    <p class="text-lg font-bold">
                        {$station.usable_batteries}<span class="opacity-50 font-normal text-xs">/{$totalSlotsAmount}</span>
                    </p>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center gap-1 opacity-30 text-xs">
                    <MaterialSymbolsFootprint
                        width="18"
                        height="18"
                    />
                    <p>{$t("home.away", { value: $formattedDistance })}</p>
                </div>
                <div class="flex items-center">
                    <FavoriteButton
                        bind:value={isFavorite}
                        on:like={() => toggleFavoriteStation(true)}
                        on:unlike={() => toggleFavoriteStation(false)}
                        disabled={isLoadingFavorite}
                    />
                </div>
            </div>
        </Card>

        {#if $activeOrder}
            <Button class="w-full bg-white active:bg-white text-black/50 pointer-events-auto cursor-default">
                {$t("home.you_have_active_rental")}
            </Button>
        {:else if $hasAvailableBattery}
            <Button
                on:click={openRentPreview}
                class="font-theme uppercase text-3xl w-full pointer-events-auto"
            >
                {$t("home.get_battery")}
            </Button>
        {:else}
            <Button class="font-theme uppercase text-3xl bg-white active:bg-white text-papaya cursor-default pointer-events-auto">
                {$t("home.no_batteries_available")}
            </Button>
        {/if}
    </div>
</aside>
