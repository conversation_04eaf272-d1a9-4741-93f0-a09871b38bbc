import type { User } from "@supabase/supabase-js";
import dayjs from "dayjs";
import Strip<PERSON> from "stripe";

import { stripe } from "$lib/services/stripe";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import type { Tables } from "$types/supabase";

export const loadPromoCode = async (code: string) => {
    try {
        const { data: [promoCode] } = await stripe.promotionCodes.list({
            code,
            active: true,
            limit: 1,
        });

        return promoCode;
    } catch {
        return null;
    }
};

export const isPromoCodeValid = (promoCode: Stripe.PromotionCode) => {
    return (promoCode.coupon.redeem_by ?? 0) > Date.now() / 1000;
};

export const isCouponValid = async (coupon: Tables<"coupons">, user: User) => {
    if (dayjs(coupon.expires_at).isBefore(dayjs())) {
        return false;
    }

    if (coupon.usage_limit_per_user) {
        const { count, error } = await supabaseAdmin
            .from("orders")
            .select("*", { count: "exact", head: true })
            .ilike("promo_code", coupon.code)
            .eq("user_id", user.id);

        if (error) return false;

        if (count !== null && coupon.usage_limit_per_user <= count) {
            return false;
        }
    }

    return true;
};

export const applyPromoCode = async (code: string, amountCents: number) => {
    const promoCode = await loadPromoCode(code);

    if (!promoCode) {
        return amountCents;
    }

    if (promoCode.coupon.amount_off) {
        return amountCents - promoCode.coupon.amount_off;
    }

    if (promoCode.coupon.percent_off) {
        return amountCents * (1 - promoCode.coupon.percent_off / 100);
    }

    return amountCents;
};
