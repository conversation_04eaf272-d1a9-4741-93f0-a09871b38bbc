import { DEFAULT_LANGUAGE } from "$lib/i18n";

export const getDistanceFromLatLngInMeters = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);  // deg2rad below
    const dLon = deg2rad(lon2 - lon1);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2)
    ;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c * 1000; // Distance in meters
};

const deg2rad = (deg: number) => {
    return deg * (Math.PI / 180);
};

export const formatDistance = (
    distanceInMeters: number,
    options: {
        language?: string,
        unitDisplay?: "short" | "long",
    } = {},
) => {
    const language = options.language ?? DEFAULT_LANGUAGE;
    const unitDisplay = options.unitDisplay ?? "short";

    if (distanceInMeters >= 1000) {
        return new Intl.NumberFormat(language, { // Make users locale dynamic
            style: "unit",
            unit: "kilometer",
            unitDisplay,
            maximumFractionDigits: 1,
        }).format(distanceInMeters / 1000);
    } else {
        return new Intl.NumberFormat(language, { // Make users locale dynamic
            style: "unit",
            unit: "meter",
            unitDisplay,
            maximumFractionDigits: 0,
        }).format(distanceInMeters);
    }
};
