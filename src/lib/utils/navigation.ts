import { goto } from "$app/navigation";
import type { Route } from "$types/routes";

type Options = {
    query?: Record<string, unknown>
    replaceState?: boolean
    noScroll?: boolean
    keepFocus?: boolean
    invalidateAll?: boolean
    state?: App.PageState
};

export const navigateTo = async (path: Route, options: Options = {}) => {
    const params = options.query ? Object.fromEntries(Object.entries(options.query).map(([key, value]) => [key, String(value)])) : {};
    const search = new URLSearchParams(params).toString();
    const url = search ? `${path}?${search}` : path;
    await goto(url, options);
};
