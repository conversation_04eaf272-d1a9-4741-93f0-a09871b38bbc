import { get } from "svelte/store";

import { supabase } from "$lib/services/supabase";
import { user } from "$lib/stores/user";

export const loadFavoriteStations = async (stationIds: string[]) => {
    const _user = get(user);

    if (!_user) {
        throw new Error("User not found");
    }

    const { data, error } = await supabase
        .from("favorite_stations")
        .select("station_id")
        .in("station_id", stationIds)
        .eq("user_id", _user.id);

    if (error) {
        throw new Error(error.message);
    }

    return stationIds.reduce((acc, curr) => {
        acc[curr] = !!data.find((s) => s.station_id === curr);
        return acc;
    }, {} as Record<string, boolean>);
};

export const addFavoriteStation = async (stationId: string) => {
    const _user = get(user);

    if (!_user) {
        throw new Error("User not found");
    }

    await supabase
        .from("favorite_stations")
        .insert({
            user_id: _user.id,
            station_id: stationId,
        });
};

export const removeFavoriteStation = async (stationId: string) => {
    const _user = get(user);

    if (!_user) {
        throw new Error("User not found");
    }

    await supabase
        .from("favorite_stations")
        .delete()
        .eq("user_id", _user.id)
        .eq("station_id", stationId);
};
