import { get } from "svelte/store";

import { locale } from "$lib/i18n";
import type { Tables } from "$types/supabase";

// TODO: All of those should be a configuration variable
export const DEFAULT_BATTERY_HOURLY_RENTAL_PRICE = 2; // CAD
export const BATTERY_PURCHASE_PRICE = 50; // CAD
export const STRIPE_MINIMUM_CHARGE = 0.70;
export const MAX_CHARGEABLE_HOURS_PER_DAY = 8;
export const FREE_MINUTES = 5;
export const INITIAL_POS_AMOUNT = 2; // CAD
export const MAX_DAYS_TO_KEEP = 2;

export const formatCentsToDollars = (cents: number, currency = "CAD") => {
    return (cents / 100).toLocaleString(get(locale), { style: "currency", currency });
};

export const formatCurrency = (amount: number, currency = "CAD") => {
    return amount.toLocaleString(get(locale), { style: "currency", currency, maximumFractionDigits: 0 });
};

export const computeRentalPriceCents = (startedAt: string, hourlyPriceCents: number) => {
    if (hourlyPriceCents === 0) {
        return 0;
    }

    const elapsedMinutes = (new Date().getTime() - new Date(startedAt).getTime()) / 60000;

    // Free for the first minutes
    if (elapsedMinutes <= FREE_MINUTES) {
        return 0;
    }

    const hoursToCharge = Math.ceil((elapsedMinutes - FREE_MINUTES) / 60);
    const daysInHours = MAX_DAYS_TO_KEEP * 24;

    if (hoursToCharge > daysInHours) {
        return BATTERY_PURCHASE_PRICE;
    }

    const totalHoursToCharge = Math.floor(hoursToCharge / 24) * MAX_CHARGEABLE_HOURS_PER_DAY + Math.min(hoursToCharge % 24, MAX_CHARGEABLE_HOURS_PER_DAY);

    return Math.max(
        hourlyPriceCents * totalHoursToCharge,
        STRIPE_MINIMUM_CHARGE * 100,
    );
};

export const computeElapsedTimeMin = (chargedAmount: number, hourlyPriceCents: number) => {
    // If the charged amount is 0, the elapsed time is within the free minutes
    if (chargedAmount === 0) {
        return FREE_MINUTES;
    }

    // If the charged amount is equal to or greater than the battery purchase price,
    // then the elapsed time exceeds max days allowed to keep the battery
    if (chargedAmount >= BATTERY_PURCHASE_PRICE) {
        return MAX_DAYS_TO_KEEP * 24 * 60; // Days in minutes
    }

    const hourlyPrice = hourlyPriceCents / 100;

    // Calculate the total hours that were charged
    const chargedHours = Math.max(chargedAmount / hourlyPrice, STRIPE_MINIMUM_CHARGE / hourlyPrice);

    // Calculate the number of days
    const fullDays = Math.floor(chargedHours / MAX_CHARGEABLE_HOURS_PER_DAY);

    // Calculate the number of hours on the last partial day
    const remainingHours = chargedHours % MAX_CHARGEABLE_HOURS_PER_DAY;

    // Total elapsed hours
    const totalElapsedHours = fullDays * 24 + remainingHours;

    // Convert elapsed hours to minutes
    return totalElapsedHours * 60;
};

export const getHourlyPriceCents = (value: Tables<"stations"> | Tables<"orders">) => {
    return value?.hourly_price_cents ?? DEFAULT_BATTERY_HOURLY_RENTAL_PRICE * 100;
};