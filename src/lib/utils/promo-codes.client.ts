import Stripe from "stripe";

import { api } from "$lib/services/api";
import { supabase } from "$lib/services/supabase";
import { formatCurrency } from "$lib/utils/pricing";

export const PROMO_CODE_QUERY_PARAM = "code";
export const PROMO_CODE_LOCAL_STORAGE_KEY = "promo_code";

export const loadPromoCode = async (code: string) => {
    const { data } = await api.get<Stripe.PromotionCode | null>("/promo-codes", {
        params: {
            code,
        },
    });

    const { data: { session } } = await supabase.auth.getSession();

    if (!data && session) {
        await supabase.auth.updateUser({
            data: {
                current_promo_code: null,
            },
        });
    }

    return data;
};

export const loadSavedPromoCode = async () => {
    const { data } = await supabase.auth.getSession();
    const code: string | null = data.session?.user.user_metadata.current_promo_code;

    if (!code) {
        return null;
    }

    return await loadPromoCode(code);
};

export const savePromoCode = async (code: string) => {
    await supabase.auth.updateUser({
        data: {
            current_promo_code: code,
        },
    });
};

export const getPromoCodePrice = (amountCents: number, promoCode: Stripe.PromotionCode) => {
    if (promoCode.coupon.amount_off) {
        return amountCents - promoCode.coupon.amount_off;
    }

    if (promoCode.coupon.percent_off) {
        return amountCents * (1 - promoCode.coupon.percent_off / 100);
    }

    return amountCents;
};

export const getPromoLabel = (promoCode: Stripe.PromotionCode) => {
    return promoCode.coupon.amount_off ? `-${formatCurrency(promoCode.coupon.amount_off / 100)}` : `-${promoCode.coupon.percent_off}%`;
};

export const savePromoCodeToLocalStorage = (code: string) => {
    localStorage.setItem(PROMO_CODE_LOCAL_STORAGE_KEY, code);
};

export const loadPromoCodeFromLocalStorage = async () => {
    const code = localStorage.getItem(PROMO_CODE_LOCAL_STORAGE_KEY);

    if (!code) {
        return;
    }

    await savePromoCode(code);
    localStorage.removeItem(PROMO_CODE_LOCAL_STORAGE_KEY);
};
