import { SvelteComponent } from "svelte";
import type { SvelteHTMLElements } from "svelte/elements";

import F7CreditcardFill from "~icons/f7/creditcard-fill";
import Fa6BrandsCcDiscover from "~icons/fa6-brands/cc-discover";
import LogosAmexDigital from "~icons/logos/amex-digital";
import LogosJcb from "~icons/logos/jcb";
import LogosMastercard from "~icons/logos/mastercard";
import LogosUnionpay from "~icons/logos/unionpay";
import LogosVisa from "~icons/logos/visa";
import SimpleIconsDinersclub from "~icons/simple-icons/dinersclub";

export const CREDIT_CARD_IMAGES: Record<string, typeof SvelteComponent<SvelteHTMLElements["svg"]>> = {
    amex: LogosAmexDigital,
    diners: SimpleIconsDinersclub,
    discover: Fa6BrandsCcDiscover,
    jcb: LogosJcb,
    mastercard: LogosMastercard,
    unionpay: LogosUnionpay,
    visa: LogosVisa,
};

export const CREDIT_CARD_NAMES: Record<string, string> = {
    amex: "American Express",
    diners: "Diners Club",
    jcb: "JCB",
    mastercard: "Mastercard",
    visa: "Visa",
};

export const DEFAULT_CREDIT_CARD_IMAGE = F7CreditcardFill;
