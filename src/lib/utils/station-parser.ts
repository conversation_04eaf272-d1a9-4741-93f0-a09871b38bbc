import { hexadecimalToBinary } from "$lib/utils/binaries";

export const parseStationSlot = (data: string) => {
    const attributes = data.split(",");

    const statusFlag = attributes[1];
    const [,,,, currentCharging] = hexadecimalToBinary(statusFlag);

    return {
        slot_id: Number(attributes[0].replace(/S/g, "")),
        voltage: Number(attributes[2].replace(/mV/g, "")),
        amperage: Number(attributes[3].replace(/mA/g, "")),
        power: Number(attributes[4]),
        is_charging: currentCharging === "1",
    };
};

export const parseStationSlots = (data: string) => {
    return data.split("|").filter((d) => d.length).map(parseStationSlot);
};
