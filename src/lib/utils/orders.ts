import type { User } from "@supabase/supabase-js";
import Strip<PERSON> from "stripe";

import { stripe } from "$lib/services/stripe";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { applyTaxes, capturePaymentIntent, generateOrderId, getStripeCustomer } from "$lib/utils/payments";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import { applyPromoCode } from "$lib/utils/promo-codes.server";
import { OrderStatus, OrderType } from "$types/orders";
import type { Tables } from "$types/supabase";

type CreateOrderParams = {
		user: User;
		paymentMethodId: string;
		orderType: OrderType;
		stationId: string;
		cable: number;
		promoCode?: string;
		returnUrl: string;
};

type CancelOrderParams = {
		paymentIntentId: string,
		carkuOrderId: string,
		reason: Stripe.PaymentIntentCancelParams.CancellationReason
};

type CaptureOrderParams = {
		order: Tables<"orders">;
		priceCents: number;
		orderStatus?: OrderStatus;
}

export const createOrder = async ({ user, paymentMethodId, orderType, stationId, cable, promoCode, returnUrl }: CreateOrderParams) => {
	const { id: stripeCustomerId } = await getStripeCustomer(user);
	const customer = await stripe.customers.retrieve(stripeCustomerId);

	if (!customer || customer.deleted) {
		throw new Error(`Failed to retrieve customer ${stripeCustomerId}`);
	}

	const { error: stationError, data: station } = await supabaseAdmin
		.from("stations")
		.select("id, hourly_price_cents")
		.eq("id", stationId)
		.single();

	if (stationError) {
		console.error("Error fetching station when creating order", stationError, stationId);
		throw new Error(stationError.message);
	}

	const totalPriceCents = await applyTaxes(BATTERY_PURCHASE_PRICE * 100);

	const intent = await stripe.paymentIntents.create({
		customer: stripeCustomerId,
		payment_method: paymentMethodId,
		amount: Math.round(totalPriceCents),
		currency: "cad",
		capture_method: "manual",
		setup_future_usage: "off_session",
		confirm: true,
		automatic_payment_methods: {
			enabled: true,
			allow_redirects: "always",
		},
		return_url: returnUrl,
	});

	const orderId = generateOrderId();

	const { error: orderError } = await supabaseAdmin
		.from("orders")
		.insert({
			user_id: user.id,
			station_id: stationId,
			stripe_customer_id: stripeCustomerId,
			stripe_payment_intent_id: intent.id,
			carku_order_id: orderId,
			cable_type: cable,
			type: orderType,
			status: OrderStatus.Pending,
			amount_charged_cents: 0,
			promo_code: promoCode,
			hourly_price_cents: station.hourly_price_cents,
		});

	if (orderError) {
		throw new Error(orderError.message);
	}

	return {
		intentId: intent.id,
		orderId,
	};
};

export const cancelOrder = async ({ paymentIntentId, carkuOrderId, reason }: CancelOrderParams) => {
	const { error } = await supabaseAdmin
		.from("orders")
		.update({
			status: OrderStatus.Failed,
			ended_at: new Date().toISOString(),
			amount_charged_cents: 0,
		})
		.eq("carku_order_id", carkuOrderId);

	if (paymentIntentId !== "free_order") {
		await stripe.paymentIntents.cancel(paymentIntentId, {
			cancellation_reason: reason,
		});
	}

	if (error) {
		console.error("Error cancelling order", error, paymentIntentId, carkuOrderId, reason);
		throw new Error(error.message);
	}
};

const updateOrderAfterCapture = async (orderId: string, priceCents: number, orderStatus: OrderStatus) => {
	const { error } = await supabaseAdmin
		.from("orders")
		.update({
			status: orderStatus,
			ended_at: new Date().toISOString(),
			amount_charged_cents: Math.round(priceCents),
		})
		.eq("id", orderId)
		.select()
		.single();

	if (error) {
		console.error("Error updating order after capture", error, orderId, priceCents, orderStatus);
		throw new Error(error.message);
	}
};

export const captureOrder = async ({ order, priceCents, orderStatus }: CaptureOrderParams) => {
	let totalPriceCents = priceCents;

	if (order.stripe_payment_intent_id === "free_order") {
		console.info("Free order, closing the order", {
			stripe_payment_intent_id: order.stripe_payment_intent_id,
			carku_order_id: order.carku_order_id,
			id: order.id,
		});

		totalPriceCents = 0
	}

	if (totalPriceCents > 0) {
		if (order.promo_code) {
			totalPriceCents = await applyPromoCode(order.promo_code, totalPriceCents);
		}
	}

	if (totalPriceCents > 0) {
		totalPriceCents = await applyTaxes(totalPriceCents);
	}
	
	const _orderStatus = orderStatus || OrderStatus.Completed;

	if (totalPriceCents === 0) {
		await updateOrderAfterCapture(order.id, 0, OrderStatus.Completed);
		
		await stripe.paymentIntents.cancel(order.stripe_payment_intent_id, {
			cancellation_reason: "abandoned",
		});
		
		return;
	}

	let paymentIntent = await stripe.paymentIntents.retrieve(order.stripe_payment_intent_id);

	if (paymentIntent.amount_capturable < totalPriceCents) {
		await stripe.paymentIntents.incrementAuthorization(paymentIntent.id, {
			amount: totalPriceCents,
		});
	}

	paymentIntent = await capturePaymentIntent(order.stripe_payment_intent_id, totalPriceCents);
	await updateOrderAfterCapture(order.id, paymentIntent.amount_received, _orderStatus);
};
