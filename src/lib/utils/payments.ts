import type { User } from "@supabase/supabase-js";
import <PERSON><PERSON> from "stripe";
import { v4 as uuidv4 } from "uuid";

import { stripe } from "$lib/services/stripe";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { getTaxRate } from "$lib/services/taxes";
import { computeRentalPriceCents } from "$lib/utils/pricing";

const createStripeCustomer = async (user: User) => {
    const customer = await stripe.customers.create({
        phone: user.phone,
    });

    await supabaseAdmin.auth.admin.updateUserById(user.id, {
        user_metadata: {
            stripe_customer_id: customer.id,
        },
    });

    return customer;
};

export const getStripeCustomer = async (user: User) => {
    if (!user.user_metadata.stripe_customer_id) {
        return createStripeCustomer(user);
    }

    let customer: Stripe.Customer;

    try {
        const _customer = await stripe.customers.retrieve(user.user_metadata.stripe_customer_id);

        if (_customer.deleted) {
            return createStripeCustomer(user);
        }

        customer = _customer;
    } catch {
        return createStripeCustomer(user);
    }

    return customer;
};

export const generateOrderId = () => {
    // The order id needs to be 31 characters long maximum (Carku limitation), we'll make it 30 characters long to play safe
    // The original uuid is 36 characters long, so we remove the dashes to make it 32 characters long
    return uuidv4().replace(/-/g, "").slice(0, 30);
};

export const applyTaxes = async (price: number) => {
    if (price === 0) {
        return 0;
    }

    const taxRate = await getTaxRate();
    return price * (1 + taxRate);
};

export const capturePaymentIntent = async (paymentIntentId: string, amountCents: number) => {
    return await stripe.paymentIntents.capture(paymentIntentId, {
        amount_to_capture: Math.round(amountCents),
    });
};

export const computeOrderPriceCents = (startedAt: string, hourlyPriceCents: number) => {
    const priceCents = computeRentalPriceCents(startedAt, hourlyPriceCents);

    if (priceCents === 0) {
        return 0;
    }

    return priceCents;
};
