import { get } from "svelte/store";

import { goto } from "$app/navigation";
import { supabase } from "$lib/services/supabase";
import { user } from "$lib/stores/user";
import { OrderEvent, OrderStatus } from "$types/orders";
import { Route } from "$types/routes";
import type { Tables } from "$types/supabase";

const TIMEOUT = 5000;

export const getBatteryStationSlotId = async (): Promise<number> => {
    const $user = get(user);

    if (!$user) {
        await goto(Route.Login);
        throw new Error("User is not authenticated");
    }

    const channel = supabase.channel($user.id);

    return new Promise((resolve, reject) => {
        const timeout = setTimeout(async () => {
            const { error, data: order } = await supabase
                .from("orders")
                .select()
                .eq("user_id", $user.id)
                .eq("status", OrderStatus.Ongoing)
                .maybeSingle();

            if (error) {
                console.error(error);
                return reject();
            }

            if (!order || !order.station_slot_id) {
                return reject();
            }

            resolve(order.station_slot_id);
        }, TIMEOUT);

        channel.on("broadcast", { event: OrderEvent.BatteryPopped }, async (event) => {
            const { order } = event.payload as { order: Tables<"orders"> };

            if (!order.station_slot_id) {
                return reject();
            }

            clearTimeout(timeout);
            resolve(order.station_slot_id);
        });
    });
};
