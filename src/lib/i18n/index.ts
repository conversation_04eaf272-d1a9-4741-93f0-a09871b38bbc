import i18n, { type Config } from "sveltekit-i18n";

import { Route } from "$types/routes";

import lang from "./lang.json";

export const DEFAULT_LANGUAGE = "fr";
export const LANGUAGE_COOKIE_NAME = "lang";

const registerLocalization = (key: string, routes?: Array<Route | RegExp>) => {
    return ["fr", "en"].map((lang) => ({
        locale: lang,
        key,
        routes,
        loader: async () => (await import(`./${lang}/${key}.json`)).default,
    }));
};

const withSubRoutes = (route: Route) => {
    return new RegExp(`^${route}(?:/[^/]+)?/?$`);
};

const config: Config<Record<string, unknown>> = ({
    translations: {
        en: { lang },
        fr: { lang },
    },
    loaders: [
        ...registerLocalization("common"),
        ...registerLocalization("login", [<PERSON>.Login, Route.LoginOTP]),
        ...registerLocalization("home", [Route.Home]),
        ...registerLocalization("search", [Route.Search]),
        ...registerLocalization("settings", [Route.Settings]),
        ...registerLocalization("onboarding", [Route.OnBoarding]),
        ...registerLocalization("history", [Route.History, Route.HistoryCompleted, Route.Home]),
        ...registerLocalization("payment_methods", [withSubRoutes(Route.PaymentMethods), Route.PaymentMethodsCreate, Route.Home]),
        ...registerLocalization("report", [Route.Report, Route.ReportEmail]),
        ...registerLocalization("faq", [Route.FAQ]),
        ...registerLocalization("station_scan", [Route.StationScan]),
        ...registerLocalization("favorites", [Route.Favorites]),
        ...registerLocalization("coupons", [Route.Coupons]),
    ],
});

export const {
    t,
    locale,
    locales,
    loading,
    translations,
    loadTranslations,
    addTranslations,
    setLocale,
    setRoute,
} = new i18n(config);
