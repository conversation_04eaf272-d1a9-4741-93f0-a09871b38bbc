import { ofetch } from "ofetch";

import { env } from "$env/dynamic/private";
import { DEFAULT_LANGUAGE } from "$lib/i18n";
import type { Address, PlaceDetails } from "$types/maps";

type AutocompleteSuggestion = {
    placePrediction: {
        placeId: string
        text: {
            text: string
        }
        structuredFormat: {
            mainText: {
                text: string
            }
            secondaryText: {
                text: string
            }
        }
        types: string[]
        distanceMeters: number
    }
}

type AutocompleteResponse = {
    suggestions: AutocompleteSuggestion[]
}

type PlaceDetailsResponse = {
    result: {
        formatted_address: string
        geometry: {
            location: {
                lat: number
                lng: number
            }
        }
    }
    status: "OK" | "ZERO_RESULTS"
}

const mapsApi = ofetch.create({
    baseURL: "https://maps.googleapis.com/maps/api",
    params: {
        key: env.GOOGLE_MAPS_API_KEY,
    },
});

const placesApi = ofetch.create({
    baseURL: "https://places.googleapis.com/v1/",
    params: {
        key: env.GOOGLE_MAPS_API_KEY,
    },
});

export const autocompleteAddress = async (
    {
        input,
        latitude,
        longitude,
        language = DEFAULT_LANGUAGE,
    } : {
        input: string
        latitude: number
        longitude: number
        language?: string
    },
) : Promise<Address[]> => {
    // https://developers.google.com/maps/documentation/places/web-service/place-autocomplete?hl=fr
    const data = await placesApi<AutocompleteResponse>("/places:autocomplete", {
        method: "POST",
        body: {
            input,
            origin: {
                latitude,
                longitude,
            },
            locationBias: {
                circle: {
                    center: {
                        latitude,
                        longitude,
                    },
                    radius: 20000, // 20km
                },
            },
            languageCode: language,
        },
    });

    if (!data.suggestions) {
        return [];
    }

    return data.suggestions.map((suggestion) => ({
        text: suggestion.placePrediction.structuredFormat.mainText.text,
        fulltext: suggestion.placePrediction.text.text,
        distance_meters: suggestion.placePrediction.distanceMeters,
        place_id: suggestion.placePrediction.placeId,
    }));
};

export const getPlaceDetails = async (placeId: string, language = DEFAULT_LANGUAGE) : Promise<PlaceDetails> => {
    // https://developers.google.com/maps/documentation/places/web-service/details?hl=fr
    const data = await mapsApi<PlaceDetailsResponse>("/place/details/json", {
        method: "GET",
        params: {
            place_id: placeId,
            language,
        },
    });

    return {
        address: data.result.formatted_address,
        latitude: data.result.geometry.location.lat,
        longitude: data.result.geometry.location.lng,
    };
};
