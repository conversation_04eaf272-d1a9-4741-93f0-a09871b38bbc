import axios from "axios";

import { goto } from "$app/navigation";
import { supabase } from "$lib/services/supabase";
import { Route } from "$types/routes";

export const api = axios.create({
    baseURL: "/api",
});

api.interceptors.response.use(
    (response) => response,
    async (error) => {
        const errorStatus = error.response?.status;

        if (errorStatus === 401) {
            await supabase.auth.signOut();
            await goto(Route.Login);
        }

        return Promise.reject(error);
    },
);
