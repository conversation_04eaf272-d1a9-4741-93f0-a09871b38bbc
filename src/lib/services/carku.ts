import { ofetch } from "ofetch";

import { env } from "$env/dynamic/private";
import type { BatteryStationAction, BatteryStationResponse } from "$types/carku";

const api = ofetch.create({
    baseURL: "https://vgnapi.minimgr.cn/api",
    headers: {
        "content-type": "application/x-www-form-urlencoded",
    },
});

export const callBatteryStation = async (action: BatteryStationAction, stationId: string, data: Record<string, unknown> = {}) => {
    const { code, msg } = await api<BatteryStationResponse>("/index.php", {
        method: "POST",
        body: {
            AppID: env.CARKU_APP_ID,
            SecretKey: env.CARKU_SECRET_KEY,
            did: stationId,
            ...data,
        },
        query: {
            mod: "controllers",
            act: "mini_api_v2",
            opt: action,
        },
    });

    if (code > 0) {
        throw new Error(`${code}: ${msg}`);
    }

    return {
        code,
        msg,
    };
};
