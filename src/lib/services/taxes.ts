// import { ofetch } from "ofetch";

// const api = ofetch.create({
// 		baseURL: "https://api.salestaxapi.ca/v2",
// });

type Response = {
		start: string;
		type: string;
		pst: number;
		hst: number;
		gst: number;
		applicable: number;
		source: string;
		updated_at: string;
		incoming_changes: boolean;
};

export const getTaxRate = async () => {
	const response = {
		"start":"2013-01-01 00:00:00",
		"type":"gst,pst",
		"gst":0.05,
		"pst":0.09975,
		"applicable":0.14975,
		"source":"Wikipedia (https:\/\/en.wikipedia.org\/wiki\/Sales_taxes_in_Canada), accessed May 31 2019.",
		"updated_at":"2019-06-01 14:39:07",
		"incoming_changes":false
	} as Response;


		// const response = await api<Response>("/province/qc", {
		//     method: "GET",
		//     retry: 3,
		// });

		return response.applicable;
};
