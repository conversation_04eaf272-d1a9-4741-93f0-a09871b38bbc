import { ofetch } from "ofetch";

import { env } from "$env/dynamic/private";

const accountSid = env.TWILIO_ACCOUNT_SID;
const authToken = env.TWILIO_AUTH_TOKEN;

type Params = {
    to: string;
    message: string;
};

export const sendSMS = async ({ to, message }: Params) => {
    const body = new URLSearchParams({
        Body: message,
        From: "+***********",
        To: to,
    });

    return await ofetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
        method: "POST",
        body,
        headers: {
            Authorization: `Basic ${btoa(`${accountSid}:${authToken}`)}`,
            "Content-Type": "application/x-www-form-urlencoded",
        },
    });
};

export const greetings = `
[Hiko Technologie] 🚀🔋

Merci d’avoir essayé Hiko ! En tant que startup, on veut améliorer notre service, et votre avis compte énormément.

Répondez à notre sondage (2 min) et on vous offre 2h de recharge gratuite sur votre prochaine utilisation ! 🎁⚡

👉 https://form.jotform.com/*************** 👈

Merci et à bientôt sur une station Hiko ! 😊
`.trim();

export const usersWithoutOrders = `
[Hiko Technologie] 🚀🔋

Nous avons remarqué que vous vous êtes inscrit(e) à notre service, mais que vous n'avez pas encore loué de batterie. En tant que startup, nous souhaitons comprendre les éventuels obstacles ou questions que vous pourriez avoir.

Pourriez-vous prendre 2 minutes pour répondre à notre sondage ? En échange, nous vous offrons 2 heures de recharge gratuite lors de votre première utilisation ! 🎁⚡

👉 https://form.jotform.com/*************** 👈

Merci pour votre aide précieuse, et à bientôt sur une station Hiko ! 😊
`.trim();
