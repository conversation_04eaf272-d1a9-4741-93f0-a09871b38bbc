import Stripe from "stripe";

import { stripe } from "$lib/services/stripe";

export const onTerminalLocation = async () => {
    let location: Stripe.Terminal.Location;

    const { data } = await stripe.terminal.locations.list();
    location = data[0];

    if (!location) {
        location = await stripe.terminal.locations.create({
            display_name: "HIKO",
            address: {
                country: "CA",
            },
        });
    }

    return {
        locationId: location.id,
    };
};
