import dayjs from "dayjs";

import { supabaseAdmin } from "$lib/services/supabase-admin";
import { applyTaxes, computeOrderPriceCents } from "$lib/utils/payments";
import { getHourlyPriceCents } from "$lib/utils/pricing";
import type { StripePayload } from "$types/carku";
import { OrderStatus } from "$types/orders";

const formatElapsedTime = (startDate: string) => {
    const elapsedTimeSeconds = dayjs().diff(dayjs(startDate), "second");

    const hours = Math.floor(elapsedTimeSeconds / 3600).toString().padStart(2, "0");
    const minutes = Math.floor((elapsedTimeSeconds % 3600) / 60).toString().padStart(2, "0");
    const remainingSeconds = (elapsedTimeSeconds % 60).toString().padStart(2, "0");

    return `${hours}:${minutes}:${remainingSeconds}`;
};

const formatPrice = async (startDate: string, hourlyPriceCents: number) => {
    const priceCents = computeOrderPriceCents(startDate, hourlyPriceCents);
    const totalPriceCents = await applyTaxes(priceCents);
    return (totalPriceCents / 100).toLocaleString("en", { style: "currency", currency: "CAD", maximumFractionDigits: 2 });
};

export const onOrderInformation = async (payload: StripePayload) => {
    const batteryId = payload.battery_id as string;

    const { data: order, error } = await supabaseAdmin
        .from("orders")
        .select()
        .eq("battery_id", batteryId)
        .eq("status", OrderStatus.Ongoing)
        .is("ended_at", null)
        .single();

    if (error) {
        throw new Error(error.message);
    }

    if (!order.started_at) {
        throw new Error("Order has not even started yet");
    }

    const hourlyPriceCents = getHourlyPriceCents(order);

    return {
        remark: "Merci pour votre confiance!",
        useTime: formatElapsedTime(order.started_at),
        amount: await formatPrice(order.started_at, hourlyPriceCents),
    };
};
