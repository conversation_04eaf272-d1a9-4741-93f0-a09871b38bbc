import { stripe } from "$lib/services/stripe";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { generateOrderId } from "$lib/utils/payments";
import { BatteryCableType, type StripePayload } from "$types/carku";
import { OrderStatus, OrderType } from "$types/orders";

export const onCollectPaymentMethodSuccess = async (payload: StripePayload) => {
    const intentId = payload.paymentId as string;

    const paymentIntent = await stripe.paymentIntents.retrieve(intentId);

    if (paymentIntent.status === "canceled") {
        throw new Error("Payment got cancelled");
    }

    const orderId = generateOrderId();

    const { error } = await supabaseAdmin
        .from("orders")
        .insert({
            station_id: payload.did,
            stripe_payment_intent_id: paymentIntent.id,
            carku_order_id: orderId,
            cable_type: BatteryCableType.USBTypeC,
            type: OrderType.Rental,
            status: OrderStatus.Pending,
            amount_charged_cents: 0,
        });

    if (error) {
        throw new Error(error.message);
    }

    return {
        intentId: paymentIntent.id,
        orderId,
    };
};
