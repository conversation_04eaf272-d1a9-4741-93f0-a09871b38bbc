import { stripe } from "$lib/services/stripe";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { applyTaxes } from "$lib/utils/payments";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import type { StripePayload } from "$types/carku";
import type Stripe from "stripe";

export const onCreatePaymentIntent = async (payload: StripePayload) => {
	// Check if any available battery
	const stationId = payload.did;

	const { data, error } = await supabaseAdmin
		.from("stations")
		.select()
		.eq("id", stationId)
		.gte("usable_batteries", 1);

	if (error) {
		console.error(`Error fetching station ${stationId}`, error);
		throw new Error(`Error fetching station ${stationId} : ${error.message}`);
	}

	if (data.length === 0) {
		console.error("No available batteries");
		throw new Error(`No available batteries for station ${stationId}`);
	}

	const paymentIntentRequestBody = {
		// amount: INITIAL_POS_AMOUNT * 100,
		// Preauthorize the amount of a battery purchase
		amount: Math.round(await applyTaxes(BATTERY_PURCHASE_PRICE * 100)),
		currency: "cad",
		capture_method: "manual",
		payment_method_types: ["card_present", "interac_present"],
		payment_method_options: {
			card_present: {
				request_incremental_authorization_support: true,
			},
		},
	} as Stripe.PaymentIntentCreateParams;

	try {
		const intent = await stripe.paymentIntents.create(paymentIntentRequestBody);

		return {
			secret: intent.client_secret,
		};
	} catch (error) {
		const errorMessage = `Error creating payment intent for station ${stationId}, error: ${String(error)}, request sent : ` + JSON.stringify(paymentIntentRequestBody);
		console.error(errorMessage);
		throw new Error(errorMessage);
	}
};
