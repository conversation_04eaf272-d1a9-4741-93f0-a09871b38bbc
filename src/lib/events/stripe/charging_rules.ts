import { supabaseAdmin } from "$lib/services/supabase-admin";
import { applyTaxes } from "$lib/utils/payments";
import {
    BATTERY_PURCHASE_PRICE,
    FREE_MINUTES,
    getHourlyPriceCents,
    MAX_CHARGEABLE_HOURS_PER_DAY,
} from "$lib/utils/pricing";
import type { StripePayload } from "$types/carku";

export const onChargingRules = async (payload: StripePayload) => {
    const { error, data: station } = await supabaseAdmin
        .from("stations")
        .select()
        .eq("id", payload.did)
        .single();

    if (error) {
        throw new Error(error.message);
    }

    const hourlyRentalPrice = getHourlyPriceCents(station) / 100;

		const fullPriceWithTaxes = Math.round(await applyTaxes(BATTERY_PURCHASE_PRICE * 100)) / 100;

		const formattedFullPriceWithTaxes = (fullPriceWithTaxes).toLocaleString("en", { minimumFractionDigits: 2, maximumFractionDigits: 2 });

    const messages = [
        `${FREE_MINUTES} minutes gratuites`,
        `${hourlyRentalPrice}$/h, max ${MAX_CHARGEABLE_HOURS_PER_DAY * hourlyRentalPrice}$/jour`,
        `Frais total non-retour : ${formattedFullPriceWithTaxes}$ après 3 jours`,
    ];

    return {
        message: messages.join("<br>"),
    };
};
