import { greetings, sendSMS } from "$lib/services/phone";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { cancelOrder, captureOrder } from "$lib/utils/orders";
import { computeOrderPriceCents } from "$lib/utils/payments";
import { getHourlyPriceCents } from "$lib/utils/pricing";
import type { StationEvent } from "$types/carku";
import { OrderEvent, OrderStatus, OrderType } from "$types/orders";

export type ReturnBackData = {
    ACT: StationEvent.ReturnBack
    USABLE_BATTERY: `${number}`
    EMPTY_SLOT_COUNT: `${number}`
    TOTAL: `${number}`
    USABLE_BATTERY_NEW: string
    ID: string
    STATIONID: `${number}`
    BATT_TYPE: string
    SLOT: `${number}`
    POWER: `${number}`
    VOL: string
    CUR: string
    USBTYPE: `${number}`
    SLOTSTATUS: string
    BATSTATUS: `${number}`
    CHKSUM: string
    DIDINFO: string
};

const handleSMS = async (userId: string) => {
    const { count } = await supabaseAdmin
        .from("orders")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId)
        .in("status", [OrderStatus.Completed, OrderStatus.LateCompleted]);

    if (count && count > 0) return;

    const { data: user, error: userError } = await supabaseAdmin
        .from("users")
        .select()
        .eq("id", userId)
        .single();

    if (userError || !user.phone) return;

    try {
        await sendSMS({
            to: user.phone,
            message: greetings,
        });
    } catch (error) {
        console.error("Failed to send SMS", error);
    }
};

export const onReturnBack = async (data: ReturnBackData) => {
    const { error } = await supabaseAdmin
        .from("stations")
        .update({
            usable_batteries: Number(data.USABLE_BATTERY),
            empty_slots: Number(data.EMPTY_SLOT_COUNT),
            total_batteries: Number(data.TOTAL),
        })
        .eq("id", data.DIDINFO);

    if (error) {
        console.error(error);
    }

    const { error: orderError, data: order } = await supabaseAdmin
        .from("orders")
        .select()
        .eq("battery_id", data.ID)
        .eq("status", OrderStatus.Ongoing)
        .eq("type", OrderType.Rental)
        .is("ended_at", null)
        .maybeSingle();

    if (orderError) {
        console.error(orderError);
        return;
    }

    if (!order) {
        console.error("No ongoing order found for battery", data.ID);
        return;
    }

    if (order.stripe_payment_intent_id === "free_order") {
      console.log("Free order, not calling stripe, closing the order");

      const { error } = await supabaseAdmin
          .from("orders")
          .update({
              status: OrderStatus.Completed,
              ended_at: new Date().toISOString(),
              amount_charged_cents: 0,
          })
          .eq("carku_order_id", order.carku_order_id);

      if (error) {
        console.error("Error closing free order", error);
      }

      return;
    }

    if (order.user_id) {
        const channel = supabaseAdmin
            .channel(order.user_id)
            .subscribe(async (event) => {
                if (event === "SUBSCRIBED") {
                    await channel.send({
                        type: "broadcast",
                        event: OrderEvent.BatteryReturned,
                        payload: {
                            order,
                        },
                    });
                    await channel.unsubscribe();
                }
            });
    }

    if (!order.started_at) {
        console.error("Order has no started time, closing it as failed");

        await cancelOrder({
          paymentIntentId: order.stripe_payment_intent_id,
          carkuOrderId: order.carku_order_id,
          reason: "abandoned",
        });
        				
        return;
    }

    const hourlyPriceCents = getHourlyPriceCents(order);
    const priceCents = computeOrderPriceCents(order.started_at, hourlyPriceCents);

    try {
        await captureOrder({
            order,
            priceCents,
        });

        if (!order.user_id) return;

        await handleSMS(order.user_id);
    } catch (error) {
        await cancelOrder({
            paymentIntentId: order.stripe_payment_intent_id,
            carkuOrderId: order.carku_order_id,
            reason: "abandoned",
        });
        console.error(error);
    }
};
