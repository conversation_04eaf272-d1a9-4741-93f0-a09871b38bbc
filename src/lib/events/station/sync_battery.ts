import { supabaseAdmin } from "$lib/services/supabase-admin";
import type { StationEvent } from "$types/carku";

export type SyncBatteryData = {
    ACT: StationEvent.SyncBattery,
    TOTAL: `${number}`
    CHKSUM: `${number}`
    MSG_ID: string
    STATUS: `${number}`
    DIDINFO: string
    ALARM_KEY: `${number}`
    STATIONID: `${number}`
    SLOTSTATUS: string
    USABLE_BATTERY: `${number}`
    EMPTY_SLOT_COUNT: `${number}`
    USABLE_BATTERY_NEW: string
    [key: `B${number}ID`]: string | undefined
    [key: `B${number}KEY`]: string | undefined
    [key: `B${number}POWER`]: string | undefined
    [key: `B${number}ADAPTER`]: string | undefined
    [key: `B${number}COLORID`]: string | undefined
    [key: `B${number}VOLTAGE`]: string | undefined
    [key: `B${number}ISDAMAGE`]: string | undefined
    [key: `B${number}USB_TYPE`]: string | undefined
    [key: `B${number}BATT_TYPE`]: string | undefined
    [key: `B${number}STATIONID`]: string | undefined
};

const parseNumber = (value?: string) => {
    return isNaN(Number(value)) ? 0 : Number(value);
};

export const onSyncBattery = async (data: SyncBatteryData) => {
    const stationId = data.DIDINFO;

    const slotCount = data.SLOTSTATUS // 0C-0C-0C-
        .split("-") // ["0C", "0C", "0C", ""]
        .filter((status) => status !== "") // ["0C", "0C", "0F"]
        .length; // 3

    for (let slotId = 1; slotId <= slotCount; slotId++) {
        const batteryId = data[`B${slotId}ID`];

        if (!batteryId) {
            continue;
        }

        const isSlotEmpty = batteryId === "0000000000";

        if (isSlotEmpty) {
            const { error } = await supabaseAdmin
                .from("station_slots")
                .update({
                    battery_id: null,
                    power: 0,
                    voltage: 0,
                    amperage: 0,
                    updated_at: new Date().toISOString(),
                    synced_at: new Date().toISOString(),
                })
                .eq("station_id", stationId)
                .eq("slot_id", slotId);

            if (error) {
                console.error(error);
            }
        } else {
            const { error } = await supabaseAdmin
                .from("station_slots")
                .update({
                    battery_id: batteryId,
                    power: parseNumber(data[`B${slotId}POWER`]),
                    voltage: parseNumber(data[`B${slotId}VOLTAGE`]),
                    updated_at: new Date().toISOString(),
                    synced_at: new Date().toISOString(),
                })
                .eq("station_id", stationId)
                .eq("slot_id", slotId);

            if (error) {
                console.error(error);
            }
        }
    }
};
