import { supabaseAdmin } from "$lib/services/supabase-admin";
import type { StationEvent } from "$types/carku";

export type QueryConfirmData = {
    ACT: StationEvent.QueryConfirm
    ID: string
    STATIONID: `${number}`
    COLORID: `${number}`
    POWER: `${number}`
    ISDAMAGE: `${number}`
    VOLTAGE: `${number}`
    ADAPTER: `${number}`
    BATT_TYPE: string
    SLOT: `${number}`
    CHKSUM: string
    DIDINFO: string
};

export const onQueryConfirm = async (data: QueryConfirmData) => {
    const { error } = await supabaseAdmin
        .from("station_slots")
        .update({
            voltage: Number(data.VOLTAGE),
            power: Number(data.POWER),
            updated_at: new Date().toISOString(),
        })
        .eq("station_id", data.DIDINFO)
        .eq("slot_id", Number(data.SLOT));

    if (error) {
        console.error(error);
    }
};
