import { supabaseAdmin } from "$lib/services/supabase-admin";
import type { StationEvent } from "$types/carku";

export type PopupConfirmData = {
    ACT: StationEvent.PopupConfirm
    USABLE_BATTERY: `${number}`
    EMPTY_SLOT_COUNT: `${number}`
    TOTAL: `${number}`
    USABLE_BATTERY_NEW: string
    STATUS: `${number}`
    STATIONID: `${number}`
    ID: string
    SLOT: `${number}`
    CHKSUM: string
    DIDINFO: string
};

export const onPopupConfirm = async (data: PopupConfirmData) => {
    // Update the station status
    const { error } = await supabaseAdmin
        .from("stations")
        .update({
            status: Number(data.STATUS),
            usable_batteries: Number(data.USABLE_BATTERY),
            empty_slots: Number(data.EMPTY_SLOT_COUNT),
            total_batteries: Number(data.TOTAL),
        })
        .eq("id", data.DIDINFO);

    if (error) {
        console.error(error);
        return;
    }

    // Battery was ejected successfully
    await supabaseAdmin
        .from("station_slots")
        .update({
            battery_id: null,
            voltage: 0,
            amperage: 0,
            power: 0,
            is_charging: false,
            updated_at: new Date().toISOString(),
            synced_at: new Date().toISOString(),
        })
        .eq("station_id", data.DIDINFO)
        .eq("slot_id", Number(data.SLOT));
};
