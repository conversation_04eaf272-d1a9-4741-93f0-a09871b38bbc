import { supabaseAdmin } from "$lib/services/supabase-admin";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import type { StationEvent } from "$types/carku";
import { OrderEvent, OrderStatus, OrderType } from "$types/orders";

export type RentConfirmData = {
    ACT: StationEvent.RentConfirm
    USABLE_BATTERY: `${number}`
    EMPTY_SLOT_COUNT: `${number}`
    TOTAL: `${number}`
    USABLE_BATTERY_NEW: string
    STATUS: `${number}`
    ORDERID: string
    STATIONID: `${number}`
    ID: string
    SLOT: `${number}`
    CHKSUM: string
    DIDINFO: string
};

export const onRentConfirm = async (data: RentConfirmData) => {
    const batteryId = data.ID;
    const stationId = data.DIDINFO;
    const slotId = Number(data.SLOT);

    // Create a new order
    const { error: orderError, data: order } = await supabaseAdmin
        .from("orders")
        .update({
            battery_id: batteryId,
            station_slot_id: slotId,
            status: OrderStatus.Ongoing,
            started_at: new Date().toISOString(),
        })
        .eq("carku_order_id", data.ORDERID)
        .select()
        .single();

    if (orderError) {
        console.error(orderError);
        return;
    }

    // Update the order status to "completed" if it's a purchase
    if (order.type === OrderType.Purchase) {
        const { error: orderError } = await supabaseAdmin
            .from("orders")
            .update({
                status: OrderStatus.Completed,
                amount_charged_cents: Math.round(BATTERY_PURCHASE_PRICE * 100),
                ended_at: new Date().toISOString(),
            })
            .eq("id", order.id);

        if (orderError) {
            console.error(orderError);
        }
    }

    // Notify the user that the battery has been popped
    if (order.user_id) {
        const channel = supabaseAdmin
            .channel(order.user_id)
            .subscribe(async (event) => {
                if (event === "SUBSCRIBED") {
                    await channel.send({
                        type: "broadcast",
                        event: OrderEvent.BatteryPopped,
                        payload: {
                            order,
                        },
                    });
                    await channel.unsubscribe();
                }
            });
    }

    // Update the station's battery count
    const { error: stationError } = await supabaseAdmin
        .from("stations")
        .update({
            usable_batteries: Number(data.USABLE_BATTERY),
            empty_slots: Number(data.EMPTY_SLOT_COUNT),
            total_batteries: Number(data.TOTAL),
        })
        .eq("id", stationId);

    if (stationError) {
        console.error(stationError);
    }

    // Update the station slot to reflect the new battery state
    const { error: stationSlotError } = await supabaseAdmin
        .from("station_slots")
        .update({
            battery_id: null,
            power: 0,
            voltage: 0,
            amperage: 0,
            updated_at: new Date().toISOString(),
            synced_at: new Date().toISOString(),
        })
        .eq("station_id", stationId)
        .eq("slot_id", slotId);

    if (stationSlotError) {
        console.error(stationSlotError);
    }
};
