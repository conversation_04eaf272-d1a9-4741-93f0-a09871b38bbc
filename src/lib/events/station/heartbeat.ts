import { supabaseAdmin } from "$lib/services/supabase-admin";
import { parseStationSlots } from "$lib/utils/station-parser";
import type { StationEvent } from "$types/carku";

export type HeartBeatData = {
    ACT: StationEvent.HeartBeat
    STATUS: `${number}`
    APN?: string
    SLOTSTATUS: string
    USABLE_BATTERY: `${number}`
    EMPTY_SLOT_COUNT: `${number}`
    TOTAL: `${number}`
    USABLE_BATTERY_NEW: string
    STATIONID: `${number}`
    CSQ?: `${number}`
    upsta: `${number}`
    LOCK_MODE: `${number}`
    CH?: string
    CHKSUM: string
    DIDINFO: string
};

export const onHearBeat = async (data: HeartBeatData) => {
    if (!data.CH) {
        return;
    }

    const stationId = data.DIDINFO;
    const parsedSlots = parseStationSlots(data.CH);

    if (parsedSlots.length > 0) {
        const parsedSlotIds = parsedSlots.map((slot) => slot.slot_id);

        const { error, data } = await supabaseAdmin
            .from("station_slots")
            .select()
            .eq("station_id", stationId)
            .in("slot_id", parsedSlotIds);

        if (error) {
            console.error(error);
        }

        const slotIds = data?.map((slot) => slot.slot_id) ?? [];

        for (const parsedSlot of parsedSlots) {
            if (slotIds.includes(parsedSlot.slot_id)) {
                const { error } = await supabaseAdmin
                    .from("station_slots")
                    .update({
                        ...parsedSlot,
                        updated_at: new Date().toISOString(),
                    })
                    .eq("station_id", stationId)
                    .eq("slot_id", parsedSlot.slot_id);

                if (error) {
                    console.error(error);
                }
            } else {
                const { error } = await supabaseAdmin
                    .from("station_slots")
                    .insert({
                        ...parsedSlot,
                        station_id: stationId,
                    });

                if (error) {
                    console.error(error);
                }
            }
        }
    }

    const { error } = await supabaseAdmin
        .from("stations")
        .upsert({
            id: stationId,
            status: Number(data.STATUS),
            apn: data.APN ? data.APN : undefined,
            usable_batteries: Number(data.USABLE_BATTERY),
            empty_slots: Number(data.EMPTY_SLOT_COUNT),
            total_batteries: Number(data.TOTAL),
            signal_strength: data.CSQ === undefined ? undefined : Number(data.CSQ),
            update_status: data.upsta === "1",
            lock_mode: Number(data.LOCK_MODE),
            last_heartbeat_at: new Date().toISOString(),
        });

    if (error) {
        console.error(error);
    }
};
