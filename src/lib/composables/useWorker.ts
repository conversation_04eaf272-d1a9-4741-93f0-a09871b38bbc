type WorkerFunction<T, U> = (payload: T) => U;

function depsParser(deps: string[]) {
    if (deps.length === 0)
        return "";

    return deps.map(dep => `importScripts("${dep}");`).join("\n") + "\n";
}

export function useWorker<T, U>(workerFunction: WorkerFunction<T, U>, dependencies: string[] = []) {
    const script = `
        ${depsParser(dependencies)}
        const workerFunction = ${workerFunction.toString()};
        self.onmessage = async (event) => {
            const result = await workerFunction(event.data);
            self.postMessage(result);
        };
    `;

    return (payload: T) => new Promise<U>((resolve, reject) => {
        const blob = new Blob([script], { type: "text/javascript" });
        const worker = new Worker(URL.createObjectURL(blob));
        worker.onmessage = (event: MessageEvent<U>) => resolve(event.data);
        worker.onerror = reject;
        worker.postMessage(payload);
    });
}
