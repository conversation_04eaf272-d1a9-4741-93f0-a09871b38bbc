import type { PaymentMethod } from "@stripe/stripe-js";
import Strip<PERSON> from "stripe";
import { onMount } from "svelte";
import { derived, writable } from "svelte/store";

import { api } from "$lib/services/api";

export const usePaymentMethods = (autoLoad = true) => {
    const defaultPaymentMethodId = writable<string | null>(null);
    const paymentMethods = writable<PaymentMethod[]>([]);
    const isLoading = writable(autoLoad);

    const sortedPaymentMethods = derived(
        [paymentMethods, defaultPaymentMethodId],
        ([$paymentMethods, $defaultPaymentMethodId]) => [...$paymentMethods]
            .sort((a, b) => {
                if (a.id === $defaultPaymentMethodId) return -1;
                if (b.id === $defaultPaymentMethodId) return 1;
                return 0;
            }),
    );

    const defaultPaymentMethod = derived(
        [defaultPaymentMethodId, paymentMethods],
        ([$defaultPaymentMethodId, $paymentMethods]) => {
            return $paymentMethods.find((method) => method.id === $defaultPaymentMethodId);
        },
    );

    const loadDefaultPaymentMethod = async () => {
        const { data } = await api.get<Stripe.Customer>("/payments/customers");
        defaultPaymentMethodId.set(data.invoice_settings.default_payment_method as string);
    };

    const loadPaymentMethods = async () => {
        try {
            isLoading.set(true);
            const { data } = await api.get<PaymentMethod[]>("/payments/methods");
            paymentMethods.set(data);
        } finally {
            isLoading.set(false);
        }
    };

    const load = async () => {
        await Promise.all([
            loadDefaultPaymentMethod(),
            loadPaymentMethods(),
        ]);
    };

    onMount(() => {
        if (autoLoad) {
            load();
        }
    });

    return {
        load,
        defaultPaymentMethodId,
        paymentMethods,
        isLoading,
        sortedPaymentMethods,
        defaultPaymentMethod,
    };
};
