import { onMount } from "svelte";
import { writable } from "svelte/store";

export const useLocalStorage = <T>(key: string, initialValue: T) => {
    const value = writable<T>(initialValue);

    onMount(() => {
        const json = localStorage.getItem(key);

        if (json) {
            try {
                value.set(JSON.parse(json).value);
            } catch {
                localStorage.removeItem(key);
                value.set(initialValue);
            }
        }

        const unsubscribe = value.subscribe((value) => {
            localStorage.setItem(key, JSON.stringify({ value }));
        });

        return () => {
            unsubscribe();
        };
    });

    return value;
};
