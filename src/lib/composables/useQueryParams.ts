import { onMount } from "svelte";
import { get, writable } from "svelte/store";

import { goto } from "$app/navigation";
import { page } from "$app/stores";

export const useQueryParams = <T>(fieldName: string, defaultValue: T) => {
    const value = writable<T>(defaultValue);

    const syncWithQueryParams = (value?: T) => {
        const url = get(page).url;
        if (value) {
            url.searchParams.set(fieldName, String(value));
        } else {
            url.searchParams.delete(fieldName);
        }
        const newUrl = url.searchParams.toString().length > 0 ? `${url.pathname}?${url.searchParams.toString()}` : url.pathname;
        goto(newUrl, { keepFocus: true, noScroll: true });
    };

    onMount(() => {
        const savedValue = get(page).url.searchParams.get(fieldName) as T;
        value.set(savedValue || defaultValue);

        const unsubscribe = value.subscribe((newValue) => {
            syncWithQueryParams(newValue);
        });

        return () => {
            unsubscribe();
        };
    });

    return value;
};
