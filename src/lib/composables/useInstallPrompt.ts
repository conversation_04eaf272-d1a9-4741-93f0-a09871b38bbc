import { onMount } from "svelte";
import { writable } from "svelte/store";

export const useInstallPrompt = () => {
    let deferredPrompt: Event|null = null;

    const isInstallable = writable(false);
    const isInstalled = writable(false);

    const installApp = async () => {
        if (!deferredPrompt) {
            return;
        }

        // @ts-expect-error - typescript doesn't know about prompt()
        deferredPrompt.prompt();
        // @ts-expect-error - typescript doesn't know about userChoice
        const { outcome } = await deferredPrompt.userChoice;
        if (outcome === "accepted") {
            console.log("User accepted the A2HS prompt");
        } else {
            console.log("User dismissed the A2HS prompt");
        }
        deferredPrompt = null;
    };

    onMount(() => {
        const isMobile = (navigator.userAgent.match(/Android|iPhone|iPad|iPod/i) ?? []).length > 0;

        if (!isMobile) {
            return;
        }

        window.addEventListener("beforeinstallprompt", (e) => {
            e.preventDefault();
            deferredPrompt = e;
            isInstallable.set(true);
        });

        window.addEventListener("appinstalled", () => {
            isInstalled.set(true);
        });
    });

    return {
        isInstallable,
        isInstalled,
        installApp,
    };
};
