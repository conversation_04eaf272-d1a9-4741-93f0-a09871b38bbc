import QrScanner from "qr-scanner";
import { onMount } from "svelte";
import { writable } from "svelte/store";

export const useQrCodeScan = (elementId: string, onScan: (stationId: string) => void) => {
    let qrScanner: QrScanner | null = null;

    const hasFlash = writable(false);

    const extractStationId = (url: string) => {
        const urlObject = new URL(url);
        return urlObject.searchParams.get("flag") || urlObject.searchParams.get("station_id");
    };

    const onScanSuccess = (result: string) => {
        const stationId = extractStationId(result);

        if (stationId) {
            onScan(stationId);
        }
    };

    const loadQrScanner = async () => {
        await qrScanner?.start();
        hasFlash.set(await qrScanner?.hasFlash() ?? false);
    };

    const manualScan = async () => {
        const videoElement = document.getElementById(elementId) as HTMLVideoElement;

        if (!videoElement) {
            return;
        }

        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        if (!context) {
            throw new Error("Canvas context not found");
        }

        const videoRatio = videoElement.videoWidth / videoElement.videoHeight;

        canvas.width = 1000;
        canvas.height = Math.round(canvas.width / videoRatio);
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const code = await QrScanner.scanImage(canvas.toDataURL("image/jpeg"), { returnDetailedScanResult: true });

        onScanSuccess(code.data);
    };

    onMount(() => {
        const element = document.getElementById(elementId);

        if (!element) {
            throw new Error(`Element with id ${elementId} not found`);
        }

        qrScanner = new QrScanner(
            element as HTMLVideoElement,
            () => null,
            {
                preferredCamera: "environment",
                highlightScanRegion: true,
                highlightCodeOutline: true,
            },
        );

        loadQrScanner();

        const interval = setInterval(() => {
            manualScan()
                .catch((error) => console.error(error));
        }, 1000);

        return () => {
            qrScanner?.destroy();
            qrScanner = null;
            clearInterval(interval);
        };
    });

    return {
        hasFlash,
        toggleFlash: () => qrScanner?.toggleFlash(),
        isFlashOn: () => qrScanner?.isFlashOn(),
        manualScan,
    };
};
