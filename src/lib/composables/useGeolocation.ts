import { onMount } from "svelte";

import { latitude, longitude } from "$lib/stores/user";

type Options = {
    onLocationLoaded?: ([latitude, longitude]: [number, number]) => void;
}

export const useGeolocation = (options: Options = {}) => {
    const loadLocationFromCache = () => {
        const lat = localStorage.getItem("latitude");
        const lng = localStorage.getItem("longitude");

        if (!lat || !lng) {
            return;
        }

        latitude.set(+lat);
        longitude.set(+lng);
    };

    const loadLocation = () => {
        loadLocationFromCache();

        return new Promise<void>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    latitude.set(position.coords.latitude);
                    longitude.set(position.coords.longitude);
                    options.onLocationLoaded?.([position.coords.latitude, position.coords.longitude]);
                    resolve();
                },
                (error) => {
                    console.error(error);
                    reject(error);
                },
                {
                    maximumAge: 0,
                    enableHighAccuracy: true,
                },
            );
        });
    };

    const saveLocationToCache = ([lat, lng]: [number, number]) => {
        localStorage.setItem("latitude", String(lat));
        localStorage.setItem("longitude", String(lng));
    };

    const watchGeolocation = () => {
        const watchId = navigator.geolocation.watchPosition(
            (position) => {
                latitude.set(position.coords.latitude);
                longitude.set(position.coords.longitude);
                saveLocationToCache([position.coords.latitude, position.coords.longitude]);
            },
            (positionError) => {
                console.error(positionError);
            },
            {
                enableHighAccuracy: true,
                maximumAge: 0,
            },
        );

        return () => navigator.geolocation.clearWatch(watchId);
    };

    onMount(() => {
        loadLocation();

        const locationSubscription = watchGeolocation();

        return () => {
            locationSubscription();
        };
    });

    return {
        latitude,
        longitude,
    };
};
