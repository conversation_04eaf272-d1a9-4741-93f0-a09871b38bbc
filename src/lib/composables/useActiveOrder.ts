import { onMount } from "svelte";
import { get, writable } from "svelte/store";

import { supabase } from "$lib/services/supabase";
import { user } from "$lib/stores/user";
import { OrderEvent, OrderStatus, OrderType } from "$types/orders";
import type { Tables } from "$types/supabase";

export const order = writable<Tables<"orders"> | null>(null);
export const location = writable<Tables<"station_locations"> | null>(null);
export const isLoading = writable(true);

const loadOrderLocation = async () => {
    const $order = get(order);

    if (!$order) {
        return;
    }

    const { error: locationError, data: _location } = await supabase
        .from("station_locations")
        .select()
        .eq("station_id", $order.station_id)
        .maybeSingle();

    if (locationError) {
        console.error(locationError);
        return;
    }

    location.set(_location);
};

export const loadActiveOrder = async () => {
    try {
        isLoading.set(true);
        const $user = get(user);

        if (!$user) {
            return;
        }

        const { error, data: _order } = await supabase
            .from("orders")
            .select()
            .eq("user_id", $user.id)
            .eq("status", OrderStatus.Ongoing)
            .is("ended_at", null)
            .maybeSingle();

        if (error || !_order) {
            return;
        }

        order.set(_order);

        await loadOrderLocation();
    } finally {
        isLoading.set(false);
    }
};

const listenToOrderEvents = () => {
    const $user = get(user);

    if (!$user) {
        return () => {};
    }

    const channel = supabase.channel($user.id)
        .subscribe((event) => {
            if (event === "SUBSCRIBED") {
                channel
                    .on("broadcast", { event: OrderEvent.BatteryPopped }, async (event) => {
                        const { order: _order } = event.payload as { order: Tables<"orders"> };

                        if (_order.type !== OrderType.Rental) {
                            return;
                        }

                        order.set(_order);
                        await loadOrderLocation();
                    })
                    .on("broadcast", { event: OrderEvent.BatteryReturned }, (event) => {
                        const { order: _order } = event.payload as { order: Tables<"orders"> };
                        const $order = get(order);

                        if ($order && $order.id === _order.id) {
                            order.set(null);
                        }
                    });
            }
        });

    return () => {
        channel.unsubscribe();
    };
};

export const useActiveOrder = () => {
    onMount(() => {
        const unsubscribe = listenToOrderEvents();
        loadActiveOrder();

        return () => {
            unsubscribe();
        };
    });

    return {
        order,
        location,
        isLoading,
    };
};
