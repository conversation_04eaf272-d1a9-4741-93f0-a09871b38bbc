<!doctype html>
<html lang="en">
	<head>
		<title>HIKO</title>

		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<link rel="manifest" href="%sveltekit.assets%/manifest.json" />
		<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0, interactive-widget=resizes-visual" />

		<!-- iPhone X (1125px x 2436px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" href="/icons/apple-launch-1125x2436.png">
		<!-- iPhone 8, 7, 6s, 6 (750px x 1334px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" href="/icons/apple-launch-750x1334.png">
		<!-- iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (1242px x 2208px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" href="/icons/apple-launch-1242x2208.png">
		<!-- iPhone 5 (640px x 1136px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" href="/icons/apple-launch-640x1136.png">
		<!-- iPad Mini, Air (1536px x 2048px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" href="/icons/apple-launch-1536x2048.png">
		<!-- iPad Pro 10.5" (1668px x 2224px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" href="/icons/apple-launch-1668x2224.png">
		<!-- iPad Pro 12.9" (2048px x 2732px) -->
		<link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" href="/icons/apple-launch-2048x2732.png">

		<meta name="apple-mobile-web-app-capable" content="yes">

		%sveltekit.head%
	</head>
	<body class="flex flex-col bg-slate-950 w-full min-h-dvh leading-snug" data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
