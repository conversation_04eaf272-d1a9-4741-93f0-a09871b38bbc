export enum OrderType {
    Purchase = "purchase",
    Rental = "rental",
}

export enum OrderStatus {
    Pending = "pending",
    Ongoing = "ongoing",
    Completed = "completed",
    Cancelled = "cancelled",
    Refunded = "refunded",
    Failed = "failed",
    LateCompleted = "late_completed",
    LateFailed = "late_failed",
}

export enum OrderEvent {
    BatteryPopped = "battery_popped",
    BatteryReturned = "battery_returned",
}
