export enum BatteryCableType {
    Lightning = 1,
    MicroUSB = 2,
    USBTypeC = 3,
}

export type BatteryStationAction = "popup_confirm_with_cable";

export type BatteryStationResponse = {
    data: unknown
    code: number
    msg: string
};

export enum StripeEvent {
    ConnectionToken = "connection_token",
    TerminalLocation = "terminal_location",
    CreatePaymentIntent = "create_paymentIntent",
    CollectPaymentMethodSuccess = "collect_paymentMethod_success",
    ChargingRules = "charging_rules",
    OrderInformation = "order_information",
    CreateSetupIntent = "create_setupIntent",
    CollectSetupIntentSuccess = "collect_setupIntent_success",
}

export type StripePayload = {
    act: string;
    did: string;
    mac: string;
    type?: string;
    [key: string]: unknown;
};

export enum StationEvent {
    HeartBeat = "heartbeat",
    ReturnBack = "return_back",
    PopupConfirm = "popup_confirm",
    RentConfirm = "rent_confirm",
    SyncSetting = "sync_setting",
    QueryConfirm = "query_confirm",
    SlotLock = "slot_lock",
    SlotUnlock = "slot_unlock",
    Reboot = "reboot",
    SyncBattery = "sync_battery",
    PopupConfirmWithCable = "popup_confirm_with_cable",
    EditLockMode = "edit_lockmode",
}
