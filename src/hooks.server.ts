import { init } from "@jill64/sentry-sveltekit-cloudflare/server";
import type { Handle, HandleServerError } from "@sveltejs/kit";
import { sequence } from "@sveltejs/kit/hooks";

import { env } from "$env/dynamic/public";
import { getCookieSession } from "$lib/utils/auth";

const { onHandle, onError } = init(env.PUBLIC_SENTRY_DNS);

const handleCookieSession: Handle = async ({ event, resolve }) => {
    event.locals.user = await getCookieSession(event.cookies) ?? undefined;
    return resolve(event);
};

export const handle: Handle = sequence(onHandle(), handleCookieSession);

export const handleError: HandleServerError = onError(({ error, event }) => {
    console.error("An error occurred on the server side:", error, event);
});
