<script lang="ts">
    import { goto } from "$app/navigation";
    import Accordion from "$components/ui/Accordion.svelte";
    import ConfirmDialog from "$components/ui/ConfirmDialog.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { supabase } from "$lib/services/supabase";
    import { BATTERY_PURCHASE_PRICE, DEFAULT_BATTERY_HOURLY_RENTAL_PRICE, MAX_CHARGEABLE_HOURS_PER_DAY } from "$lib/utils/pricing";
    import { Route } from "$types/routes";

    let showConfirmDialog = false;
    let isDeleting = false;
    let showErrorDialog = false;

    useTopBar({
        backUrl: Route.Settings,
    });

    const deleteAccount = async () => {
        try {
            isDeleting = true;
            await api.delete("/users");
            await supabase.auth.signOut();
            await goto(Route.Login);
        } catch (err) {
            console.error(err);
            showErrorDialog = true;
        } finally {
            isDeleting = false;
            showConfirmDialog = false;
        }
    };
</script>

<div class="bg-white flex flex-col flex-1 px-3 py-6">
    <h1 class="font-bold text-2xl mb-6">{$t("faq.faq")}</h1>
    <div class="grid grid-cols-1 gap-3">
        <Accordion>
            <h2>{$t("faq.how_to_rent.title")}</h2>
            <p slot="content">{$t("faq.how_to_rent.description")}</p>
        </Accordion>
        <Accordion>
            <h2>{$t("faq.how_to_return.title")}</h2>
            <p slot="content">{$t("faq.how_to_return.description")}</p>
        </Accordion>
        <Accordion>
            <h2>{$t("faq.how_much.title")}</h2>
            <p slot="content">
                {$t("faq.how_much.description")}
                <br />
                {$t("faq.how_much.per_hour", { value: DEFAULT_BATTERY_HOURLY_RENTAL_PRICE })}
                <br />
                {$t("faq.how_much.per_day", { value: DEFAULT_BATTERY_HOURLY_RENTAL_PRICE * MAX_CHARGEABLE_HOURS_PER_DAY })}
                <br />
                {$t("faq.how_much.per_2_days", { value: DEFAULT_BATTERY_HOURLY_RENTAL_PRICE * MAX_CHARGEABLE_HOURS_PER_DAY * 2 })}
                <br />
                {$t("faq.how_much.per_2_days_and_more", { value: BATTERY_PURCHASE_PRICE })}
                <br />
                <br />
                <span class="underline">Note:</span> {$t("faq.how_much.if_not_returned", { value: BATTERY_PURCHASE_PRICE })}
            </p>
        </Accordion>
        <Accordion>
            <h2>{$t("faq.when_lost.title")}</h2>
            <p slot="content">{$t("faq.when_lost.description")}</p>
        </Accordion>
        <Accordion>
            <h2>{$t("faq.when_issue.title")}</h2>
            <p slot="content">{$t("faq.when_issue.description")}</p>
        </Accordion>
    </div>

    <h2 class="font-bold text-2xl my-6">{$t("faq.contact_us.title")}</h2>
    <Accordion>
        <h2>{$t("faq.contact_us.how")}</h2>
        <p slot="content">
            {$t("faq.contact_us.online_help")}
            <br />
            {$t("faq.contact_us.phone_number")} <b>+****************</b>
            <br />
            {$t("faq.contact_us.support_email")} <a
                href="mailto:<EMAIL>"
                class="text-blue-700 font-medium underline"
            >
                <EMAIL>
            </a>
        </p>
    </Accordion>

    <h2 class="font-bold text-2xl my-6">{$t("faq.delete_account.title")}</h2>
    <Accordion>
        <h2>{$t("faq.delete_account.how")}</h2>
        <p slot="content">
            {$t("faq.delete_account.description")}
            <br />
            <br />
            <button
                on:click={() => showConfirmDialog = true}
                class="text-primary-500 font-medium"
            >
                {$t("faq.delete_account.delete")}
            </button>
        </p>
    </Accordion>
</div>

<ConfirmDialog
    bind:visible={showConfirmDialog}
    on:confirm={deleteAccount}
    loading={isDeleting}
    dangerMode={true}
>
    <p class="text-sm mb-3">This action is irreversible, and all your account data will be permanently removed from our system.</p>

    <p class="font-medium mb-1">Please note:</p>
    <ul class="list-disc text-sm ml-6">
        <li>
            <p>You cannot delete your account if you have an ongoing order.</p>
        </li>
        <li>
            <p>You will no longer have access to your account, order history, or saved information.</p>
        </li>
    </ul>
</ConfirmDialog>

<ErrorDialog bind:visible={showErrorDialog}>
    <slot slot="content">
        <p>You have an ongoing order.</p>
        <p>Please complete it before proceeding with deleting your account.</p>
    </slot>
</ErrorDialog>
