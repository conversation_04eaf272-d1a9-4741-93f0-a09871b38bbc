<script lang="ts">
    import { SvelteComponent } from "svelte";
    import type { SvelteHTMLElements } from "svelte/elements";
    import { derived } from "svelte/store";

    import HeroiconsClipboardDocumentList16Solid from "~icons/heroicons/clipboard-document-list-16-solid";
    import HeroiconsCreditCard16Solid from "~icons/heroicons/credit-card-16-solid";
    import HeroiconsExclamationCircle16Solid from "~icons/heroicons/exclamation-circle-16-solid";
    import HeroiconsPlay20Solid from "~icons/heroicons/play-20-solid";
    import HeroiconsQuestionMarkCircle16Solid from "~icons/heroicons/question-mark-circle-16-solid";
    import MaterialSymbolsLogoutRounded from "~icons/material-symbols/logout-rounded";
    import { goto } from "$app/navigation";
    import LanguageSelector from "$components/ui/LanguageSelector.svelte";
    import { locale, t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { supabase } from "$lib/services/supabase";
    import { Route } from "$types/routes";

    type NavItem = {
        text: string
        icon: typeof SvelteComponent<SvelteHTMLElements["svg"]>
        to: Route
    };

    const navItems = derived<typeof locale, NavItem[]>(locale, () => [
        {
            text: $t("settings.payment_methods"),
            icon: HeroiconsCreditCard16Solid,
            to: Route.PaymentMethods,
        },
        {
            text: $t("settings.history"),
            icon: HeroiconsClipboardDocumentList16Solid,
            to: Route.History,
        },
        {
            text: $t("settings.faq"),
            icon: HeroiconsQuestionMarkCircle16Solid,
            to: Route.FAQ,
        },
        {
            text: $t("settings.report"),
            icon: HeroiconsExclamationCircle16Solid,
            to: Route.Report,
        },
        {
            text: $t("settings.play_onboarding"),
            icon: HeroiconsPlay20Solid,
            to: Route.OnBoarding,
        },
    ]);

    const logout = async () => {
        await supabase.auth.signOut();
        await goto(Route.Login);
    };
</script>

<div class="bg-primary-500 flex flex-col flex-1 py-6">
    <h1 class="text-white font-bold text-2xl px-6 mb-6">{$t("settings.settings")}</h1>
    <ul class="flex flex-col gap-2 flex-1 px-3">
        {#each $navItems as item}
            <li>
                <a
                    href={item.to}
                    class="bg-white rounded-2xl shadow-app active:bg-slate-100 flex items-center gap-6 px-6 py-4"
                >
                    <svelte:component
                        this={item.icon}
                        width="26"
                        height="26"
                        class="text-primary-500"
                    />
                    <p class="text-xl">{item.text}</p>
                </a>
            </li>
        {/each}
        <li class="my-6">
            <hr class="rounded border-2 border-white/20 w-1/2 mx-auto" />
        </li>
        <li>
            <LanguageSelector />
        </li>
        <li class="mt-auto">
            <button
                on:click={logout}
                class="bg-white rounded-2xl shadow-app active:bg-slate-100 text-red-500 flex items-center gap-6 w-full px-6 py-4"
            >
                <MaterialSymbolsLogoutRounded
                    width="26"
                    height="26"
                />
                <span class="text-xl">{$t("settings.logout")}</span>
            </button>
        </li>
    </ul>
</div>
