<script lang="ts">
    import { onMount } from "svelte";

    import StationItem from "$components/StationItem.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { useGeolocation } from "$lib/composables/useGeolocation";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { user } from "$lib/stores/user";
    import type { Tables } from "$types/supabase";

    let userStations: { station: Tables<"station_locations">, isFavorite: boolean }[] = [];
    let isLoading = true;

    useGeolocation();

    const loadFavoriteStations = async () => {
        try {
            if (!$user) {
                return;
            }

            isLoading = true;
            const { data, error } = await supabase
                .from("favorite_stations")
                .select("station_id")
                .eq("user_id", $user.id)
                .order("created_at", { ascending: false });

            if (error) {
                console.error(error);
                return;
            }

            const stationIds = data.map((d) => d.station_id);

            const { data: _stations, error: stationsError } = await supabase
                .from("station_locations")
                .select()
                .in("station_id", stationIds);

            if (stationsError) {
                console.error(stationsError);
                return;
            }

            userStations = stationIds.map((id) => ({
                station: _stations.find((s) => s.station_id === id)!,
                isFavorite: true,
            }));
        } catch (error) {
            console.error(error);
        } finally {
            isLoading = false;
        }
    };

    onMount(() => {
        loadFavoriteStations();
    });
</script>

{#if isLoading}
    <div class="flex justify-center mt-16">
        <Loader />
    </div>
{:else if userStations.length === 0}
    <div class="mt-16">
        <EmptyPlaceholder>
            <p>{$t("favorites.no_favorite")}</p>
        </EmptyPlaceholder>
    </div>
{:else}
    <ul>
        {#each userStations as { station, isFavorite }}
            <StationItem
                {station}
                {isFavorite}
            />
        {/each}
    </ul>
{/if}
