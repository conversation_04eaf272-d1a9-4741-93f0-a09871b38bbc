<script lang="ts">
    import { onMount } from "svelte";

    import PhFlashlight from "~icons/ph/flashlight";
    import { goto } from "$app/navigation";
    import Alert from "$components/ui/Alert.svelte";
    import { useQrCodeScan } from "$lib/composables/useQrCodeScan";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { cn } from "$lib/utils/style";
    import { Route } from "$types/routes";

    let isFlashLightOn = false;
    let isTogglingFlashLight = false;
    let cameraPermissionDenied = false;

    useTopBar({
        cssClass: "bg-papaya",
    });

    const {
        hasFlash,
        toggleFlash,
        isFlashOn,
        manualScan,
    } = useQrCodeScan(
        "reader",
        (id: string) => goto(`${Route.Home}?station_id=${id}`),
    );

    const toggleFlashLight = async () => {
        try {
            isTogglingFlashLight = true;
            await toggleFlash();
            isFlashLightOn = isFlashOn() ?? false;
        } finally {
            isTogglingFlashLight = false;
        }
    };

    onMount(() => {
        isFlashLightOn = isFlashOn() ?? false;
    });
</script>

<div class="bg-papaya flex flex-col flex-1 p-6">
    <div class="relative w-full rounded-3xl aspect-[3/4] sm:aspect-square bg-slate-950 overflow-hidden">
        <video
            on:click={manualScan}
            id="reader"
            class="absolute top-0 left-0 w-full h-full object-cover"
        >
            <track
                default
                kind="captions"
                srclang="en"
                src={null}
            />
        </video>

        {#if cameraPermissionDenied}
            <div class="absolute w-full h-full text-white flex flex-col justify-center p-6">
                <Alert color="warning">
                    <p slot="title">
                        Camera Access Denied
                    </p>
                    <p>Please enable camera access in your device settings to proceed.</p>
                </Alert>
            </div>
        {/if}
    </div>
    <div class="text-center text-white my-6">
        <p class="text-2xl font-bold mb-1">{$t("station_scan.scan_qr_code")}</p>
        <p>{$t("station_scan.place_in_the_center")}</p>
    </div>
    {#if $hasFlash}
        <div class="flex justify-center mt-auto">
            <button
                on:click={toggleFlashLight}
                disabled={isTogglingFlashLight}
                class={cn("disabled:pointer-events-none disabled:opacity-50 rounded-full shadow-app p-3", {
                    "bg-white active:bg-slate-100 text-black": isFlashLightOn,
                    "bg-transparent active:bg-white/10 text-white outline": !isFlashLightOn,
                })}
            >
                <PhFlashlight
                    width="42"
                    height="42"
                />
            </button>
        </div>
    {/if}
</div>
