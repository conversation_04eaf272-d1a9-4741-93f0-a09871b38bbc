<script lang="ts">
    import { parsePhoneNumber } from "libphonenumber-js";

    import Button from "$components/ui/Button.svelte";
    import LanguageSelector from "$components/ui/LanguageSelector.svelte";
    import PhoneInput from "$components/ui/PhoneInput.svelte";
    import HikoLogoWhite from "$lib/assets/images/hiko-white.png";
    import { useQueryParams } from "$lib/composables/useQueryParams";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { navigateTo } from "$lib/utils/navigation";
    import { Route } from "$types/routes";

    const phoneNumber = useQueryParams("phone", "");
    let isPhoneNumberValid = true;
    let sendingOTP = false;
    let errors = {
        invalid_phone_number: false,
    };

    useTopBar({
        isVisible: false,
    });

    const onPhoneValidation = (event: CustomEvent<boolean>) => {
        isPhoneNumberValid = event.detail;
        errors.invalid_phone_number = false;
    };

    const signInWithOTP = async () => {
        try {
            sendingOTP = true;
            const parsedPhoneNumber = parsePhoneNumber($phoneNumber, "CA").number;

            const { error } = await supabase.auth.signInWithOtp({
                phone: parsedPhoneNumber,
            });

            if (error) {
                errors.invalid_phone_number = error.name === "AuthApiError";
                return;
            }

            await navigateTo(Route.LoginOTP, {
                query: {
                    phone_number: parsedPhoneNumber,
                },
            });
        } catch (err) {
            console.error(err);
        } finally {
            sendingOTP = false;
        }
    };
</script>

<div class="flex flex-col flex-1 bg-primary-500 text-white px-3 py-6">
    <LanguageSelector
        class="text-3xl font-theme text-black"
        buttonClass="uppercase"
    />

    <div class="flex justify-center mt-12">
        <img
            src={HikoLogoWhite}
            alt="HIKO"
            width="240"
        />
    </div>
    <p class="text-center"><b>{$t("login.keep_charged")}</b> {$t("login.without_pausing")}</p>

    <div class="mt-auto">
        <p class="text-xl font-medium text-center mb-3">{$t("login.enter_phone_number")}</p>
        <div>
            <PhoneInput
                bind:value={$phoneNumber}
                on:validation={onPhoneValidation}
                placeholder="(*************"
            />
            {#if errors.invalid_phone_number}
                <p class="text-xs text-red-500 mt-3">{$t("login.invalid_phone_number")}</p>
            {/if}
        </div>
        <Button
            on:click={signInWithOTP}
            loading={sendingOTP}
            disabled={!$phoneNumber || !isPhoneNumberValid || sendingOTP}
            class="w-full bg-white text-primary-500 font-theme uppercase text-3xl mt-6"
        >
            {$t("login.send_code")}
        </Button>
    </div>
</div>
