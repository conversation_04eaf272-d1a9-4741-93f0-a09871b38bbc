<script lang="ts">
    import { onMount } from "svelte";

    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import Button from "$components/ui/Button.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import Input from "$components/ui/Input.svelte";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { user } from "$lib/stores/user";
    import { Route } from "$types/routes";

    let email = "";
    let isUpdating = false;
    let showErrorDialog = false;
    let isEmailUpdated = false;
    let errors = {
        email_exists: false,
    };

    $: isEmailValid = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(email);
    $: canUpdate = isEmailValid && !isUpdating;

    useTopBar({
        backUrl: $page.state.returnTo ?? Route.Settings,
    });

    const updateEmail = async () => {
        try {
            isUpdating = true;

            const { error, data } = await supabase.auth.updateUser({
                email,
            }, {
                emailRedirectTo: window.location.origin + Route.Report,
            });

            if (error) {
                console.error(error);

                if (error.status === 422) {
                    errors.email_exists = true;
                }

                showErrorDialog = true;
                return;
            }

            if (data.user) {
                user.set(data.user);
            }

            isEmailUpdated = true;
        } finally {
            isUpdating = false;
        }
    };

    onMount(() => {
        if ($user?.email) {
            goto(Route.Report);
        }
    });
</script>

<div class="flex flex-col flex-1">
    <div class="flex-1 px-3 py-6">
        {#if !isEmailUpdated}
            <h1 class="font-bold text-2xl mb-6">{$t("report.before_submitting")}</h1>
            <p class="text-sm opacity-80 mb-6">{$t("report.enter_email_below")}</p>

            <div>
                <Input
                    bind:value={email}
                    placeholder={$t("report.your_email_address")}
                    type="email"
                    name="email"
                    autocomplete="email"
                    rules={[
                        isEmailValid || $t("report.enter_valid_address"),
                        email.length > 0 || $t("report.field_required"),
                    ]}
                />
            </div>
        {:else}
            <h1 class="font-bold text-2xl mb-6">{$t("report.verification_required")}</h1>
            <div>
                <p class="font-medium mb-1">{$t("report.email_sent")}</p>
                <p class="opacity-80 text-sm">{$t("report.check_inbox")}</p>
            </div>
        {/if}
    </div>

    {#if !isEmailUpdated}
        <div class="sticky bottom-0 bg-white border-t mt-auto px-3 py-6">
            <Button
                on:click={updateEmail}
                disabled={!canUpdate}
                loading={isUpdating}
                class="w-full"
            >
                {$t("report.update")}
            </Button>
        </div>
    {/if}
</div>

<ErrorDialog bind:visible={showErrorDialog}>
    <p slot="content">
        {#if errors.email_exists}
            {$t("report.email_exists")}
        {:else}
            {$t("common.something_wrong_happened")}
        {/if}
    </p>
</ErrorDialog>
