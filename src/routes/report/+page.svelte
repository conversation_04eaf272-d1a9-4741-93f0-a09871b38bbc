<script lang="ts">
    import { onMount } from "svelte";

    import PhImagesSquareFill from "~icons/ph/images-square-fill";
    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import Button from "$components/ui/Button.svelte";
    import Dialog from "$components/ui/Dialog.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import FileInput from "$components/ui/FileInput.svelte";
    import RadioButton from "$components/ui/RadioButton.svelte";
    import Textarea from "$components/ui/Textarea.svelte";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { useWorker } from "$lib/composables/useWorker";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { user } from "$lib/stores/user";
    import { ISSUE_DESCRIPTION_MAX_LENGTH } from "$lib/utils/issues";
    import { IssueType } from "$types/issues";
    import { Route } from "$types/routes";

    let step: "type" | "description" = "type";
    let issueId = "";
    let isSubmitting = false;
    let showSuccessDialog = false;
    let showErrorDialog = false;
    const options = [
        {
            type: IssueType.BatteryNotCharging,
            title: $t("report.battery_not_charging.title"),
            description: $t("report.battery_not_charging.description"),
        },
        {
            type: IssueType.BatteryNotHoldingCharge,
            title: $t("report.battery_not_holding_charge.title"),
            description: $t("report.battery_not_holding_charge.description"),
        },
        {
            type: IssueType.RentalTimerNotStopping,
            title: $t("report.rental_timer_not_stopping.title"),
            description: $t("report.rental_timer_not_stopping.description"),
        },
        {
            type: IssueType.StationUnavailable,
            title: $t("report.station_unavailable.title"),
            description: $t("report.station_unavailable.description"),
        },
        {
            type: IssueType.BatteryDamage,
            title: $t("report.battery_damage.title"),
            description: $t("report.battery_damage.description"),
        },
        {
            type: IssueType.StationMalfunction,
            title: $t("report.station_malfunction.title"),
            description: $t("report.station_malfunction.description"),
        },
        {
            type: IssueType.BillingDiscrepancy,
            title: $t("report.billing_discrepancy.title"),
            description: $t("report.billing_discrepancy.description"),
        },
        {
            type: IssueType.AppTechnicalIssues,
            title: $t("report.app_technical_issues.title"),
            description: $t("report.app_technical_issues.description"),
        },
        {
            type: IssueType.LostOrStolenBattery,
            title: $t("report.lost_or_stolen_battery.title"),
            description: $t("report.lost_or_stolen_battery.description"),
        },
        {
            type: IssueType.FeedbackOrSuggestions,
            title: $t("report.feedback_or_suggestions.title"),
            description: $t("report.feedback_or_suggestions.description"),
        },
        {
            type: IssueType.Other,
            title: $t("report.other.title"),
            description: $t("report.other.description"),
        },
    ];

    let issueDescription = "";
    let files: File[] = [];

    $: isIssueValid = issueId.length > 0 && (issueId !== "other" || issueDescription.trim().length > 0);

    useTopBar({
        backUrl: $page.state.returnTo ?? Route.Settings,
    });

    const submitIssue = async () => {
        if (!isIssueValid || isSubmitting) {
            return;
        }

        try {
            isSubmitting = true;

            const formData = new FormData();
            formData.append("type", issueId);
            formData.append("description", issueDescription);

            const compressFile = useWorker(async (file: File) => {
                // @ts-expect-error Jimp is not defined
                const { Jimp } = self;
                const image = await Jimp.read(Buffer.from(await file.arrayBuffer()));

                const MAX_WIDTH = 1280;
                const width = image.getWidth() > MAX_WIDTH ? MAX_WIDTH : image.getWidth();
                image.resize(width, Jimp.AUTO);
                image.quality(50);

                const buffer = await image.getBufferAsync(Jimp.MIME_JPEG);
                return new File([buffer], file.name, { type: Jimp.MIME_JPEG });
            }, ["https://cdn.jsdelivr.net/npm/jimp@0.22.12/browser/lib/jimp.min.js"]);

            for (const file of files) {
                const compressedFile = await compressFile(file);
                formData.append("file", compressedFile, file.name);
            }

            await api.post("/issues", formData, { headers: { "Content-Type": "multipart/form-data" } });

            showSuccessDialog = true;
        } catch (err) {
            console.error(err);
            showErrorDialog = true;
        } finally {
            isSubmitting = false;
        }
    };

    const onDismiss = async () => {
        showSuccessDialog = false;
        await goto(Route.Home);
    };

    onMount(() => {
        if (!$user?.email) {
            goto(Route.ReportEmail);
        }
    });
</script>

<div class="flex flex-col flex-1">
    <div class="flex-1 px-3 py-6">
        <h1 class="font-bold text-2xl mb-6">{$t("report.report_issue")}</h1>

        {#if step === "type"}
            <p class="opacity-50 mb-3">{$t("report.what_kind_of_issue")}</p>
            <ul class="grid grid-cols-1 gap-3">
                {#each options as option}
                    <li>
                        <RadioButton
                            bind:value={issueId}
                            id={option.type}
                            name="report"
                        >
                            <p class="font-medium mb-1">{option.title}</p>
                            <p class="text-sm opacity-80 leading-snug">{option.description}</p>
                        </RadioButton>
                    </li>
                {/each}
            </ul>
        {:else if step === "description"}
            <div class="mb-6">
                <Textarea
                    bind:value={issueDescription}
                    maxlength={ISSUE_DESCRIPTION_MAX_LENGTH}
                    rows={3}
                    placeholder={$t("report.describe_your_issue")}
                />
            </div>
            <FileInput
                bind:value={files}
                class="w-full bg-white active:bg-slate-100 text-black border"
                accept="image/jpeg,image/png"
                preview
                multiple
                limit={3}
                disabled={isSubmitting}
            >
                <PhImagesSquareFill />
                <span>{$t("report.attach_images")}</span>
            </FileInput>
        {/if}
    </div>

    <div class="sticky bottom-0 bg-white border-t mt-auto px-3 py-6">
        {#if step === "type"}
            <Button
                on:click={() => step = "description"}
                disabled={!issueId}
                class="w-full"
            >
                <span class="font-theme uppercase text-3xl">{$t("report.next")}</span>
            </Button>
        {:else if step === "description"}
            <Button
                on:click={submitIssue}
                disabled={!isIssueValid || isSubmitting}
                loading={isSubmitting}
                class="w-full"
            >
                <span class="font-theme uppercase text-3xl">{$t("report.submit")}</span>
            </Button>
        {/if}
    </div>
</div>

<ErrorDialog bind:visible={showErrorDialog} />

<Dialog
    bind:visible={showSuccessDialog}
    contentClass="p-0"
>
    <div class="text-lg text-white px-3 py-6">
        <h2 class="font-bold text-center text-3xl leading-snug mb-6">{$t("report.report_submitted")}</h2>
        <p>{$t("report.thank_you_for_reporting")}</p>

        <p class="text-2xl leading-snug font-bold mb-1 mt-10">{$t("report.what_next")}</p>
        <ul class="grid grid-cols-1 gap-1 list-disc ml-6">
            <li>{$t("report.issue_resolution_1")}</li>
            <li>{$t("report.issue_resolution_2")}</li>
        </ul>

        <p class="text-2xl leading-snug font-bold mb-1 mt-10">{$t("report.need_assistance")}</p>
        <div>
            <p class="mb-1">{$t("report.contact_support")}</p>
            <p>
                {$t("report.support_email")} <a
                    href="mailto:<EMAIL>"
                    class="underline"
                >
                    <EMAIL>
                </a>
            </p>
        </div>
    </div>

    <div class="sticky bottom-0 bg-primary-500 mt-auto px-3 py-6">
        <Button
            on:click={onDismiss}
            class="bg-white active:bg-slate-100 text-black w-full"
        >
            {$t("report.dismiss")}
        </Button>
    </div>
</Dialog>
