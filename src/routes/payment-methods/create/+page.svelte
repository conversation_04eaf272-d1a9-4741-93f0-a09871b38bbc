<script lang="ts">
    import { page } from "$app/stores";
    import PaymentMethodForm from "$components/PaymentMethodForm.svelte";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { Route } from "$types/routes";

    useTopBar({
        backUrl: $page.state.returnTo ?? Route.PaymentMethods,
    });
</script>

<svelte:head>
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, user-scalable=0, interactive-widget=resizes-content"
    />
</svelte:head>
<div class="flex flex-col flex-1 px-3 py-6">
    <h1 class="font-bold text-2xl mb-6">{$t("payment_methods.new_payment_method")}</h1>
    <p class="text-gray-500 mb-2">{$t("payment_methods.only_credit_cards")}</p>
    <PaymentMethodForm />
</div>
