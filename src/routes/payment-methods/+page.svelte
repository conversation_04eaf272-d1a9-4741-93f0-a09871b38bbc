<script lang="ts">
    import CreditCardItem from "$components/CreditCardItem.svelte";
    import Button from "$components/ui/Button.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { usePaymentMethods } from "$lib/composables/usePaymentMethods";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { Route } from "$types/routes";

    const {
        isLoading,
        paymentMethods,
        sortedPaymentMethods,
        defaultPaymentMethodId,
    } = usePaymentMethods();

    useTopBar({
        backUrl: Route.Settings,
    });
</script>

<div class="flex flex-col flex-1">
    <div class="px-3 py-6">
        <h1 class="font-bold text-2xl mb-6">{$t("payment_methods.payment_methods")}</h1>
        {#if $isLoading}
            <div class="flex justify-center mt-16">
                <Loader />
            </div>
        {:else if $paymentMethods.length === 0}
            <div class="mt-16">
                <EmptyPlaceholder>
                    <p>{$t("payment_methods.no_data")}</p>
                </EmptyPlaceholder>
            </div>
        {:else}
            <ul class="grid grid-cols-1 gap-3">
                {#each $sortedPaymentMethods as paymentMethod}
                    {#if paymentMethod.card}
                        <li>
                            <a
                                href={`${Route.PaymentMethods}/${paymentMethod.id}`}
                                class="w-full text-left"
                            >
                                <CreditCardItem
                                    card={paymentMethod.card}
                                    isDefault={paymentMethod.id === $defaultPaymentMethodId}
                                />
                            </a>
                        </li>
                    {/if}
                {/each}
            </ul>
        {/if}
    </div>
    <div class="sticky bottom-0 mt-auto bg-white border-t px-3 py-6">
        <Button
            href={Route.PaymentMethodsCreate}
            class="w-full"
        >
            <span class="font-theme uppercase text-3xl truncate">{$t("payment_methods.add_payment_method")}</span>
        </Button>
    </div>
</div>
