<script lang="ts">
    import Stripe from "stripe";
    import { onMount } from "svelte";

    import SolarCardLinear from "~icons/solar/card-linear";
    import SolarInfoCircleLinear from "~icons/solar/info-circle-linear";
    import SolarTrashBin2Linear from "~icons/solar/trash-bin-2-linear";
    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import Badge from "$components/ui/Badge.svelte";
    import Button from "$components/ui/Button.svelte";
    import ConfirmDialog from "$components/ui/ConfirmDialog.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { CREDIT_CARD_NAMES } from "$lib/utils/payment-methods";
    import { Route } from "$types/routes";

    let paymentMethod: Stripe.PaymentMethod | null = null;
    let defaultPaymentMethodId: string | null = null;
    let isLoading = true;
    let isSettingDefaultPaymentMethod = false;
    let isConfirmDeleteDialogVisible = false;
    let isDeletingPaymentMethod = false;

    $: isDefaultPaymentMethod = paymentMethod?.id === defaultPaymentMethodId;
    $: cardName = CREDIT_CARD_NAMES[paymentMethod?.card?.brand || ""] || paymentMethod?.card?.brand;
    $: isExpired = paymentMethod?.card?.exp_year && paymentMethod?.card?.exp_month
        ? new Date(paymentMethod.card.exp_year, paymentMethod.card.exp_month) < new Date()
        : false;

    useTopBar({
        backUrl: Route.PaymentMethods,
    });

    const loadDefaultPaymentMethod = async () => {
        const { data } = await api.get<Stripe.Customer>("/payments/customers");
        defaultPaymentMethodId = data.invoice_settings.default_payment_method as string;
    };

    const loadPaymentMethod = async () => {
        try {
            isLoading = true;
            const { data } = await api.get<Stripe.PaymentMethod>("/payments/methods", {
                params: {
                    id: $page.params.id,
                },
            });
            paymentMethod = data;
        } finally {
            isLoading = false;
        }
    };

    const setAsDefaultPaymentMethod = async () => {
        if (!paymentMethod) {
            return;
        }

        try {
            isSettingDefaultPaymentMethod = true;
            await api.post("/payments/methods/default", {
                payment_method_id: paymentMethod.id,
            });
            defaultPaymentMethodId = paymentMethod.id;
        } finally {
            isSettingDefaultPaymentMethod = false;
        }
    };

    const deletePaymentMethod = async () => {
        isConfirmDeleteDialogVisible = false;

        if (!paymentMethod) {
            return;
        }

        try {
            isDeletingPaymentMethod = true;
            await api.delete("/payments/methods", {
                data: {
                    payment_method_id: paymentMethod.id,
                },
            });
            await goto(Route.PaymentMethods);
        } finally {
            isConfirmDeleteDialogVisible = false;
            isDeletingPaymentMethod = false;
        }
    };

    onMount(() => {
        loadDefaultPaymentMethod();
        loadPaymentMethod();
    });
</script>

{#if isLoading}
    <div class="flex justify-center mt-16">
        <Loader />
    </div>
{:else if paymentMethod}
    <div class="px-3 py-6">
        <div class="flex items-center justify-between gap-3 mb-3">
            <h1 class="font-bold text-4xl capitalize truncate">{cardName}</h1>

            {#if isDefaultPaymentMethod}
                <Badge>{$t("payment_methods.default")}</Badge>
            {/if}
        </div>
        <div class="flex items-center gap-3">
            {#each new Array(3) as i}
                <p
                    class="flex items-center gap-1 opacity-80"
                    data-index={i}
                >
                    {#each new Array(4) as j}
                        <p
                            class="w-2 h-2 bg-black rounded-full"
                            data-index={j}
                        />
                    {/each}
                </p>
            {/each}
            <p class="text-2xl font-card leading-none">{paymentMethod.card?.last4}</p>
        </div>

        {#if isExpired}
            <div class="mt-6">
                <article class="text-red-500 text-xl font-medium rounded-2xl flex items-center gap-2">
                    <SolarInfoCircleLinear />
                    <span>{$t("payment_methods.card_expired")}</span>
                </article>
            </div>
        {/if}

        <div class="my-6">
            <p class="text-sm">{$t("payment_methods.expiry_date")}</p>
            <p class="text-lg font-medium">{paymentMethod.card?.exp_month} / {paymentMethod.card?.exp_year}</p>
        </div>

        <ul class="grid grid-cols-1 gap-3">
            {#if !isDefaultPaymentMethod}
                <li>
                    <Button
                        on:click={setAsDefaultPaymentMethod}
                        loading={isSettingDefaultPaymentMethod}
                        disabled={isSettingDefaultPaymentMethod}
                        class="w-full bg-white active:bg-slate-100 text-black border justify-start px-4"
                    >
                        {#if !isSettingDefaultPaymentMethod}
                            <SolarCardLinear />
                        {/if}
                        <span>{$t("payment_methods.set_default")}</span>
                    </Button>
                </li>
            {/if}
            <li>
                <Button
                    on:click={() => isConfirmDeleteDialogVisible = true}
                    loading={isDeletingPaymentMethod}
                    disabled={isDeletingPaymentMethod}
                    class="w-full bg-white active:bg-slate-100 text-red-500 border justify-start px-4"
                >
                    {#if !isDeletingPaymentMethod}
                        <SolarTrashBin2Linear />
                    {/if}
                    <span>{$t("payment_methods.delete_payment_method")}</span>
                </Button>
            </li>
        </ul>
    </div>
{/if}

<ConfirmDialog
    bind:visible={isConfirmDeleteDialogVisible}
    on:confirm={deletePaymentMethod}
    dangerMode
>
    <p>{$t("payment_methods.confirm_delete")}</p>
</ConfirmDialog>

<style>
    .font-card {
        font-family: 'Courier New', serif;
    }
</style>
