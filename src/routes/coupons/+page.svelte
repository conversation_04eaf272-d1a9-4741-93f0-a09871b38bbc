<script lang="ts">
    import Stripe from "stripe";
    import { onMount } from "svelte";

    import { goto } from "$app/navigation";
    import Button from "$components/ui/Button.svelte";
    import Dialog from "$components/ui/Dialog.svelte";
    import ErrorDialog from "$components/ui/ErrorDialog.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { getPromoLabel, loadPromoCode, PROMO_CODE_QUERY_PARAM, savePromoCode, savePromoCodeToLocalStorage } from "$lib/utils/promo-codes.client";
    import { Route } from "$types/routes";

    let isDialogVisible = false;
    let isErrorDialogVisible = false;
    let promoCode: Stripe.PromotionCode | null = null;
    let isClaiming = false;

    $: promoLabel = promoCode ? getPromoLabel(promoCode) : "";

    const getPromoCodeFromUrl = () => {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(PROMO_CODE_QUERY_PARAM);
    };

    const claimPromoCode = async () => {
        if (!promoCode) {
            return;
        }

        try {
            isClaiming = true;
            await savePromoCode(promoCode.code);
        } finally {
            await goto(Route.Home);
            isClaiming = false;
        }
    };

    onMount(async () => {
        const code = getPromoCodeFromUrl();

        if (!code) {
            await goto(Route.Home);
            return;
        }

        promoCode = await loadPromoCode(code);

        if (!promoCode) {
            isErrorDialogVisible = true;
            return;
        }

        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
            savePromoCodeToLocalStorage(code);
        }

        isDialogVisible = true;
    });
</script>

<div>
    <div class="flex justify-center mt-16">
        <Loader />
    </div>
    <Dialog bind:visible={isDialogVisible}>
        <div class="flex-1 text-white flex flex-col">
            <p class="text-white text-4xl text-center font-bold mt-16">{$t("coupons.intro_message")}</p>

            <article class="border-4 border-dashed border-primary-950/20 bg-primary-950/10 rounded-2xl my-16 p-6">
                <p class="text-4xl text-center font-bold tracking-wider uppercase">{promoCode?.code}</p>
            </article>

            <p class="text-center text-xl"><b>{promoLabel}</b> {$t("coupons.on_your_next_order")}</p>

            <Button
                on:click={claimPromoCode}
                loading={isClaiming}
                class="w-full bg-white active:bg-slate-100 text-primary-500 font-theme uppercase text-3xl mt-auto"
            >
                <span class="truncate">{$t("coupons.claim_coupon")}</span>
            </Button>
        </div>
    </Dialog>
    <ErrorDialog
        bind:visible={isErrorDialogVisible}
        on:close={() => goto(Route.Home)}>
        <p slot="title">{$t("coupons.invalid_coupon")}</p>
        <p slot="content">{$t("coupons.invalid_coupon_description")}</p>
    </ErrorDialog>
</div>
