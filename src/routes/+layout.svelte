<script lang="ts">
    import "../app.css";

    import jsCookie from "js-cookie";
    import PostHog from "posthog-js";
    import { onMount } from "svelte";

    import { afterNavigate, beforeNavigate } from "$app/navigation";
    import AppInstallationBanner from "$components/ui/AppInstallationBanner.svelte";
    import TopBar from "$components/ui/TopBar.svelte";
    import { env } from "$env/dynamic/public";
    import { openModalCount } from "$lib/composables/useModal";
    import { LANGUAGE_COOKIE_NAME, locale } from "$lib/i18n";
    import { onAuthStateChange } from "$lib/utils/auth";
    import { PostHogEvent } from "$types/posthog";

    beforeNavigate(() => PostHog.capture(PostHogEvent.PageLeave));
    afterNavigate(() => PostHog.capture(PostHogEvent.PageView));

    if (env.PUBLIC_POSTHOG_API_KEY) {
        PostHog.init(
            env.PUBLIC_POSTHOG_API_KEY,
            {
                api_host: "https://us.i.posthog.com",
                person_profiles: "identified_only",
                capture_pageview: false,
                capture_pageleave: false,
            },
        );
    }

    onMount(() => {
        onAuthStateChange();

        const languageSubscription = locale.subscribe((value) => {
            jsCookie.set(LANGUAGE_COOKIE_NAME, value);
        });

        const modalSubscription = openModalCount.subscribe((value) => {
            document.body.style.overflow = value > 0 ? "hidden" : "";
        });

        return () => {
            languageSubscription();
            modalSubscription();
        };
    });
</script>

<TopBar />
<main class="relative flex flex-col w-full flex-1 bg-white max-w-app mx-auto">
    <slot />
</main>
<AppInstallationBanner />
