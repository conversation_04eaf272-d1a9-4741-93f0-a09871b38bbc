import { addTranslations, setLocale, setRoute } from "$lib/i18n";
import { user } from "$lib/stores/user";

import type { LayoutLoad } from "./$types";

export const load: LayoutLoad = async ({ data }) => {
    const { i18n, translations, user: _user } = data;
    const { locale, route } = i18n;

    user.set(_user || null);

    addTranslations(translations);

    await setRoute(route);
    await setLocale(locale);

    return i18n;
};
