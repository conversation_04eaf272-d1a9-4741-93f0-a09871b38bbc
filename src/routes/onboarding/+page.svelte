<script lang="ts">
    import { goto } from "$app/navigation";
    import Button from "$components/ui/Button.svelte";
    import Stepper from "$components/ui/Stepper.svelte";
    import Step1Image from "$lib/assets/images/onboarding/step-1.svg";
    import Step2Image from "$lib/assets/images/onboarding/step-2.svg";
    import Step3Image from "$lib/assets/images/onboarding/step-3.svg";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { Route } from "$types/routes";

    let currentStep = 0;

    const steps = [
        {
            icon: Step2Image,
            title: $t("onboarding.step1"),
            description: $t("onboarding.step1_description"),
        },
        {
            icon: Step1Image,
            title: $t("onboarding.step2"),
            description: $t("onboarding.step2_description"),
        },
        {
            icon: Step3Image,
            title: $t("onboarding.step3"),
            description: $t("onboarding.step3_description"),
        },
    ];

    $: translateStyle = `transform: translateX(-${currentStep * 100}%)`;

    useTopBar({
        isVisible: false,
    });

    const nextStep = async () => {
        if (currentStep >= steps.length - 1) {
            await supabase.auth.updateUser({
                data: {
                    has_seen_onboarding: true,
                },
            });

            await goto(Route.Home);
            return;
        }

        currentStep++;
    };
</script>

<div class="flex flex-col flex-1 bg-primary-500">
    <div class="flex flex-1 overflow-hidden pb-6">
        {#each steps as step, index}
            <article
                class="flex flex-col min-w-full max-w-app text-white text-center transition-transform"
                style={translateStyle}
            >
                <div class="flex justify-center max-w-[450px] mx-auto px-12">
                    <img
                        src={step.icon}
                        alt={`step-${index + 1}`}
                        class="w-full rounded-b-3xl bg-primary-100"
                    />
                </div>

                <div class="max-w-[450px] mx-auto">
                    <h2 class="font-semibold text-2xl xxs:text-3xl mt-6 mb-3 px-6">{step.title}</h2>
                    <p class="text-sm xxs:text-lg px-6">{step.description}</p>
                </div>
            </article>
        {/each}
    </div>

    <div class="pb-6 px-6">
        <Stepper
            step={currentStep}
            max={steps.length}
            class="mb-6"
        />

        <Button
            on:click={nextStep}
            class="w-full bg-white active:bg-slate-100 text-primary-500 font-theme uppercase text-3xl"
        >
            {#if currentStep < steps.length - 1}
                {$t("onboarding.next")}
            {:else}
                {$t("onboarding.discover")}
            {/if}
        </Button>
    </div>
</div>
