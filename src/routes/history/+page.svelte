<script lang="ts">
    import { onMount } from "svelte";

    import OrderItem from "$components/OrderItem.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { useActiveOrder } from "$lib/composables/useActiveOrder";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { OrderStatus } from "$types/orders";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    let geolocalizedOrders: Array<{ order: Tables<"orders">, location: Tables<"station_locations"> | null }> = [];
    let isLoadingOrders = true;

    useTopBar({
        backUrl: Route.Settings,
    });

    const {
        order: activeOrder,
        location: activeOrderLocation,
        isLoading: isLoadingActiveOrder,
    } = useActiveOrder();

    const loadOrders = async () => {
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
            return;
        }

        const { error, data } = await supabase.from("orders")
            .select()
            .eq("user_id", session.user.id)
            .eq("status", OrderStatus.Completed)
            .order("started_at", { ascending: false })
            .limit(3);

        if (error) {
            console.error(error);
            return;
        }

        const { error: locationsError, data: locations } = await supabase
            .from("station_locations")
            .select()
            .in("station_id", data.map((order) => order.station_id));

        if (locationsError) {
            console.error(locationsError);
            return;
        }

        geolocalizedOrders = data.map((order) => {
            const location = locations.find(location => location.station_id === order.station_id) || null;

            return {
                order,
                location,
            };
        });
    };

    const load = async () => {
        try {
            isLoadingOrders = true;
            await loadOrders();
        } catch (err) {
            console.error(err);
        } finally {
            isLoadingOrders = false;
        }
    };

    onMount(() => {
        load();
    });
</script>

<div class="flex flex-col flex-1">
    <div class="px-3 py-6">
        <h1 class="font-bold text-2xl mb-6">{$t("history.history")}</h1>

        {#if isLoadingOrders || $isLoadingActiveOrder}
            <div class="flex justify-center mt-16">
                <Loader />
            </div>
        {:else if !$activeOrder && geolocalizedOrders.length === 0}
            <EmptyPlaceholder>
                <p>{$t("history.no_data")}</p>
            </EmptyPlaceholder>
        {:else}
            {#if $activeOrder}
                <div class="mb-10">
                    <p class="font-medium opacity-50 mb-3">{$t("history.active_order")}</p>
                    <OrderItem
                        order={$activeOrder}
                        location={$activeOrderLocation}
                    />
                </div>
            {/if}

            {#if geolocalizedOrders.length > 0}
                <div class="font-medium flex items-center justify-between mb-3">
                    <p class="opacity-50">{$t("history.completed_orders")}</p>
                    <a
                        href={Route.HistoryCompleted}
                        class="text-primary-500"
                    >
                        {$t("history.view_all")}
                    </a>
                </div>

                <ul class="grid grid-cols-1 gap-3">
                    {#each geolocalizedOrders as geolocalizedOrder}
                        <li class="col-span-1">
                            <OrderItem
                                order={geolocalizedOrder.order}
                                location={geolocalizedOrder.location}
                            />
                        </li>
                    {/each}
                </ul>
            {/if}
        {/if}
    </div>
</div>
