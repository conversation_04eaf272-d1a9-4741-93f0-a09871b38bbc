<script lang="ts">
    import { onMount } from "svelte";

    import OrderItem from "$components/OrderItem.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import InfiniteScroll from "$components/ui/InfiniteScroll.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { usePagination } from "$lib/composables/usePagination";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { OrderStatus } from "$types/orders";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    type GelolocalizedOrder = {
        order: Tables<"orders">
        location: Tables<"station_locations"> | null
    };

    let geolocalizedOrders: GelolocalizedOrder[] = [];
    let isLoading = true;
    const {
        hasNextPage,
        getPagination,
        nextPage,
    } = usePagination();

    useTopBar({
        backUrl: Route.History,
    });

    const loadOrders = async () => {
        if (!$hasNextPage) {
            return;
        }

        try {
            isLoading = geolocalizedOrders.length === 0;
            const { from, to } = getPagination();
            const { data: { session } } = await supabase.auth.getSession();

            if (!session) {
                return;
            }

            const { error, data: orders } = await supabase
                .from("orders")
                .select()
                .eq("user_id", session.user.id)
                .in("status", [OrderStatus.Completed, OrderStatus.LateCompleted])
                .order("started_at", { ascending: false })
                .range(from, to);

            if (error) {
                console.error(error);
                return;
            }

            const { error: locationsError, data: locations } = await supabase
                .from("station_locations")
                .select()
                .in("station_id", orders.map((order) => order.station_id));

            if (locationsError) {
                console.error(locationsError);
                return;
            }

            geolocalizedOrders = [
                ...geolocalizedOrders,
                ...orders.map((order) => {
                    const location = locations.find(location => location.station_id === order.station_id) || null;

                    return {
                        order,
                        location,
                    };
                }),
            ];

            nextPage(geolocalizedOrders.length);
        } catch (err) {
            console.error(err);
        } finally {
            isLoading = false;
        }
    };

    onMount(() => {
        loadOrders();
    });
</script>

<div class="flex flex-col flex-1">
    <div class="px-3 py-6">
        <h1 class="font-bold text-2xl mb-6">{$t("history.completed_orders")}</h1>

        {#if isLoading}
            <div class="flex justify-center mt-16">
                <Loader />
            </div>
        {:else if geolocalizedOrders.length === 0}
            <EmptyPlaceholder>
                <p>{$t("history.no_data")}</p>
            </EmptyPlaceholder>
        {:else}
            <InfiniteScroll on:bottom={loadOrders}>
                <ul class="grid grid-cols-1 gap-3">
                    {#each geolocalizedOrders as geolocalizedOrder}
                        <li class="col-span-1">
                            <OrderItem
                                order={geolocalizedOrder.order}
                                location={geolocalizedOrder.location}
                            />
                        </li>
                    {/each}
                </ul>
            </InfiniteScroll>
        {/if}
    </div>
</div>
