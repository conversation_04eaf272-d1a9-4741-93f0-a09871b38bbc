import { error, json, type RequestHandler } from "@sveltejs/kit";

import { supabaseAdmin } from "$lib/services/supabase-admin";
import { isCouponValid, loadPromoCode } from "$lib/utils/promo-codes.server";

export const GET: RequestHandler = async ({ url, locals }) => {
    const code = url.searchParams.get("code");

    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    if (!code) {
        throw error(400, "Missing required parameter: code");
    }

    const promoCode = await loadPromoCode(code);

    if (!promoCode) {
        return json(null);
    }

    const { data: coupon, error: couponError } = await supabaseAdmin
        .from("coupons")
        .select()
        .eq("code", code)
        .single();

    if (couponError) {
        return json(null);
    }

    const isValid = await isCouponValid(coupon, locals.user);

    if (!isValid) {
        return json(null);
    }

    return json(promoCode);
};
