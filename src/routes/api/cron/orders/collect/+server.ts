import { error, json, type RequestHandler } from "@sveltejs/kit";

import { env } from "$env/dynamic/private";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { captureOrder } from "$lib/utils/orders";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import { OrderStatus, OrderType } from "$types/orders";
import type { Tables } from "$types/supabase";

const updateFailedOrder = async (orderId: string) => {
    await supabaseAdmin
        .from("orders")
        .update({
            status: OrderStatus.LateFailed,
            ended_at: new Date().toISOString(),
            amount_charged_cents: 0,
        })
        .eq("id", orderId);
};

export const POST: RequestHandler = async ({ request }) => {
    const password = request.headers.get("Authorization");

    if (password !== env.CRON_JOB_PASSWORD) {
        throw error(401, "Unauthorized");
    }

    let collectedOrderAmount = 0;
    let failedOrderAmount = 0;
    const errors: unknown[] = [];

    const collectFunds = async (order: Tables<"orders">) => {
        try {
            await captureOrder({
                order,
                priceCents: BATTERY_PURCHASE_PRICE * 100,
                orderStatus: OrderStatus.LateCompleted,
            });

            collectedOrderAmount++;
        } catch (err) {
            await updateFailedOrder(order.id);
            errors.push(err);
            failedOrderAmount++;
        }
    };

    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    const { error: ordersError, data: orders } = await supabaseAdmin
        .from("orders")
        .select()
        .eq("status", OrderStatus.Ongoing)
        .eq("type", OrderType.Rental)
        .lte("started_at", threeDaysAgo.toISOString())
        .is("ended_at", null);

    if (ordersError) {
        throw error(500, ordersError.message);
    }

    await Promise.allSettled(
        orders.map((order) => collectFunds(order)),
    );

    return json({
        message: `
            ${collectedOrderAmount} order(s) have been collected
            \n
            ${failedOrderAmount} order(s) couldn't be collected
        `,
        errors,
    });
};
