import { error, json, type RequestHandler } from "@sveltejs/kit";
import dayjs from "dayjs";

import { env } from "$env/dynamic/private";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { cancelOrder, captureOrder } from "$lib/utils/orders";
import { computeOrderPriceCents } from "$lib/utils/payments";
import { getHourlyPriceCents } from "$lib/utils/pricing";
import type { Tables } from "$types/supabase";

export const POST: RequestHandler = async ({ request }) => {
    const password = request.headers.get("Authorization");

    if (password !== env.CRON_JOB_PASSWORD) {
        throw error(401, "Unauthorized");
    }

    let resolvedOrderAmount = 0;
    let failedOrderAmount = 0;
    const errors: unknown[] = [];

    const { error: ordersError, data } = await supabaseAdmin
        .from("orders_with_unresolved_end")
        .select();

    if (ordersError) {
        throw error(500, ordersError.message);
    }

    // Bypass supabase views typing issues
    const orders = data as Tables<"orders">[];

    const resolveOrder = async (order: Tables<"orders">) => {
        const startedAtObject = dayjs(order.started_at);
        // If the order was started more than an hour ago, we give a free hour due to the time it took to resolve the order
        const startedAt = startedAtObject.isBefore(dayjs().subtract(1, "hour"))
            ? startedAtObject.subtract(1, "hour").toISOString()
            : dayjs(order.started_at).toISOString();

        try {
            const hourlyPriceCents = getHourlyPriceCents(order);
            const priceCents = computeOrderPriceCents(startedAt, hourlyPriceCents);
            await captureOrder({
                order,
                priceCents,
            });
            resolvedOrderAmount++;
        } catch (err) {
            failedOrderAmount++;
            errors.push(err);

            await cancelOrder({
                paymentIntentId: order.stripe_payment_intent_id,
                carkuOrderId: order.carku_order_id,
                reason: "abandoned",
            });
        }
    };

    await Promise.allSettled(
        orders.map((order) => resolveOrder(order)),
    );

    return json({
        message: `
            ${resolvedOrderAmount} order(s) have been resolved
            \n
            ${failedOrderAmount} order(s) couldn't be resolved
        `,
        errors,
    });
};
