import { error, json, type <PERSON>quest<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import dayjs from "dayjs";

import { env } from "$env/dynamic/private";
import { greetings, sendSMS } from "$lib/services/phone";
import { supabaseAdmin } from "$lib/services/supabase-admin";

export const POST: RequestHandler = async ({ request }) => {
    const password = request.headers.get("Authorization");

    if (password !== env.CRON_JOB_PASSWORD) {
        throw error(401, "Unauthorized");
    }

    // Get all users who have not been notified yet
    const { data: usersWithoutOrders, error: usersError } = await supabaseAdmin
        .from("users_without_orders")
        .select()
        .is("raw_user_meta_data->>notification_users_without_orders", null)
        .gte("created_at", dayjs().subtract(5, "day").toISOString());

    if (usersError) {
        throw error(500, usersError.message);
    }

    const sentTo: string[] = [];
    const errors: unknown[] = [];

    // Notify users
    for (const user of usersWithoutOrders) {
        try {
            await sendSMS({
                to: user.phone!,
                message: greetings,
            });

            await supabaseAdmin.auth.admin.updateUserById(user.id!, {
                user_metadata: {
                    notification_users_without_orders: dayjs().toISOString(),
                },
            });

            sentTo.push(user.phone!);
        } catch (err) {
            console.error(err);
            errors.push(err);
        }
    }

    return json({
        message: `
            ${sentTo.length} SMS sent.
            \n
            ${sentTo.join(", ")}
        `,
        errors,
    });
};
