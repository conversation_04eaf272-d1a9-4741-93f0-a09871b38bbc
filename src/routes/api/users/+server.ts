import { error, json, type RequestHandler } from "@sveltejs/kit";

import { supabaseAdmin } from "$lib/services/supabase-admin";
import { OrderStatus } from "$types/orders";

export const DELETE: RequestHandler = async ({ locals }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { data: hasOngoingOrder } = await supabaseAdmin.from("orders")
        .select("id")
        .eq("status", OrderStatus.Ongoing)
        .eq("user_id", locals.user.id)
        .maybeSingle();

    if (hasOngoingOrder) {
        throw error(500, "Customer has an ongoing order");
    }

    await supabaseAdmin.auth.admin.deleteUser(locals.user.id);

    return json({ message: "Success" });
};
