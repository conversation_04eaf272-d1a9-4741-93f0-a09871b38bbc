import { error, json, type RequestEvent, type RequestHand<PERSON> } from "@sveltejs/kit";

import { callBatteryStation } from "$lib/services/carku";
import { cancelOrder, createOrder } from "$lib/utils/orders";
import { applyTaxes, capturePaymentIntent } from "$lib/utils/payments";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import { OrderType } from "$types/orders";

export const POST: RequestHandler = async ({ locals, request, url }: RequestEvent) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { station_id, payment_method_id, cable } = await request.json();

    let intentId: string, orderId: string;

    try {
        const data = await createOrder({
            user: locals.user,
            paymentMethodId: payment_method_id,
            orderType: OrderType.Purchase,
            stationId: station_id,
            cable,
            returnUrl: url.origin,
        });

        intentId = data.intentId;
        orderId = data.orderId;

        const totalPriceCents = await applyTaxes(BATTERY_PURCHASE_PRICE * 100);
        await capturePaymentIntent(intentId, totalPriceCents);
    } catch (err) {
        console.error(err);
        throw error(500, err as Error);
    }

    try {
        await callBatteryStation(
            "popup_confirm_with_cable",
            station_id,
            {
                cable,
                orderid: orderId,
            },
        );
    } catch (err) {
        console.error(err);
        await cancelOrder({ paymentIntentId: intentId, carkuOrderId: orderId, reason: "abandoned" });

        throw error(500, err as Error);
    }

    return json({ message: "Success" });
};
