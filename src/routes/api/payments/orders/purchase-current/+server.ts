import { error, json, type RequestEvent, type RequestHandler } from "@sveltejs/kit";

import { supabaseAdmin } from "$lib/services/supabase-admin";
import { applyTaxes, capturePaymentIntent, generateOrderId } from "$lib/utils/payments";
import { BATTERY_PURCHASE_PRICE } from "$lib/utils/pricing";
import { OrderStatus, OrderType } from "$types/orders";
import type { Tables } from "$types/supabase";

const loadActiveOrder = async (userId: string) => {
    const { error, data: order } = await supabaseAdmin
        .from("orders")
        .select()
        .eq("user_id", userId)
        .eq("status", OrderStatus.Ongoing)
        .eq("type", OrderType.Rental)
        .is("ended_at", null)
        .maybeSingle();

    if (error) {
        throw new Error(error.message);
    }

    if (!order) {
        throw new Error("No active order found");
    }

    return order;
};

const cancelActiveOrder = async (orderId: string) => {
    const { error } = await supabaseAdmin.from("orders")
        .update({
            status: OrderStatus.Cancelled,
            ended_at: new Date().toISOString(),
        })
        .eq("id", orderId);

    if (error) {
        console.error(error.message);
        throw new Error(error.message);
    }
};

const createPurchaseOrder = async (order: Tables<"orders">) => {
    const orderId = generateOrderId();

    const { error } = await supabaseAdmin
        .from("orders")
        .insert({
            user_id: order.user_id,
            station_id: order.station_id,
            stripe_customer_id: order.stripe_customer_id,
            stripe_payment_intent_id: order.stripe_payment_intent_id,
            carku_order_id: orderId,
            cable_type: order.cable_type,
            type: OrderType.Purchase,
            status: OrderStatus.Completed,
            amount_charged_cents: Math.round(BATTERY_PURCHASE_PRICE * 100),
            started_at: new Date().toISOString(),
            ended_at: new Date().toISOString(),
        });

    if (error) {
        console.error(error.message);
        throw new Error(error.message);
    }
};

export const POST: RequestHandler = async ({ locals }: RequestEvent) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    try {
        const order = await loadActiveOrder(locals.user.id);
        const totalPriceCents = await applyTaxes(BATTERY_PURCHASE_PRICE * 100);
        await capturePaymentIntent(order.stripe_payment_intent_id, totalPriceCents);
        await cancelActiveOrder(order.id);
        await createPurchaseOrder(order);
    } catch (err) {
        console.error(err);
        throw error(500, err as Error);
    }

    return json({ message: "Success" });
};
