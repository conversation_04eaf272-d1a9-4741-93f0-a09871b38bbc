import { error, json, type RequestEvent, type Request<PERSON>and<PERSON> } from "@sveltejs/kit";

import { callBatteryStation } from "$lib/services/carku";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { cancelOrder, createOrder } from "$lib/utils/orders";
import { BatteryCableType } from "$types/carku";
import { OrderStatus, OrderType } from "$types/orders";

export const POST: RequestHandler = async ({ locals, request, url }: RequestEvent) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { station_id, payment_method_id, cable, promo_code } = await request.json();

    if (!station_id) {
        throw error(400, "Station id is required");
    }

    if (!Object.values(BatteryCableType).includes(cable)) {
        throw error(400, "Invalid cable type");
    }

    // Verify that the user has no ongoing order
    const { data: hasOngoingOrder } = await supabaseAdmin.from("orders")
        .select("id")
        .eq("status", OrderStatus.Ongoing)
        .eq("user_id", locals.user.id)
        .maybeSingle();

    if (hasOngoingOrder) {
        throw error(500, "Customer already has an ongoing order");
    }

    let intentId: string, orderId: string;

    try {
        const data = await createOrder({
            user: locals.user,
            paymentMethodId: payment_method_id,
            orderType: OrderType.Rental,
            stationId: station_id,
            cable,
            promoCode: promo_code,
            returnUrl: url.origin,
        });

        intentId = data.intentId;
        orderId = data.orderId;
    } catch (err) {
        console.error(err);
        throw error(500, err as Error);
    }

    // Pop the battery from the station
    try {
        await callBatteryStation(
            "popup_confirm_with_cable",
            station_id,
            {
                cable,
                orderid: orderId,
            },
        );
    } catch (err) {
        console.error(err);
        await cancelOrder({ paymentIntentId: intentId, carkuOrderId: orderId, reason: "abandoned" });

        throw error(500, err as Error);
    }

    return json({ message: "Success" });
};
