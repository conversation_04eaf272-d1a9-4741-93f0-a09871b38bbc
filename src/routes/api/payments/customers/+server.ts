import { error, json, type RequestHandler } from "@sveltejs/kit";
import Stripe from "stripe";

import { stripe } from "$lib/services/stripe";
import { getStripeCustomer } from "$lib/utils/payments";

export const GET: RequestHandler = async ({ locals }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { id: stripeCustomerId } = await getStripeCustomer(locals.user);

    try {
        const customer = await stripe.customers.retrieve(stripeCustomerId);
        return json(customer);
    } catch (err) {
        if (err instanceof Stripe.errors.StripeError) {
            throw error(500, err.message);
        }
    }

    return json(null);
};
