import { error, json, type RequestHandler } from "@sveltejs/kit";

import { stripe } from "$lib/services/stripe";
import { getStripeCustomer } from "$lib/utils/payments";

export const GET: RequestHandler = async ({ locals }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { id: stripeCustomerId } = await getStripeCustomer(locals.user);
    const customer = await stripe.customers.retrieve(stripeCustomerId);

    if (!customer || customer.deleted) {
        throw error(500, "Failed to retrieve customer");
    }

    const paymentMethodId = customer.invoice_settings.default_payment_method as string;

    if (!paymentMethodId) {
        return json(null);
    }

    const paymentMethod = await stripe.customers.retrievePaymentMethod(stripeCustomerId, paymentMethodId);

    return json(paymentMethod);
};

export const POST: RequestHandler = async ({ locals, request }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { payment_method_id } = await request.json();

    if (!payment_method_id) {
        throw error(400, "Payment method is required");
    }

    const { id: stripeCustomerId } = await getStripeCustomer(locals.user);

    await stripe.customers.update(stripeCustomerId, {
        invoice_settings: {
            default_payment_method: payment_method_id,
        },
    });

    return json({ message: "Success" });
};
