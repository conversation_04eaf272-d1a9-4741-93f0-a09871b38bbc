import { error, json, type RequestHandler } from "@sveltejs/kit";
import Strip<PERSON> from "stripe";

import { stripe } from "$lib/services/stripe";
import { getStripeCustomer } from "$lib/utils/payments";

const getPaymentMethod = async (customerId: string, paymentMethodId: string) => {
    return stripe.customers.retrievePaymentMethod(customerId, paymentMethodId);
};

const getAllPaymentMethods = async (customerId: string) => {
    const { data } = await stripe.customers.listPaymentMethods(customerId, {
        limit: 100,
    });

    return data;
};

const getDefaultPaymentMethod = async (customerId: string) => {
    const customer = await stripe.customers.retrieve(customerId);

    if (!customer || customer.deleted) {
        throw error(500, "Failed to retrieve customer");
    }

    return customer.invoice_settings.default_payment_method;
};

const setDefaultPaymentMethod = async (customerId: string, paymentMethodId: string) => {
    await stripe.customers.update(customerId, {
        invoice_settings: {
            default_payment_method: paymentMethodId,
        },
    });
};

export const GET: RequestHandler = async ({ locals, url }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const paymentMethodId = url.searchParams.get("id");
    const { id: stripeCustomerId } = await getStripeCustomer(locals.user);

    if (paymentMethodId) {
        const paymentMethod = await getPaymentMethod(stripeCustomerId, paymentMethodId);
        return json(paymentMethod);
    }

    const paymentMethods = await getAllPaymentMethods(stripeCustomerId);

    return json(paymentMethods);
};

export const POST: RequestHandler = async ({ locals, request, url }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { payment_method_id } = await request.json();

    if (!payment_method_id) {
        throw error(400, "Payment method is required");
    }

    const { id: stripeCustomerId } = await getStripeCustomer(locals.user);

    try {
        await stripe.setupIntents.create({
            customer: stripeCustomerId,
            payment_method: payment_method_id,
            automatic_payment_methods: {
                enabled: true,
                allow_redirects: "always",
            },
            return_url: url.origin,
            confirm: true,
        });

        const defaultPaymentMethodId = await getDefaultPaymentMethod(stripeCustomerId);
        if (!defaultPaymentMethodId) {
            await setDefaultPaymentMethod(stripeCustomerId, payment_method_id);
        }
    } catch (err) {
        if (err instanceof Stripe.errors.StripeError) {
            throw error(500, err.message);
        }
    }

    return json({ message: "Success" });
};

export const DELETE: RequestHandler = async ({ locals, request }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const { payment_method_id } = await request.json();

    if (!payment_method_id) {
        throw error(400, "Payment method is required");
    }

    try {
        await stripe.paymentMethods.detach(payment_method_id);

        const { id: stripeCustomerId } = await getStripeCustomer(locals.user);

        const defaultPaymentMethodId = await getDefaultPaymentMethod(stripeCustomerId);
        if (!defaultPaymentMethodId) {
            const [paymentMethod] = await getAllPaymentMethods(stripeCustomerId);
            await setDefaultPaymentMethod(stripeCustomerId, paymentMethod.id);
        }
    } catch (err) {
        if (err instanceof Stripe.errors.StripeError) {
            throw error(500, err.message);
        }
    }

    return json({ message: "Success" });
};
