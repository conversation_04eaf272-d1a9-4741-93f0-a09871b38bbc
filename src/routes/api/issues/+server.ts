import type { User } from "@supabase/supabase-js";
import { error, json, type RequestHandler } from "@sveltejs/kit";
import * as _Jimp from "jimp";

import { supabaseAdmin } from "$lib/services/supabase-admin";
import { ISSUE_DESCRIPTION_MAX_LENGTH } from "$lib/utils/issues";
import { IssueType } from "$types/issues";
import { OrderStatus } from "$types/orders";
import type { Tables } from "$types/supabase";

// @ts-expect-error This is required for <PERSON><PERSON> to work in Cloudflare Workers
const Jimp = (typeof self !== "undefined") ? (self.Jimp || _Jimp) : _Jimp.default;

const MAX_WIDTH = 1280;
const MAX_SIZE = 5 * 1024 * 1024; // 5 MB

const getJpegFileName = (file: File) => {
    const tab = file.name.split(".");
    tab.pop();
    const filename = tab.join(".") + ".jpg";
    return filename.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
};

const isImageCompressed = async (file: File) => {
    const arrayBuffer = await file.arrayBuffer();
    const image = await Jimp.read(Buffer.from(arrayBuffer));
    return image.getWidth() <= MAX_WIDTH && image.getMIME() === Jimp.MIME_JPEG && arrayBuffer.byteLength <= MAX_SIZE;
};

const compressImage = async (file: File) => {
    if (await isImageCompressed(file)) {
        return file.arrayBuffer();
    }

    const image = await Jimp.read(Buffer.from(await file.arrayBuffer()));

    const width = image.getWidth() > MAX_WIDTH ? MAX_WIDTH : image.getWidth();
    image.resize(width, Jimp.AUTO);
    image.quality(50);

    return image.getBufferAsync(Jimp.MIME_JPEG);
};

const uploadFiles = async (issue: Tables<"issues">, files: File[]) => {
    for (const file of files) {
        const arrayBuffer = await compressImage(file);

        if (arrayBuffer.byteLength > MAX_SIZE) {
            throw new Error("File exceeds the maximum size of 5 MB");
        }

        const filename = getJpegFileName(file);

        const { error: fileError } = await supabaseAdmin
            .storage
            .from("issues")
            .upload(`${issue.id}/${filename}`, arrayBuffer, { contentType: "image/jpeg" });

        if (fileError) {
            throw new Error(fileError.message);
        }
    }
};

const createIssue = async (user: User, type: IssueType, description: string) => {
    const { data: ongoingOrder } = await supabaseAdmin
        .from("orders")
        .select("id")
        .eq("status", OrderStatus.Ongoing)
        .eq("user_id", user.id)
        .maybeSingle();

    const { data: issue, error: issueError } = await supabaseAdmin
        .from("issues")
        .insert({
            user_id: user.id,
            order_id: ongoingOrder?.id,
            type,
            description: description ? description.trim().slice(0, ISSUE_DESCRIPTION_MAX_LENGTH) : null,
        })
        .select()
        .single();

    if (issueError) {
        throw new Error(issueError.message);
    }

    return issue;
};

const cancelIssue = async (issue: Tables<"issues">) => {
    await supabaseAdmin
        .from("issues")
        .delete()
        .eq("id", issue.id);

    const { data: files } = await supabaseAdmin
        .storage
        .from("issues")
        .list(issue.id);

    if (!files) {
        return;
    }

    await supabaseAdmin
        .storage
        .from("issues")
        .remove(files.map((file) => `${issue.id}/${file.name}`));
};

export const POST: RequestHandler = async ({ locals, request }) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const formData = await request.formData();
    const type = formData.get("type") as IssueType;
    const description = formData.get("description") as string;

    const files = formData.getAll("file") as File[];

    if (!Object.values(IssueType).includes(type)) {
        throw error(400, "Invalid issue type");
    }

    if (type === IssueType.Other && description.trim().length === 0) {
        throw error(400, "Description is required");
    }

    let issue: Tables<"issues">;

    try {
        issue = await createIssue(locals.user, type, description);
    } catch (err) {
        console.error(err);
        throw error(500, err as Error);
    }

    try {
        await uploadFiles(issue, files);
    } catch (err) {
        console.error(err);
        await cancelIssue(issue);
        throw error(500, err as Error);
    }

    return json({ message: "Success" });
};
