import { error, json, type RequestEvent, type RequestHandler } from "@sveltejs/kit";

import { onChargingRules } from "$lib/events/stripe/charging_rules";
import { onCollectPaymentMethodSuccess } from "$lib/events/stripe/collect_paymentMethod_success";
import { onCollectSetupIntentSuccess } from "$lib/events/stripe/collect_setupIntent_success";
import { onConnectionToken } from "$lib/events/stripe/connection_token";
import { onCreatePaymentIntent } from "$lib/events/stripe/create_paymentIntent";
import { onCreateSetupIntent } from "$lib/events/stripe/create_setupIntent";
import { onOrderInformation } from "$lib/events/stripe/order_information";
import { onTerminalLocation } from "$lib/events/stripe/terminal_location";
import { callBatteryStation } from "$lib/services/carku";
import { cancelOrder } from "$lib/utils/orders";
import { BatteryCableType, StripeEvent, type StripePayload } from "$types/carku";
import { generateOrderId } from "$lib/utils/payments";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { OrderStatus, OrderType } from "$types/orders";
import type { Database } from "$types/supabase";

export const POST: RequestHandler = async ({ request }: RequestEvent) => {

  try {
    const query = await request.text();
    const params = new URLSearchParams(query);
    const payload = Object.fromEntries(params) as StripePayload;
    const ACT = payload.act;
    console.log("payload received:", payload);


    const { error, data: station } = await supabaseAdmin
        .from("stations")
        .select()
        .eq("id", payload.did)
        .single();

    if (error) {
        throw new Error(error.message);
    }
    

    if (ACT === StripeEvent.ConnectionToken) {
        const { secret } = await onConnectionToken();

        return json({
            secret,
        });
    }

    if (ACT === StripeEvent.TerminalLocation) {
        const { locationId } = await onTerminalLocation();

        return json({
            location_id: locationId,
        });
    }

    if (ACT === StripeEvent.CreatePaymentIntent) {

        // If the station is free to use, we don't need to create a payment intent
        // TODO check if this is the right way to do it, 
        if (station?.is_free) {
            const orderId = generateOrderId();

            const orderData: Database["public"]["Tables"]["orders"]["Insert"] = {
              station_id: station?.id,
              stripe_payment_intent_id: "free_order",
              stripe_customer_id: "free_order",
              carku_order_id: orderId,
              cable_type: BatteryCableType.USBTypeC,
              type: OrderType.Rental,
              status: OrderStatus.Pending,
              amount_charged_cents: 0,
            };

            const { error } = await supabaseAdmin
                .from("orders")
                .insert(orderData);

            if (error) {
                throw new Error(error.message);
            }
            await callBatteryStation(
                "popup_confirm_with_cable",
                station?.id,
                {
                    cable: BatteryCableType.USBTypeC,
                    orderid: orderId,
                },
            );
            // return json(`Order ID: ${orderId}`);
						return json({
							orderId: orderId,
						});
        }
        // Get a stripe payment session, this event will be generated when the user clicks to use the card to rent.
        // The Pos machine will not check whether there is a borrowable battery at present, so it can check whether there is a
        // borrowable battery here. If there is no borrowable battery, it will return a status code of 500, and the user cannot
        // swipe the card.
        let paymentIntentSecret: string | null = null;
        try {
            const { secret } = await onCreatePaymentIntent(payload);
            paymentIntentSecret = secret;
        } catch (err) {
            throw error(500, err as Error);
        }

        return json({
            secret: paymentIntentSecret,
        });
    }

    if (ACT === StripeEvent.CollectPaymentMethodSuccess) {
        // The user has successfully authorized the card. At this time, you can verify the authorization status of the payment session and eject the battery.
        const { intentId, orderId } = await onCollectPaymentMethodSuccess(payload);
        const stationId = payload.did;

        try {
            await callBatteryStation(
                "popup_confirm_with_cable",
                stationId,
                {
                    cable: BatteryCableType.USBTypeC,
                    orderid: orderId,
                },
            );
        } catch (err) {
            await cancelOrder({ paymentIntentId: intentId, carkuOrderId: orderId, reason: "abandoned" });
            console.error("Cancelled order, could not release battery with cable", {
                paymentIntentId: intentId,
                carkuOrderId: orderId,
                reason: "abandoned",
            });

            throw error(500, err as Error);
        }

        return json(`Payment Intent ID: ${intentId}`);
    }

    if (ACT === StripeEvent.ChargingRules) {
        const { message } = await onChargingRules(payload);

        return json({
            charging_rules: message,
        });
    }

    if (ACT === StripeEvent.OrderInformation) {
        const { remark, amount, useTime } = await onOrderInformation(payload);

        return json({
            remark,
            amount,
            use_time: useTime,
        });
    }

    if (ACT === StripeEvent.CreateSetupIntent) {
        // Obtaining a stripe setupIntent object can save the changed card to a customer to facilitate subsequent
        // automatic collection. Wallet payment - Apple and Google Pay is not supported.
        const { secret } = await onCreateSetupIntent();

        return json({
            secret,
        });
    }

    if (ACT === StripeEvent.CollectSetupIntentSuccess) {
        // The user has successfully authorized using the card, at which point you can verify the authorization status of the payment session and eject the battery.
        const { setupIntentId } = await onCollectSetupIntentSuccess(payload);

        return json(`Setup ID: ${setupIntentId}`);
    }

    return json("Success");
  } catch (err) {
        // Ensure we always have a stack trace by creating an Error if needed
        const errorWithStack = err instanceof Error ? err : new Error(String(err));
        
        const errorDetails = {
            message: errorWithStack.message,
            stack: errorWithStack.stack,
            originalError: err instanceof Error ? undefined : String(err),
            request: {
                method: request.method,
                url: request.url,
                headers: Object.fromEntries(request.headers.entries()),
            },
        }
        console.error("Error in stripe servercallback:", JSON.stringify(errorDetails));
        return new Response("Internal Server Error", { status: 500 });
  }
};
