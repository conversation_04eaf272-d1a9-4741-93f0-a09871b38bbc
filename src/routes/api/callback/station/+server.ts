import { error, json, type RequestEvent, type RequestHandler } from "@sveltejs/kit";

import { type HeartBeatData, onHearBeat } from "$lib/events/station/heartbeat";
import { onPopupConfirm, type PopupConfirmData } from "$lib/events/station/popup_confirm";
import { onQueryConfirm, type QueryConfirmData } from "$lib/events/station/query_confirm";
import { onRentConfirm, type RentConfirmData } from "$lib/events/station/rent_confirm";
import { onReturnBack, type ReturnBackData } from "$lib/events/station/return_back";
import { onSyncBattery, type SyncBatteryData } from "$lib/events/station/sync_battery";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { StationEvent } from "$types/carku";

type EventData = HeartBeatData | ReturnBackData | PopupConfirmData | QueryConfirmData | RentConfirmData | SyncBatteryData;

// TODO: this should be a configuration variable
const CARKU_SERVER_IP = "************";

export const POST: RequestHandler = async ({ request, getClientAddress }: RequestEvent) => {
    const body = await request.json() as EventData;
    const ip = getClientAddress();

    if (ip !== CARKU_SERVER_IP) {
        throw error(400, `IP not allowed: ${ip}`);
    }

    console.log("body received:", body);

    switch (body.ACT) {
        case StationEvent.HeartBeat:
            await onHearBeat(body);
            break;
        case StationEvent.ReturnBack:
            await onReturnBack(body);
            break;
        case StationEvent.PopupConfirm:
            await onPopupConfirm(body);
            break;
        case StationEvent.QueryConfirm:
            await onQueryConfirm(body);
            break;
        case StationEvent.RentConfirm:
            await onRentConfirm(body);
            break;
        case StationEvent.SyncBattery:
            await onSyncBattery(body);
            break;
        default:
    }

    await supabaseAdmin
        .from("station_events")
        .insert({
            station_id: body.DIDINFO,
            data: body,
        });

    return json("success");
};
