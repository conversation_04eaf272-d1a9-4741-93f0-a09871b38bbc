import { error, json, type RequestEvent, type RequestHandler } from "@sveltejs/kit";

import { autocompleteAddress } from "$lib/services/maps";

export const GET: RequestHandler = async ({ url, locals }: RequestEvent) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const input = url.searchParams.get("input");
    const latitude = url.searchParams.get("latitude") || "0";
    const longitude = url.searchParams.get("longitude") || "0";

    if (!input) {
        throw error(400, "Missing input");
    }

    const data = await autocompleteAddress({
        input,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        language: locals.user.user_metadata.language,
    });

    return json(data);
};
