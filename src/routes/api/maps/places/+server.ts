import { error, json, type RequestEvent, type RequestHandler } from "@sveltejs/kit";

import { getPlaceDetails } from "$lib/services/maps";

export const GET: RequestHandler = async ({ url, locals }: RequestEvent) => {
    if (!locals.user) {
        throw error(401, "Unauthorized");
    }

    const placeId = url.searchParams.get("place_id");

    if (!placeId) {
        throw error(400, "Missing places id");
    }

    const data = await getPlaceDetails(placeId);

    return json(data);
};
