<script lang="ts">
    import { onMount } from "svelte";
    import { writable } from "svelte/store";

    import MdiMapMarker from "~icons/mdi/map-marker";
    import StationItem from "$components/StationItem.svelte";
    import EmptyPlaceholder from "$components/ui/EmptyPlaceholder.svelte";
    import Input from "$components/ui/Input.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import { useGeolocation } from "$lib/composables/useGeolocation";
    import { locale, t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { supabase } from "$lib/services/supabase";
    import { formatDistance } from "$lib/utils/distance";
    import { loadFavoriteStations } from "$lib/utils/favorites";
    import type { Address } from "$types/maps";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    const MIN_SEARCH_LENGTH = 3;

    const search = writable("");

    let addresses: Address[] = [];
    let timeout: ReturnType<typeof setTimeout>;
    let isSearching = false;
    let userStations: Array<{ station: Tables<"station_locations">, isFavorite: boolean }> = [];

    const { latitude, longitude } = useGeolocation();

    const loadNearbyStations = async () => {
        try {
            isSearching = true;
            const { data, error } = await supabase
                .rpc("find_stations_within_radius", {
                    lat: $latitude,
                    lng: $longitude,
                    radius: 20_000, // 20 km
                })
                .limit(10);

            if (error) {
                console.error(error);
                return;
            }

            const stationIds = data.map((s) => s.station_id);

            const { data: _stations, error: stationsError } = await supabase
                .from("station_locations")
                .select()
                .in("station_id", stationIds);

            if (stationsError) {
                console.error(stationsError);
                return;
            }

            const favoriteStations = await loadFavoriteStations(stationIds);

            userStations = data
                .map((d) => ({
                    station: _stations.find((s) => s.station_id === d.station_id)!,
                    isFavorite: favoriteStations[d.station_id],
                }))
                .filter((s) => !!s.station);
        } finally {
            isSearching = false;
        }
    };

    const autocomplete = async () => {
        if (!$search || $search.length < MIN_SEARCH_LENGTH) {
            return;
        }

        try {
            isSearching = true;
            const { data } = await api.get<Address[]>("/maps/autocomplete", {
                params: {
                    input: $search,
                    latitude: $latitude,
                    longitude: $longitude,
                },
            });
            addresses = data;
        } finally {
            isSearching = false;
        }
    };

    onMount(() => {
        loadNearbyStations();
        const unsubscribe = search.subscribe(() => {
            if (timeout) {
                clearTimeout(timeout);
            }

            timeout = setTimeout(() => {
                autocomplete();
            }, 500);
        });

        return () => {
            unsubscribe();
        };
    });
</script>

<div>
    <div class="p-3">
        <Input
            bind:value={$search}
            type="search"
            placeholder={$t("search.where_are_you_going")}
            autofocus
        />
    </div>

    {#if addresses.length > 0}
        <ul>
            {#each addresses as address}
                <li class="border-t last-of-type:border-b">
                    <a
                        href={`${Route.Home}?place_id=${address.place_id}`}
                        class="flex items-center gap-3 active:bg-slate-50 md:hover:bg-slate-50 px-3 py-2"
                    >
                        <div class="flex flex-col items-center overflow-hidden w-14">
                            <div class="bg-primary-500 w-7 aspect-square rounded-full flex items-center justify-center">
                                <MdiMapMarker
                                    width={20}
                                    height={20}
                                    class="text-white"
                                />
                            </div>
                            {#if address.distance_meters}
                                <p class="text-xs text-center opacity-80 truncate w-14 mt-1">
                                    {formatDistance(address.distance_meters, { language: $locale, unitDisplay: "short" })}
                                </p>
                            {/if}
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">{address.text}</p>
                            <p class="text-sm font-light line-clamp-2">{address.fulltext}</p>
                        </div>
                    </a>
                </li>
            {/each}
        </ul>
    {:else if isSearching}
        <div class="flex justify-center mt-8">
            <Loader />
        </div>
    {:else if userStations.length > 0}
        <ul>
            {#each userStations as { station, isFavorite }}
                <StationItem
                    {station}
                    {isFavorite}
                />
            {/each}
        </ul>
    {:else}
        <div class="text-center mt-16">
            <EmptyPlaceholder>
                {#if $search}
                    <p>{$t("search.no_results")}</p>
                {:else}
                    <p>{$t("search.start_typing")}</p>
                {/if}
            </EmptyPlaceholder>
        </div>
    {/if}
</div>
