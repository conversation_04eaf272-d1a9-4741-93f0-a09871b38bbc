import { redirect, type RequestEvent } from "@sveltejs/kit";

import { DEFAULT_LANGUAGE, LANGUAGE_COOKIE_NAME, loadTranslations, translations } from "$lib/i18n";
import { isAuthenticatedRoute, REDIRECT_COOKIE_NAME } from "$lib/utils/auth";
import { Route } from "$types/routes";

const HTTP_TEMPORARY_REDIRECT = 307;

export const load = async ({ url, cookies, locals }: RequestEvent) => {
    const { pathname } = url;

    if (!locals.user && isAuthenticatedRoute(url.pathname as Route)) {
        cookies.set(REDIRECT_COOKIE_NAME, url.pathname + url.search, {
            path: "/",
            httpOnly: false,
        });
        throw redirect(HTTP_TEMPORARY_REDIRECT, Route.Login);
    }

    const initLocale = cookies.get(LANGUAGE_COOKIE_NAME) || DEFAULT_LANGUAGE;

    await loadTranslations(initLocale, pathname);

    return {
        i18n: {
            locale: initLocale,
            route: pathname,
        },
        translations: translations.get(),
        user: locals.user,
    };
};
