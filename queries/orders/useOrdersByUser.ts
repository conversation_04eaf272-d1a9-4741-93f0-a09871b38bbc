import { useQuery } from "@tanstack/vue-query";
import { refDebounced } from "@vueuse/core";

import type { Tables } from "~/types/database.types";
import { Query, type QueryOptions } from "~/types/query";

type Data = {
  data: Tables<"orders">[];
  count: number | null;
};

type Options = QueryOptions<Data>;

export const useOrdersByUser = (userId: string, options: Options = {}) => {
  const supabase = useSupabaseClient();

  const page = ref(1);
  const perPage = ref(10);
  const sort = ref<{ column: string; direction: "desc" | "asc" }>({
    column: "created_at",
    direction: "desc",
  });
  const sortDebounced = refDebounced(sort, 500);

  watch(sortDebounced, () => {
    page.value = 1;
  });

  const query = useQuery({
    ...options,
    queryKey: [Query.ORDERS, userId, sortDebounced, page, perPage],
    queryFn: async () => {
      const { data, count, error } = await supabase
        .from("orders")
        .select("*", { count: "estimated" })
        .eq("user_id", userId)
        .order(sortDebounced.value.column, {
          ascending: sortDebounced.value.direction === "asc",
          nullsFirst: false,
        })
        .range(
          (page.value - 1) * perPage.value,
          page.value * perPage.value - 1,
        );

      if (error) {
        throw new Error(error.message);
      }

      return { data, count };
    },
  });

  return {
    ...query,
    page,
    perPage,
    sort,
  };
};
