import type { H3Event } from "h3";

import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server";
import type { Enums } from "~/types/database.types";

export const requireOrganizationRole = async (
  event: H3Event,
  organizationId: string,
  roles: Enums<"user_role">[],
) => {
  const user = await serverSupabaseUser(event);

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    });
  }

  const supabase = await serverSupabaseClient(event);

  const { data: isAllowed, error } = await supabase
    .from("user_roles")
    .select()
    .eq("user_id", user.id)
    .eq("organization_id", organizationId)
    .in("role", roles)
    .maybeSingle();

  if (error) {
    throw createError({
      statusCode: 500,
      statusMessage: "Something wrong happened",
    });
  }

  if (!isAllowed) {
    throw createError({
      statusCode: 401,
      statusMessage: "Insufficient permissions",
    });
  }
};
