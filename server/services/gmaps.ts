import { ofetch } from "ofetch";

import type { Address, PlaceDetails } from "~/types/gmaps";

type AutocompleteSuggestion = {
  placePrediction: {
    placeId: string;
    text: {
      text: string;
    };
    structuredFormat: {
      mainText: {
        text: string;
      };
      secondaryText: {
        text: string;
      };
    };
    types: string[];
    distanceMeters: number;
  };
};

type AutocompleteResponse = {
  suggestions: AutocompleteSuggestion[];
};

type PlaceDetailsResponse = {
  result: {
    name: string;
    formatted_address: string;
    geometry: {
      location: {
        lat: number;
        lng: number;
      };
    };
    address_components: Array<{
      long_name: string;
      short_name: string;
      types: string[];
    }>;
  };
  status: "OK" | "ZERO_RESULTS";
};

const DEFAULT_LANGUAGE = "en";

const mapsApi = ofetch.create({
  baseURL: "https://maps.googleapis.com/maps/api",
  params: {
    key: process.env.GOOGLE_MAPS_API_KEY,
  },
});

const placesApi = ofetch.create({
  baseURL: "https://places.googleapis.com/v1/",
  params: {
    key: process.env.GOOGLE_MAPS_API_KEY,
  },
});

export const autocompleteAddress = async ({
  input,
  latitude,
  longitude,
  language = DEFAULT_LANGUAGE,
}: {
  input: string;
  latitude: number;
  longitude: number;
  language?: string;
}): Promise<Address[]> => {
  // https://developers.google.com/maps/documentation/places/web-service/place-autocomplete?hl=fr
  const data = await placesApi<AutocompleteResponse>("/places:autocomplete", {
    method: "POST",
    body: {
      input,
      origin: {
        latitude,
        longitude,
      },
      locationBias: {
        circle: {
          center: {
            latitude,
            longitude,
          },
          radius: 20000, // 20km
        },
      },
      languageCode: language,
    },
  });

  if (!data.suggestions) {
    return [];
  }

  return data.suggestions.map((suggestion) => ({
    text: suggestion.placePrediction.structuredFormat.mainText.text,
    fulltext: suggestion.placePrediction.text.text,
    distance_meters: suggestion.placePrediction.distanceMeters,
    place_id: suggestion.placePrediction.placeId,
  }));
};

export const getPlaceDetails = async (
  placeId: string,
  language = DEFAULT_LANGUAGE,
): Promise<PlaceDetails> => {
  // https://developers.google.com/maps/documentation/places/web-service/details?hl=fr
  const data = await mapsApi<PlaceDetailsResponse>("/place/details/json", {
    method: "GET",
    params: {
      place_id: placeId,
      language,
    },
  });

  return {
    address: data.result.name,
    latitude: data.result.geometry.location.lat,
    longitude: data.result.geometry.location.lng,
    country: data.result.address_components.find((component) =>
      component.types.includes("country"),
    )?.long_name,
    city: data.result.address_components.find((component) =>
      component.types.includes("locality"),
    )?.long_name,
    postal_code: data.result.address_components.find((component) =>
      component.types.includes("postal_code"),
    )?.long_name,
  };
};
