import { z } from "zod";

import { stripe } from "~/server/services/stripe";

export const createPromoCodeParamsSchema = z.object({
  code: z
    .string()
    .regex(/^[a-zA-Z0-9]*$/, "Invalid code")
    .min(6, "Must be at least 6 characters")
    .max(20, "Must be at most 20 characters")
    .optional(),
  amount_off: z.number().gt(0).optional(),
  percent_off: z.number().gt(0).optional(),
  redeem_by: z.number().optional(),
  customer_id: z.string().optional(),
  usage_limit_per_user: z.number().gt(0).optional(),
  organization_id: z.string(),
});

type CreatePromoCodeParams = z.infer<typeof createPromoCodeParamsSchema>;

export const createPromoCode = async ({
  code,
  amount_off,
  percent_off,
  redeem_by,
  customer_id,
  usage_limit_per_user,
}: CreatePromoCodeParams) => {
  if (!amount_off && !percent_off) {
    throw new Error("Either amount_off or percent_off must be provided");
  }

  if (amount_off && percent_off) {
    throw new Error("Only one of amount_off or percent_off can be provided");
  }

  const coupon = await stripe.coupons.create({
    percent_off,
    amount_off,
    currency: "CAD",
    redeem_by,
  });

  return await stripe.promotionCodes.create({
    coupon: coupon.id,
    code,
    customer: customer_id,
    expires_at: redeem_by,
    metadata: {
      usage_limit_per_user: usage_limit_per_user ?? null,
    },
  });
};

export const deletePromoCode = (couponId: string) => {
  return stripe.coupons.del(couponId);
};

export const loadPromoCodes = async () => {
  const { data } = await stripe.promotionCodes.list({
    limit: 100,
    active: true,
  });
  return data;
};
