import { ofetch } from "ofetch";

import type { ApiOption, BatteryStationResponse } from "~/types/carku";

const api = ofetch.create({
  baseURL: "https://vgnapi.minimgr.cn/api",
  headers: {
    "content-type": "application/x-www-form-urlencoded",
  },
});

export const apiRequest = (
  opt: ApiOption,
  data: Record<string, unknown> = {},
) => {
  return api<BatteryStationResponse>("/index.php", {
    method: "POST",
    body: {
      AppID: process.env.CARKU_APP_ID,
      SecretKey: process.env.CARKU_SECRET_KEY,
      ...data,
    },
    query: {
      mod: "controllers",
      act: "mini_api_v2",
      opt,
    },
  });
};
