import { ofetch } from "ofetch";

const turnstile = ofetch.create({
  baseURL: "https://challenges.cloudflare.com/turnstile/v0",
});

export default defineEventHandler(async (event) => {
  const { token } = await readBody(event);

  if (!token) {
    throw createError({
      statusCode: 422,
      statusMessage: "Token not provided.",
    });
  }

  const formData = new FormData();
  formData.append("secret", process.env.NUXT_TURNSTILE_SECRET_KEY as string);
  formData.append("response", token);
  formData.append("remoteip", event.context.clientAddress ?? "");

  const { success } = await turnstile<{ success: boolean }>("/siteverify", {
    method: "POST",
    body: formData,
  });

  if (!success) {
    throw createError({
      statusCode: 422,
      statusMessage: "Invalid token.",
    });
  }

  return true;
});
