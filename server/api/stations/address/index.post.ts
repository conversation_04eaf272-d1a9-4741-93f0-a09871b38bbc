import { z } from "zod";

import { serverSupabaseServiceRole } from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";
import { getPlaceDetails } from "~/server/services/gmaps";

const schema = z.object({
  station_id: z.string(),
  place_id: z.string(),
});

export default defineEventHandler(async (event) => {
  const result = await readValidatedBody(event, (body) =>
    schema.safeParse(body),
  );

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: result.error.message,
    });
  }

  const { station_id: stationId, place_id: placeId } = result.data;

  const supabase = serverSupabaseServiceRole(event);

  const { data: station, error: stationError } = await supabase
    .from("stations")
    .select("id, organization_id")
    .eq("id", stationId)
    .single();

  if (stationError) {
    throw createError({
      statusCode: 500,
      statusMessage: stationError.message,
    });
  }

  if (!station.organization_id) {
    throw createError({
      statusCode: 500,
      statusMessage: "This station does not belong to your organization",
    });
  }

  await requireOrganizationRole(event, station.organization_id, ["superuser"]);

  const placeDetails = await getPlaceDetails(placeId);

  const { error: locationError } = await supabase
    .from("station_locations")
    .upsert({
      station_id: stationId,
      address: placeDetails.address,
      postal_code: placeDetails.postal_code,
      city: placeDetails.city,
      state: placeDetails.state ?? null,
      country: placeDetails.country,
      latitude: placeDetails.latitude,
      longitude: placeDetails.longitude,
      google_place_id: placeId,
    })
    .eq("id", stationId);

  if (locationError) {
    throw createError({
      statusCode: 500,
      statusMessage: locationError.message,
    });
  }

  const { error: updateStationError } = await supabase
    .from("stations")
    .update({
      latitude: placeDetails.latitude,
      longitude: placeDetails.longitude,
    })
    .eq("id", stationId);

  if (updateStationError) {
    throw createError({
      statusCode: 500,
      statusMessage: updateStationError.message,
    });
  }

  return true;
});
