import { z } from "zod";

import { serverSupabaseUser } from "#supabase/server";
import { autocompleteAddress } from "~/server/services/gmaps";

const schema = z.object({
  query: z.string(),
  latitude: z.number().optional().default(0),
  longitude: z.number().optional().default(0),
});

export default defineEventHandler(async (event) => {
  const user = await serverSupabaseUser(event);

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    });
  }

  const result = await getValidatedQuery(event, (query) =>
    schema.safeParse(query),
  );

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: result.error.message,
    });
  }

  const { query, latitude, longitude } = result.data;

  return autocompleteAddress({
    input: query,
    latitude,
    longitude,
  });
});
