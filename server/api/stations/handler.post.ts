import { apiRequest } from "~/server/services/carku";
import type { ApiOption } from "~/types/carku";

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  const { option } = getQuery<{ option: ApiOption | null }>(event);

  if (!option) {
    throw createError({
      statusCode: 400,
      statusMessage: "Missing required parameter: option",
    });
  }

  const { data } = await apiRequest(option, body);

  return data;
});
