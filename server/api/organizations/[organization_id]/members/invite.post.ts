import { z } from "zod";

import {
  serverSupabaseServiceRole,
  serverSupabaseUser,
} from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";

const schema = z.object({
  email: z.string().email("Invalid email"),
  role: z.enum(["employee", "admin"]),
});

export default defineEventHandler(async (event) => {
  const organizationId = getRouterParam(event, "organization_id");
  const user = await serverSupabaseUser(event);
  const result = await readValidatedBody(event, (body) =>
    schema.safeParse(body),
  );
  const supabase = serverSupabaseServiceRole(event);

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    });
  }

  if (!result.success || !organizationId) {
    throw createError({
      statusCode: 400,
      statusMessage:
        result.error?.message ?? "Missing required fields organizationId",
    });
  }

  await requireOrganizationRole(event, organizationId, ["superuser"]);

  const { email, role } = result.data;

  // Send the invite
  const {
    data: { user: newUser },
    error,
  } = await supabase.auth.admin.inviteUserByEmail(email);

  if (error || !newUser) {
    throw createError({
      statusCode: 500,
      statusMessage: error?.message ?? "Failed to invite user",
    });
  }

  // Insert the user role
  const { error: insertError } = await supabase.from("user_roles").insert({
    user_id: newUser.id,
    organization_id: organizationId,
    role: role,
  });

  if (insertError) {
    throw createError({
      statusCode: 500,
      statusMessage: "This user has already been invited",
    });
  }

  return newUser;
});
