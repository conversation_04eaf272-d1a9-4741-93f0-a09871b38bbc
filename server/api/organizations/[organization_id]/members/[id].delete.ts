import { serverSupabaseServiceRole } from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";

export default defineEventHandler(async (event) => {
  const organizationId = getRouterParam(event, "organization_id");
  const memberId = getRouterParam(event, "id");
  const supabase = serverSupabaseServiceRole(event);

  if (!organizationId || !memberId) {
    throw createError({
      statusCode: 400,
      statusMessage: "Missing required fields organizationId and userId",
    });
  }

  await requireOrganizationRole(event, organizationId, ["superuser"]);

  const { error } = await supabase
    .from("user_roles")
    .delete()
    .eq("user_id", memberId)
    .eq("organization_id", organizationId);

  if (error) {
    throw createError({
      statusCode: 500,
      statusMessage: "Failed to delete user",
    });
  }

  return true;
});
