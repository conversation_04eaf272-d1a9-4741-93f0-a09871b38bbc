import { z } from "zod";

import { serverSupabaseServiceRole } from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";

const schema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must be at most 50 characters")
    .optional(),
});

export default defineEventHandler(async (event) => {
  const organizationId = getRouterParam(event, "organization_id");
  const memberId = getRouterParam(event, "id");
  const supabase = serverSupabaseServiceRole(event);

  if (!organizationId || !memberId) {
    throw createError({
      statusCode: 400,
      statusMessage: "Missing required fields organizationId and userId",
    });
  }

  const result = await readValidatedBody(event, (body) =>
    schema.safeParse(body),
  );

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: "Missing required fields name",
    });
  }

  await requireOrganizationRole(event, organizationId, ["superuser"]);

  const { error } = await supabase
    .from("user_roles")
    .select()
    .eq("user_id", memberId)
    .eq("organization_id", organizationId)
    .single();

  if (error) {
    throw createError({
      statusCode: 500,
      statusMessage: "User not found in organization",
    });
  }

  const { name } = result.data;

  await supabase.auth.admin.updateUserById(memberId, {
    user_metadata: {
      display_name: name,
    },
  });

  return { organizationId, userId: memberId };
});
