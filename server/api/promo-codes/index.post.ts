import dayjs from "dayjs";

import { serverSupabaseServiceRole } from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";
import {
  createPromoCode,
  createPromoCodeParamsSchema,
} from "~/server/services/coupons";

export default defineEventHandler(async (event) => {
  const result = await readValidatedBody(event, (body) =>
    createPromoCodeParamsSchema.safeParse(body),
  );

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: result.error.message,
    });
  }

  // Ensure the user has the superuser role for the organization
  await requireOrganizationRole(event, result.data.organization_id, [
    "superuser",
  ]);

  if (result.data.code) {
    result.data.code = result.data.code.toUpperCase();
  }

  // Create the promotion code in Stripe
  const promotionCode = await createPromoCode(result.data);

  const supabase = serverSupabaseServiceRole(event);

  // Create the coupon in the database
  const { data, error } = await supabase
    .from("coupons")
    .insert({
      organization_id: result.data.organization_id,
      stripe_promotion_code_id: promotionCode.id,
      stripe_customer_id: result.data.customer_id,
      code: promotionCode.code.toUpperCase(),
      discount_amount:
        promotionCode.coupon.amount_off ??
        promotionCode.coupon.percent_off ??
        0,
      discount_type:
        promotionCode.coupon.amount_off !== null ? "amount" : "percent",
      usage_limit_per_user: result.data.usage_limit_per_user,
      expires_at: result.data.redeem_by
        ? dayjs.unix(result.data.redeem_by).toISOString()
        : null,
    })
    .select()
    .single();

  if (error) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message,
    });
  }

  return data;
});
