import { z } from "zod";

import { serverSupabaseServiceRole } from "#supabase/server";
import { requireOrganizationRole } from "~/server/helpers/permissions";
import { deletePromoCode } from "~/server/services/coupons";
import { stripe } from "~/server/services/stripe";

const schema = z.object({
  coupon_id: z.string(),
});

export default defineEventHandler(async (event) => {
  const result = await getValidatedQuery(event, (body) =>
    schema.safeParse(body),
  );

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: result.error.message,
    });
  }

  const supabase = serverSupabaseServiceRole(event);

  const { data: coupon, error } = await supabase
    .from("coupons")
    .select()
    .eq("id", result.data.coupon_id)
    .single();

  if (error) {
    throw createError({
      statusCode: 500,
      statusMessage: "Could not find coupon",
    });
  }

  // Ensure the user has the superuser role for the organization
  await requireOrganizationRole(event, coupon.organization_id, ["superuser"]);

  const stripePromotionCode = await stripe.promotionCodes.retrieve(
    coupon.stripe_promotion_code_id,
  );

  await supabase.from("coupons").delete().eq("id", coupon.id);

  await deletePromoCode(stripePromotionCode.coupon.id);

  return true;
});
