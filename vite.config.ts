import { sentryVitePlugin } from "@sentry/vite-plugin";
import { sveltekit } from "@sveltejs/kit/vite";
import Icons from "unplugin-icons/vite";
import { defineConfig } from "vite";

export default defineConfig({
	plugins: [
		sentryVitePlugin({
			org: "hiko-tech",
			project: "hiko-app",
			authToken: process.env.SENTRY_AUTH_TOKEN,
		}),
		sveltekit(),
		Icons({
			compiler: "svelte",
		}),
	],
	ssr: {
		noExternal: ["@jill64/sentry-sveltekit-cloudflare"],
	},
});
