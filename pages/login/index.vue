<script setup lang="ts">
import { useMutation } from "@tanstack/vue-query";
import { z } from "zod";

import type { FormSubmitEvent } from "#ui/types";
import ForgotPasswordModal from "~/pages/login/components/ForgotPasswordModal.vue";

const supabase = useSupabaseClient();
const router = useRouter();
const colorMode = useColorMode();

const schema = z.object({
  email: z.string().email("Invalid email"),
  password: z.string().min(8, "Must be at least 8 characters"),
});

type Schema = z.output<typeof schema>;

const state = reactive({
  email: undefined,
  password: undefined,
});
const turnstileToken = ref("");
const isForgotPasswordVisible = ref(false);

const isFormValid = computed(() => schema.safeParse(state).success);

const validateCaptcha = async () => {
  const runtimeConfig = useRuntimeConfig();
  
  if (!runtimeConfig.turnstileEnabled) {
    return;
  }

  if (!turnstileToken.value) {
    throw new Error("Please complete the captcha");
  }

  await $fetch("/api/captcha", {
    method: "POST",
    body: {
      token: turnstileToken.value,
    },
  });
};

const {
  mutate: signIn,
  isPending,
  error,
} = useMutation({
  mutationFn: async (event: FormSubmitEvent<Schema>) => {
    const { email, password } = event.data;

    await validateCaptcha();

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error("Invalid email or password");
    }

    await router.push("/");
  },
});
</script>

<template>
  <div class="flex-1 flex items-center justify-center px-3">
    <UCard class="w-[600px]">
      <h1 class="text-center text-xl mb-6">Sign in to your account</h1>
      <UForm :schema="schema" :state="state" class="space-y-4" @submit="signIn">
        <UFormGroup label="Email" name="email">
          <UInput v-model="state.email" size="lg" />
        </UFormGroup>

        <UFormGroup label="Password" name="password">
          <UInput v-model="state.password" type="password" size="lg" />
        </UFormGroup>

        <p v-if="error" class="text-red-500 text-center text-sm">
          {{ error.message }}
        </p>

        <div v-if="useRuntimeConfig().turnstileEnabled" class="flex justify-center">
          <NuxtTurnstile
            v-model="turnstileToken"
            :options="{ theme: colorMode.value as Turnstile.Theme }"
          />
        </div>

        <UButton
          type="submit"
          size="lg"
          block
          :loading="isPending"
          :disabled="!isFormValid"
        >
          Sign in
        </UButton>

        <UButton
          color="gray"
          variant="link"
          block
          @click="isForgotPasswordVisible = true"
        >
          Forgot password?
        </UButton>
      </UForm>
    </UCard>

    <ForgotPasswordModal v-model="isForgotPasswordVisible" />
    <UNotifications />
  </div>
</template>
