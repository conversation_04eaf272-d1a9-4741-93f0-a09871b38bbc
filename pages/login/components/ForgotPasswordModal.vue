<script setup lang="ts">
import { useMutation } from "@tanstack/vue-query";
import { z } from "zod";

import { Route } from "~/types/route";

const schema = z.object({
  email: z.string().email("Invalid email"),
});

const supabase = useSupabaseClient();
const toast = useToast();

const isVisible = defineModel<boolean>({ required: true });

const state = reactive({
  email: undefined as string | undefined,
});

const {
  mutateAsync: sendPasswordRecovery,
  isPending: isSending,
  error,
} = useMutation({
  mutationFn: async () => {
    if (!state.email) return;

    const { data, error } = await supabase.auth.resetPasswordForEmail(
      state.email,
      {
        redirectTo: `${window.location.origin}${Route.UPDATE_PASSWORD}`,
      },
    );

    if (error) {
      throw new Error(error.message);
    }

    isVisible.value = false;

    toast.add({
      title: "Password recovery email sent",
      description: `An email has been sent to ${state.email} with instructions on how to reset your password.`,
    });

    return data;
  },
});
</script>

<template>
  <UModal v-model="isVisible" prevent-close>
    <UForm
      :schema="schema"
      :state="state"
      class="space-y-4"
      @submit="sendPasswordRecovery"
    >
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              Forgot password?
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="ph:x"
              class="-my-1"
              @click="isVisible = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <UFormGroup label="Email" name="email">
            <UInput
              v-model="state.email"
              placeholder="Enter your email address"
            />
          </UFormGroup>

          <p v-if="error" class="text-red-500 text-sm text-center">
            {{ error.message }}
          </p>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-3">
            <UButton color="gray" variant="ghost" @click="isVisible = false">
              Cancel
            </UButton>
            <UButton type="submit" color="black" :loading="isSending">
              Send reset link
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>
