<script setup lang="ts">
import { useMutation } from "@tanstack/vue-query";
import { z } from "zod";

import { Route } from "~/types/route";

const schema = z.object({
  password: z.string().min(8, "Must be at least 8 characters"),
});

const supabase = useSupabaseClient();
const router = useRouter();

const state = reactive({
  password: "",
  password_confirm: "",
});
const showPassword = ref(false);

const {
  mutateAsync: updatePassword,
  isPending: isUpdatingPassword,
  error,
} = useMutation({
  mutationFn: async () => {
    const { data, error } = await supabase.auth.updateUser({
      password: state.password,
    });

    if (error) {
      throw new Error(error.message);
    }

    await router.push(Route.HOME);

    return data;
  },
});
</script>

<template>
  <div class="flex-1 flex items-center justify-center px-3">
    <UCard class="w-[600px]">
      <h1 class="text-center text-xl mb-6">Update your password</h1>

      <UForm
        :schema="schema"
        :state="state"
        class="space-y-4"
        @submit="updatePassword"
      >
        <UFormGroup label="New password" name="password">
          <UInput
            v-model="state.password"
            :type="showPassword ? 'text' : 'password'"
            size="md"
            leading-icon="heroicons:lock-closed"
            placeholder="Enter your new password"
          >
            <template v-if="state.password" #trailing>
              <UButton
                :icon="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'"
                color="gray"
                variant="link"
                :padded="false"
                class="pointer-events-auto"
                @click="showPassword = !showPassword"
              />
            </template>
          </UInput>
        </UFormGroup>

        <p v-if="error" class="text-red-500 text-center text-sm">
          {{ error.message }}
        </p>

        <div class="flex justify-end">
          <UButton
            color="black"
            type="submit"
            size="md"
            :loading="isUpdatingPassword"
          >
            Update password
          </UButton>
        </div>
      </UForm>
    </UCard>
  </div>
</template>
