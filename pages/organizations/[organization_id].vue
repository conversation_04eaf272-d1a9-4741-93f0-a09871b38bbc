<script setup lang="ts">
import { OrganizationRoute } from "~/types/route";

const route = useRoute();
const router = useRouter();
const organizationId = useOrganizationId();

watch(
  route,
  () => {
    if (route.fullPath === OrganizationRoute.HOME(organizationId.value)) {
      router.replace(OrganizationRoute.DASHBOARD(organizationId.value));
    }
  },
  { immediate: true },
);
</script>

<template>
  <NuxtPage />
</template>
