<script setup lang="ts">
import type { BreadcrumbLink } from "#ui/types";
import { OrganizationRoute } from "~/types/route";

const route = useRoute();
const organizationId = useOrganizationId();

const stationId = route.params.id as string;

const links = computed<BreadcrumbLink[]>(() => [
  {
    label: "Stations",
    to: OrganizationRoute.STATIONS(organizationId.value),
  },
  {
    label: stationId,
  },
]);

const tabs = computed(() => [
  {
    label: "Station slots",
    icon: "ph:rows",
    to:
      OrganizationRoute.STATIONS(organizationId.value) +
      "/" +
      stationId +
      "/slots",
  },
  {
    label: "Orders",
    icon: "ph:basket",
    to:
      OrganizationRoute.STATIONS(organizationId.value) +
      "/" +
      stationId +
      "/orders",
  },
  {
    label: "Settings",
    icon: "ph:gear",
    to:
      OrganizationRoute.STATIONS(organizationId.value) +
      "/" +
      stationId +
      "/settings",
  },
]);
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <UBreadcrumb :links="links" />
    </template>
    <template #subheader>
      <HorizontalNavigation :tabs="tabs" />
    </template>
    <NuxtPage />
  </NuxtLayout>
</template>
