<script setup lang="ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query";
import { refDebounced } from "@vueuse/core";

import type { Address } from "~/types/gmaps";
import { Query } from "~/types/query";

const route = useRoute();
const supabase = useSupabaseClient();
const queryClient = useQueryClient();
const toast = useToast();
const { isSuperuser } = useUserRole();
const userRoles = useUserRoles();

const stationId = route.params.id as string;

const { data: station, isLoading: isLoadingStation } = useQuery({
  queryKey: [Query.STATIONS, stationId],
  queryFn: async () => {
    const { data, error } = await supabase
      .from("stations")
      .select()
      .eq("id", stationId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    console.log("data loaded for stattion :",data);

    hourlyPrice.value = (data.hourly_price_cents ?? 0) / 100;
    isFreeToUse.value = data.is_free ?? false;

    return data;
  },
});

const { data: stationLocation, isLoading: isLoadingStationLocation } = useQuery(
  {
    queryKey: [Query.STATION_LOCATIONS, stationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("station_locations")
        .select()
        .eq("station_id", stationId)
        .maybeSingle();

      if (error) {
        throw new Error(error.message);
      }

      address.value = data?.address ?? "";

      return data;
    },
  },
);

const hourlyPrice = ref((station.value?.hourly_price_cents ?? 0) / 100);
const isFreeToUse = ref(station.value?.is_free ?? false);
const address = ref<string | Address>(stationLocation.value?.address ?? "");
const addressQuery = ref("");
const addressQueryDebounced = refDebounced(addressQuery, 500);
const canEdit = computed(() => {
  if (isSuperuser.value) return true;
  if (!station.value?.organization_id) return false;
  return userRoles.value[station.value.organization_id] === "superuser";
});

const { mutate: applyChanges, isPending: isApplyingChanges } = useMutation({
  mutationFn: async () => {
    const { error: stationError } = await supabase
      .from("stations")
      .update({
        hourly_price_cents: hourlyPrice.value * 100,
        is_free: isFreeToUse.value,
      })
      .eq("id", stationId)
      .select()
      .single();

    if (stationError) {
      throw new Error("Failed to update station");
    }

    await queryClient.invalidateQueries({
      queryKey: [Query.STATIONS, stationId],
    });

    if (typeof address.value !== "string") {
      await $fetch("/api/stations/address", {
        method: "POST",
        body: {
          station_id: stationId,
          place_id: address.value.place_id,
        },
      });

      await queryClient.invalidateQueries({
        queryKey: [Query.STATION_LOCATIONS, stationId],
      });
    }

    toast.add({
      title: "Changes applied successfully",
    });
  },
  onError: (error) => {
    toast.add({
      title: "Something went wrong",
      description: error.message,
      color: "red",
    });
  },
});

const { data: addresses } = useQuery({
  queryKey: [Query.ADDRESS_AUTOCOMPLETE, addressQueryDebounced],
  queryFn: async () => {
    if (!addressQueryDebounced.value) {
      return [];
    }

    return $fetch("/api/stations/address/autocomplete", {
      method: "GET",
      query: {
        query: addressQueryDebounced.value,
      },
    });
  },
});
</script>

<template>
  <Loading v-if="isLoadingStation || isLoadingStationLocation" />
  <div v-else class="flex-1 space-y-6 px-4 py-6">
    <div
      class="flex flex-col md:flex-row md:items-center justify-between gap-3"
    >
      <div>
        <p class="text-gray-900 dark:text-white font-semibold">Settings</p>
        <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Configure the station settings
        </div>
      </div>
      <UButton
        v-if="canEdit"
        color="black"
        :loading="isApplyingChanges"
        class="justify-center md:justify-start"
        size="md"
        @click="applyChanges"
      >
        Save changes
      </UButton>
    </div>

    <UDivider />

    <div
      class="flex flex-col md:flex-row md:items-center justify-between gap-3"
    >
      <div>
        <p class="text-sm font-medium text-gray-700 dark:text-gray-200">
          Is free to use
        </p>
        <div class="text-gray-500 dark:text-gray-400 text-sm">
          If the station is free to use, the user will not be charged for the battery.
          It will not ask for payment at all when using the POS.
        </div>
      </div>
      <UCheckbox
        v-model="isFreeToUse"
        :disabled="!canEdit"
        label="Is free to use"
      />

    </div>

    <UDivider />

    <div
      class="flex flex-col md:flex-row md:items-center justify-between gap-3"
    >
      <div>
        <p class="text-sm font-medium text-gray-700 dark:text-gray-200">
          Hourly price
        </p>
        <div class="text-gray-500 dark:text-gray-400 text-sm">
          How much per hour it is to rent a battery from this station. (in CA$)
        </div>
      </div>
      <UInput
        v-model="hourlyPrice"
        size="md"
        type="number"
        min="0"
        placeholder="0.00"
        :disabled="!canEdit"
      >
        <template #trailing>
          <span class="text-gray-500 dark:text-gray-400 text-sm"> CA$ </span>
        </template>
      </UInput>
    </div>

    <UDivider />

    <div
      class="flex flex-col md:flex-row md:items-center justify-between gap-3"
    >
      <div>
        <p class="text-sm font-medium text-gray-700 dark:text-gray-200">
          Address
        </p>
        <div class="text-gray-500 dark:text-gray-400 text-sm">
          Where the station is located.
        </div>
      </div>
      <UInputMenu
        v-model="address"
        v-model:query="addressQuery"
        :options="addresses"
        :popper="{ placement: 'bottom-start' }"
        :ui="{
          width: 'w-full',
          popper: {
            strategy: 'absolute',
          },
        }"
        size="md"
        option-attribute="fulltext"
        class="w-full max-w-96"
        :disabled="!canEdit"
      />
    </div>

    <UDivider />

    <div
      class="flex flex-col md:flex-row md:items-center justify-between gap-3"
    >
      <div>
        <p class="text-gray-900 dark:text-white font-semibold">QR Code</p>
        <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Download the QR code for this station.
        </div>
      </div>
      <QrCodeDownloader
        :data="`https://app.hiko-tech.com/?station_id=${stationId}`"
        :filename="`hiko_${stationId}`"
        class="justify-center md:justify-start"
      />
    </div>
  </div>
</template>
