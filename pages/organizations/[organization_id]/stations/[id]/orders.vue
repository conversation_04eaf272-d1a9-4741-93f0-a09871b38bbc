<script setup lang="ts">
import OrderTable from "~/components/OrderTable.vue";
import { useOrdersByStation } from "~/queries/orders/useOrdersByStation";

const route = useRoute();
const organizationId = useOrganizationId();
const { isSuperuser } = useUserRole();

const stationId = route.params.id as string;

const {
  data: orders,
  isLoading,
  page,
  perPage,
  sort,
} = useOrdersByStation(stationId);

const excludeColumns = computed(() => (isSuperuser.value ? [] : ["user_id"]));
</script>

<template>
  <OrderTable
    v-model:sort="sort"
    v-model:page="page"
    v-model:per-page="perPage"
    :orders="orders?.data"
    :count="orders?.count ?? 0"
    :is-loading="isLoading"
    :organization-id="organizationId"
    :exclude-columns="excludeColumns"
  />
</template>
