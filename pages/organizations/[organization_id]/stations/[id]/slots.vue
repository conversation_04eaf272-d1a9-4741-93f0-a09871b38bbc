<script setup lang="ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query";

import type { Tables } from "~/types/database.types";
import { Query } from "~/types/query";
import { Time } from "~/types/time";

const route = useRoute();
const supabase = useSupabaseClient();
const queryClient = useQueryClient();
const { isSuperuser } = useUserRole();

const stationId = route.params.id as string;

const { data: stationSlots, isLoading: isLoadingStationSlots } = useQuery({
  queryKey: [Query.STATION_SLOTS, stationId],
  queryFn: async () => {
    const { data, error } = await supabase
      .from("station_slots")
      .select()
      .eq("station_id", stationId)
      .order("slot_id", { ascending: true });

    if (error) throw new Error(error.message);

    return data;
  },
  refetchInterval: Time.ONE_MINUTE,
});

const rows = computed(() => {
  if (!stationSlots.value) return [];

  return stationSlots.value.map((slot) => ({
    slot_id: slot.slot_id,
    power: slot.power,
    amperage: slot.amperage,
    voltage: slot.voltage,
  }));
});

const columns = [
  {
    key: "slot_id",
    label: "ID",
    sortable: true,
  },
  {
    key: "power",
    label: "Battery level",
    sortable: true,
  },
  {
    key: "amperage",
    label: "Amperage",
    sortable: true,
  },
  {
    key: "voltage",
    label: "Voltage",
    sortable: true,
  },
  {
    key: "actions",
  },
];

const isSlotEmpty = (slot: Tables<"station_slots">) =>
  slot.power === 0 && !slot.is_charging;

const getBatteryIcon = (power: number) => {
  if (power > 75) return "ph:battery-full-bold";
  else if (power > 50) return "ph:battery-medium-bold";
  else if (power > 25) return "ph:battery-low-bold";
  else return "ph:battery-empty-bold";
};

const getBatteryColor = (power: number) => {
  if (power > 75) return "text-green-400";
  else if (power > 50) return "text-orange-400";
  else if (power > 25) return "text-red-500";
  else return "text-gray-500";
};

const { mutate: ejectSlot } = useMutation({
  mutationFn: async (slotId: number) => {
    await $fetch("/api/stations/handler", {
      method: "POST",
      body: {
        did: stationId,
        slot: slotId,
      },
      query: {
        option: "popup_confirm",
      },
    });

    setTimeout(async () => {
      await queryClient.invalidateQueries({
        queryKey: [Query.STATION_SLOTS, stationId],
      });
    }, Time.FIVE_SECONDS);
  },
});
</script>

<template>
  <UTable
    :rows="rows"
    :columns="columns"
    :loading="isLoadingStationSlots"
    class="border-b border-gray-300 dark:border-gray-700"
  >
    <template #power-data="{ row }: { row: Tables<'station_slots'> }">
      <div v-if="isSlotEmpty(row)">-</div>
      <div v-else class="flex items-center gap-1">
        <UIcon
          :name="getBatteryIcon(row.power)"
          :class="cn('size-5', getBatteryColor(row.power))"
        />
        <p class="text-gray-500 dark:text-gray-400 font-medium">
          {{ row.power }}%
        </p>
      </div>
    </template>
    <template #amperage-data="{ row }: { row: Tables<'station_slots'> }">
      <div v-if="isSlotEmpty(row)">-</div>
      <p v-else>
        {{ row.amperage }}
      </p>
    </template>
    <template #voltage-data="{ row }: { row: Tables<'station_slots'> }">
      <div v-if="isSlotEmpty(row)">-</div>
      <p v-else>
        {{ row.voltage }}
      </p>
    </template>
    <template
      v-if="isSuperuser"
      #actions-data="{ row }: { row: Tables<'station_slots'> }"
    >
      <div v-if="isSlotEmpty(row)">-</div>
      <UTooltip
        v-else
        :text="`Eject battery #${row.slot_id}`"
        :popper="{ placement: 'top' }"
      >
        <UButton color="gray" size="xs" @click="ejectSlot(row.slot_id)">
          Eject
        </UButton>
      </UTooltip>
    </template>
  </UTable>
</template>
