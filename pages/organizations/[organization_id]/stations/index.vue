<script setup lang="ts">
import { useQuery } from "@tanstack/vue-query";
import { refDebounced } from "@vueuse/core";
import dayjs from "dayjs";

import type { Tables } from "~/types/database.types";
import { Query } from "~/types/query";
import { OrganizationRoute } from "~/types/route";

const supabase = useSupabaseClient();
const router = useRouter();
const organizationId = useOrganizationId();

const search = ref("");
const searchDebounced = refDebounced(search, 500);
const sort = ref<{ column: string; direction: "desc" | "asc" }>({
  column: "created_at",
  direction: "desc",
});
const sortDebounced = refDebounced(sort, 500);
const page = ref(1);
const perPage = ref(10);

const { data: stations, isLoading: isLoadingStations } = useQuery({
  queryKey: [
    Query.STATIONS,
    organizationId,
    searchDebounced,
    sortDebounced,
    page,
    perPage,
  ],
  queryFn: async () => {
    const query = supabase
      .from("stations")
      .select("*", { count: "estimated" })
      .order(sortDebounced.value.column, {
        ascending: sortDebounced.value.direction === "asc",
      })
      .range((page.value - 1) * perPage.value, page.value * perPage.value - 1);

    if (organizationId.value !== ALL_ORGANIZATIONS) {
      query.eq("organization_id", organizationId.value);
    }

    if (searchDebounced.value) {
      query.or(`id.ilike."%${searchDebounced.value}%"`);
    }

    const { data, count, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      data,
      count,
    };
  },
});

const columns = [
  {
    key: "id",
    label: "ID",
  },
  {
    key: "usable_batteries",
    label: "Usable batteries",
    sortable: true,
  },
  {
    key: "total_batteries",
    label: "Total batteries",
    sortable: true,
  },
  {
    key: "hourly_price",
    label: "Hourly price",
    sortable: true,
  },
  {
    key: "signal_strength",
    label: "Signal strength",
    sortable: true,
  },
  {
    key: "last_heartbeat_at",
    label: "Status",
    sortable: true,
  },
  {
    key: "created_at",
    label: "Created at",
    sortable: true,
  },
];

const isOnline = (date: string) => {
  return dayjs().diff(dayjs(date), "minute") < 5;
};

const onClickStation = (station: Tables<"stations">) => {
  router.push(
    `${OrganizationRoute.STATIONS(organizationId.value)}/${station.id}`,
  );
};

const getSignalIcon = (dBm: number | null) => {
  if (dBm === null) return "ph:cell-signal-slash-bold";

  if (dBm > -51) {
    return "ph:cell-signal-full-bold";
  } else if (dBm > -111) {
    return "ph:cell-signal-medium-bold";
  } else {
    return "ph:cell-signal-low-bold";
  }
};

const getSignalColor = (dBm: number | null) => {
  if (dBm === null) return "text-gray-400";

  if (dBm > -51) {
    return "text-green-400";
  } else if (dBm > -111) {
    return "text-orange-400";
  } else {
    return "text-red-500";
  }
};
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <div class="flex items-center gap-1.5">
        <h1 class="font-semibold">Stations</h1>
        <UBadge v-if="stations?.count" color="green" variant="subtle" size="xs">
          {{ stations.count }}
        </UBadge>
      </div>
    </template>

    <template #subheader>
      <div class="flex items-center">
        <UInput
          v-model="search"
          icon="ph:magnifying-glass"
          placeholder="Search stations..."
        >
          <template #trailing>
            <UButton
              v-show="search !== ''"
              color="gray"
              variant="link"
              icon="ph:x"
              :padded="false"
              class="pointer-events-auto"
              @click="search = ''"
            />
          </template>
        </UInput>
      </div>
    </template>

    <UTable
      v-model:sort="sort"
      sort-mode="manual"
      :loading="isLoadingStations"
      :rows="stations?.data"
      :columns="columns"
      class="border-b border-gray-300 dark:border-gray-700"
      @select="onClickStation"
    >
      <template #id-data="{ row }: { row: Tables<'stations'> }">
        <p class="text-gray-800 dark:text-gray-300 font-medium">
          {{ row.id }}
        </p>
      </template>
      <template #hourly_price-data="{ row }: { row: Tables<'stations'> }">
        <p>
          {{
            formatCurrency((row.hourly_price_cents ?? 0) / 100, {
              currencyDisplay: "narrowSymbol",
            })
          }}
        </p>
      </template>
      <template #last_heartbeat_at-data="{ row }: { row: Tables<'stations'> }">
        <UBadge
          v-if="isOnline(row.last_heartbeat_at)"
          color="green"
          variant="subtle"
        >
          online
        </UBadge>
        <UBadge v-else color="orange" variant="subtle"> offline </UBadge>
      </template>
      <template #signal_strength-data="{ row }: { row: Tables<'stations'> }">
        <div class="flex items-center gap-1">
          <UIcon
            :name="getSignalIcon(row.signal_strength)"
            :class="cn('size-5', getSignalColor(row.signal_strength))"
          />
          <span>{{ row.signal_strength }} dBm</span>
        </div>
      </template>
      <template #created_at-data="{ row }: { row: Tables<'stations'> }">
        <p>{{ dayjs(row.created_at).format("D MMM YYYY") }}</p>
      </template>
    </UTable>

    <div v-if="stations?.data" class="flex justify-end px-3 py-3.5">
      <Pagination
        v-model="page"
        v-model:per-page="perPage"
        :total="stations.count ?? 0"
      />
    </div>
  </NuxtLayout>
</template>
