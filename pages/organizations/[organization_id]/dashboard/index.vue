<script setup lang="ts">
import dayjs from "dayjs";

import DateRangePicker from "~/components/DatePicker/DateRangePicker.vue";

import RecentOrders from "./components/RecentOrders.vue";
import RevenueChart from "./components/RevenueChart.vue";
import TopStations from "./components/TopStations.vue";

const dateRange = ref({
  start: dayjs().startOf("day").subtract(30, "days").toDate(),
  end: dayjs().endOf("day").toDate(),
});
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <h1 class="font-semibold">Dashboard</h1>
    </template>

    <template #subheader>
      <div class="flex">
        <DateRangePicker
          v-model="dateRange"
          :button-props="{ color: 'gray' }"
        />
      </div>
    </template>

    <div class="p-4">
      <RevenueChart :start="dateRange.start" :end="dateRange.end" />
      <div class="grid md:grid-cols-2 gap-4 md:gap-8 mt-4 md:mt-8">
        <RecentOrders />
        <TopStations />
      </div>
    </div>
  </NuxtLayout>
</template>
