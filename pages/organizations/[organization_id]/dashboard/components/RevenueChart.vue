<script setup lang="ts">
import { useQuery } from "@tanstack/vue-query";
import { useElementSize } from "@vueuse/core";
import dayjs from "dayjs";

import { Query } from "~/types/query";

type Props = {
  start: Date;
  end: Date;
};

const props = defineProps<Props>();

const supabase = useSupabaseClient();
const organizationId = useOrganizationId();

const dates = computed(() => {
  const diff = dayjs(props.end).diff(dayjs(props.start), "day");
  return new Array(diff + 1)
    .fill(0)
    .map((_, index) =>
      dayjs(props.start).add(index, "day").format("YYYY-MM-DD"),
    );
});

const { data: incomePerDay, isLoading } = useQuery({
  queryKey: [
    Query.INCOME_PER_DAY,
    organizationId,
    () => props.start,
    () => props.end,
  ],
  queryFn: async () => {
    if (!organizationId.value) return [];

    const query = supabase
      .from("income_per_day")
      .select()
      .gte("date", dayjs(props.start).toISOString())
      .lte("date", dayjs(props.end).toISOString());

    if (organizationId.value !== ALL_ORGANIZATIONS) {
      query.eq("organization_id", organizationId.value);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },
});

const series = computed(() => {
  if (!incomePerDay.value) {
    return [];
  }

  const data = dates.value.map((date) => {
    const item = incomePerDay.value.find((item) =>
      dayjs(item.date).isSame(date, "day"),
    );
    return {
      x: date,
      y: (item?.total_income_cents ?? 0) / 100,
    };
  });

  return [
    {
      name: "Income",
      data,
    },
  ];
});

const totalIncome = computed(() => {
  if (!incomePerDay.value) {
    return 0;
  }

  return (
    incomePerDay.value.reduce(
      (acc, item) => acc + (item.total_income_cents ?? 0),
      0,
    ) / 100
  );
});

const containerElement = ref<HTMLDivElement>();
const { width: containerWidth, height: containerHeight } =
  useElementSize(containerElement);

const options = computed<ApexCharts.ApexOptions>(() => ({
  chart: {
    type: "area",
    width: containerWidth.value,
    height: containerHeight.value,
  },
  series: series.value,
  colors: ["#3b82f6"],
  stroke: {
    curve: "smooth",
  },
  xaxis: {
    type: "datetime",
    labels: {
      format: "d MMM",
    },
    tooltip: {
      enabled: false,
    },
  },
  yaxis: {
    show: false,
  },
  grid: {
    xaxis: {
      lines: {
        show: true,
      },
    },
  },
  tooltip: {
    y: {
      formatter: (value: number) =>
        formatCurrency(value, { currencyDisplay: "narrowSymbol" }),
      title: {
        formatter: () => "",
      },
    },
    marker: {
      show: false,
    },
    custom: ({ seriesIndex, dataPointIndex }) => {
      return `
                <div class='bg-gray-50 dark:bg-gray-800 ring-1 ring-gray-200 dark:ring-gray-600 rounded-md shadow-md px-3 py-1.5'>
                    <p class='font-medium text-gray-900 dark:text-gray-50'>
                        ${dayjs(series.value[seriesIndex].data[dataPointIndex].x).format("D MMM")}:
                        ${formatCurrency(series.value[seriesIndex].data[dataPointIndex].y, { currencyDisplay: "narrowSymbol" })}
                    </p>
                </div>
              `;
    },
  },
}));
</script>

<template>
  <UCard
    :ui="{
      divide: 'divide-y-0',
      body: {
        padding: '!p-0',
      },
    }"
  >
    <template #header>
      <p class="text-sm text-gray-500 dark:text-gray-400 font-medium mb-1">
        Revenue
      </p>
      <p class="text-3xl text-gray-900 dark:text-white font-semibold">
        {{ formatCurrency(totalIncome) }}
      </p>
    </template>

    <div ref="containerElement" class="h-96 flex items-center pb-2">
      <Loading v-if="isLoading" />
      <ApexChart v-else :options="options" />
    </div>
  </UCard>
</template>
