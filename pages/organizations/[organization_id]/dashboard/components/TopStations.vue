<script setup lang="ts">
import { useQuery } from "@tanstack/vue-query";

import { Query } from "~/types/query";
import { OrganizationRoute } from "~/types/route";

const supabase = useSupabaseClient();
const organizationId = useOrganizationId();

const { data: ordersPerStation, isLoading } = useQuery({
  queryKey: [Query.ORDERS_PER_STATION, organizationId],
  queryFn: async () => {
    if (!organizationId.value) return [];

    const query = supabase
      .from("orders_per_station")
      .select()
      .order("order_count", { ascending: false });

    if (organizationId.value !== ALL_ORGANIZATIONS) {
      query.eq("organization_id", organizationId.value);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },
});

const totalOrderCount = computed(() => {
  if (!ordersPerStation.value) return 0;

  return ordersPerStation.value.reduce((acc, order) => {
    return acc + (order.order_count ?? 0);
  }, 0);
});
</script>

<template>
  <UCard
    :ui="{
      divide: 'divide-y-0',
      body: {
        padding: '!pt-0',
      },
    }"
  >
    <template #header>
      <div class="flex gap-4">
        <UIcon
          name="ph:ranking"
          class="size-[48px] text-gray-900 dark:text-white"
        />
        <div>
          <p class="text-gray-900 dark:text-white font-semibold">
            Top stations
          </p>
          <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">
            Stations that generated the most orders.
          </p>
        </div>
      </div>
    </template>

    <div v-if="isLoading">
      <Loading />
    </div>
    <div v-else-if="ordersPerStation?.length === 0">
      <p class="text-gray-500 dark:text-gray-400 text-center text-sm p-3">
        No data.
      </p>
    </div>
    <ul v-else-if="ordersPerStation" class="space-y-2">
      <li
        v-for="(order, index) in ordersPerStation"
        :key="index"
        class="flex items-center"
      >
        <UButton
          color="black"
          variant="link"
          :padded="false"
          :to="`${OrganizationRoute.STATIONS(organizationId)}/${order.station_id}/orders`"
          class="w-32 text-gray-500 dark:text-gray-400 capitalize"
        >
          {{ order.station_id }}
        </UButton>
        <UMeter
          color="gray"
          size="lg"
          class="flex-1"
          :value="order.order_count ?? 0"
          :max="totalOrderCount"
        />
        <p class="w-8 text-gray-400 dark:text-gray-500 text-end text-sm">
          {{ order.order_count ?? 0 }}
        </p>
      </li>
    </ul>
  </UCard>
</template>
