<script setup lang="ts">
import { useQuery } from "@tanstack/vue-query";
import dayjs from "dayjs";

import { Query } from "~/types/query";
import { OrganizationRoute } from "~/types/route";

const supabase = useSupabaseClient();
const organizationId = useOrganizationId();

const { data: orders, isLoading: isLoadingOrders } = useQuery({
  queryKey: [Query.ORDERS, organizationId],
  queryFn: async () => {
    if (!organizationId.value) {
      return {
        data: [],
        count: 0,
      };
    }

    const query = supabase
      .from("orders")
      .select("*, station:stations!inner(organization_id)", {
        count: "estimated",
      })
      .order("created_at", { ascending: false })
      .not("ended_at", "is", null)
      .gte("created_at", dayjs().subtract(30, "days").toISOString())
      .limit(5);

    if (organizationId.value !== ALL_ORGANIZATIONS) {
      query.eq("station.organization_id", organizationId.value);
    }

    const { data, count, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      data,
      count,
    };
  },
});
</script>

<template>
  <UCard
    :ui="{
      divide: 'divide-y-0',
      body: {
        padding: '!pt-0',
      },
    }"
  >
    <template #header>
      <div class="flex gap-4">
        <UIcon
          name="ph:shopping-cart"
          class="size-[48px] text-gray-900 dark:text-white"
        />
        <div>
          <p class="text-gray-900 dark:text-white font-semibold">
            Recent orders
          </p>
          <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">
            You had {{ orders?.count ?? 0 }} orders in the last 30 days.
          </p>
        </div>
      </div>
    </template>

    <div v-if="isLoadingOrders">
      <Loading />
    </div>
    <div v-else-if="orders?.data.length === 0">
      <p class="text-gray-500 dark:text-gray-400 text-center text-sm p-3">
        No orders found.
      </p>
    </div>
    <ul v-else-if="orders">
      <li
        v-for="(order, index) in orders.data"
        :key="index"
        class="flex items-center justify-between gap-2 text-sm px-3 py-2 -mx-3"
      >
        <div>
          <p class="text-gray-900 dark:text-white font-semibold">
            {{ dayjs(order.ended_at).format("D MMM") }}
          </p>
          <UButton
            color="black"
            variant="link"
            :padded="false"
            :to="`${OrganizationRoute.STATIONS(organizationId)}/${order.station_id}/orders`"
            class="text-gray-500 dark:text-gray-400 capitalize"
          >
            {{ order.station_id }}
          </UButton>
        </div>
        <p class="text-gray-900 dark:text-white font-medium text-lg">
          {{
            formatCurrency(order.amount_charged_cents / 100, {
              currencyDisplay: "narrowSymbol",
            })
          }}
        </p>
      </li>
    </ul>
  </UCard>
</template>
