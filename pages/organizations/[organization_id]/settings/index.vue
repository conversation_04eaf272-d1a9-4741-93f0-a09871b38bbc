<script setup lang="ts">
import EditOrganizationForm from "~/pages/organizations/[organization_id]/settings/components/EditOrganizationForm.vue";

import EditProfileForm from "./components/EditProfileForm.vue";

const user = useSupabaseUser();
const organizationId = useOrganizationId();
</script>

<template>
  <div v-if="!user">
    <Loading />
  </div>
  <div v-else class="space-y-6 px-4 py-6">
    <EditProfileForm />
    <template v-if="organizationId !== ALL_ORGANIZATIONS">
      <UDivider />
      <EditOrganizationForm />
    </template>
  </div>
</template>
