<script setup lang="ts">
import { useMutation } from "@tanstack/vue-query";
import { z } from "zod";

const schema = z.object({
  email: z.string().email("Invalid email"),
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must be at most 50 characters")
    .optional(),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .optional(),
});

const user = useSupabaseUser();
const supabase = useSupabaseClient();
const toast = useToast();

const state = reactive({
  name: user.value?.user_metadata?.display_name as string | undefined,
  email: user.value?.email,
  password: undefined as string | undefined,
});
const showPassword = ref(false);

const { mutateAsync: updateProfile, isPending: isUpdatingProfile } =
  useMutation({
    mutationFn: async () => {
      await supabase.auth.updateUser({
        password: state.password || undefined,
        data: {
          display_name: state.name,
        },
      });

      toast.add({
        title: "Profile settings updated",
      });
    },
  });

watch(
  user,
  () => {
    state.name = user.value?.user_metadata?.display_name as string | undefined;
    state.email = user.value?.email;
  },
  { immediate: true },
);
</script>

<template>
  <UForm
    :schema="schema"
    :state="state"
    class="space-y-6"
    @submit="updateProfile"
  >
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-3"
    >
      <div>
        <p class="text-gray-900 dark:text-white font-semibold">Profile</p>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Update your profile settings.
        </p>
      </div>

      <UButton
        type="submit"
        color="black"
        size="md"
        :loading="isUpdatingProfile"
        class="justify-center md:justify-start"
      >
        Save profile settings
      </UButton>
    </div>

    <UDivider />

    <div class="grid md:grid-cols-2 gap-3 md:gap-2">
      <div class="text-sm">
        <label
          for="name"
          class="block font-medium text-gray-700 dark:text-gray-200"
          >Name</label
        >
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          Will appear on receipts, invoices, and other communication.
        </p>
      </div>
      <div>
        <UFormGroup name="name">
          <UInput
            id="name"
            v-model.trim="state.name"
            name="name"
            leading-icon="heroicons:user"
            placeholder="John Doe"
            size="md"
            @update:model-value="state.name = $event || undefined"
          />
        </UFormGroup>
      </div>
    </div>

    <UDivider />

    <div class="grid md:grid-cols-2 gap-3 md:gap-2">
      <div class="text-sm">
        <label
          for="email"
          class="block font-medium text-gray-700 dark:text-gray-200 after:content-['*'] after:ms-0.5 after:text-red-500 dark:after:text-red-400"
          >Email</label
        >
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          Used to sign in, for email receipts and product updates.
        </p>
      </div>
      <div>
        <UFormGroup name="email">
          <UInput
            id="email"
            v-model.trim="state.email"
            type="email"
            leading-icon="heroicons:envelope"
            placeholder="<EMAIL>"
            size="md"
            disabled
          />
        </UFormGroup>
      </div>
    </div>

    <UDivider />

    <div class="grid md:grid-cols-2 gap-3 md:gap-2">
      <div class="text-sm">
        <label
          for="password"
          class="block font-medium text-gray-700 dark:text-gray-200"
          >Password</label
        >
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          Leave blank to keep your current password.
        </p>
      </div>
      <div>
        <UFormGroup name="password">
          <UInput
            id="password"
            v-model="state.password"
            :type="showPassword ? 'text' : 'password'"
            leading-icon="heroicons:lock-closed"
            placeholder="New password"
            autocomplete="current-password"
            size="md"
            @update:model-value="state.password = $event || undefined"
          >
            <template v-if="state.password" #trailing>
              <UButton
                :icon="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'"
                color="gray"
                variant="link"
                :padded="false"
                class="pointer-events-auto"
                @click="showPassword = !showPassword"
              />
            </template>
          </UInput>
        </UFormGroup>
      </div>
    </div>
  </UForm>
</template>
