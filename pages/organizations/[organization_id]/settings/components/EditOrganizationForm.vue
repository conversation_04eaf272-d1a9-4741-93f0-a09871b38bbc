<script setup lang="ts">
import { useMutation, useQueryClient } from "@tanstack/vue-query";
import { z } from "zod";

import { useOrganization } from "~/composables/useOrganizations";
import { Query } from "~/types/query";

const schema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must be at most 50 characters"),
});

const organization = useOrganization();
const supabase = useSupabaseClient();
const queryClient = useQueryClient();
const toast = useToast();

const state = reactive({
  name: organization.value?.name,
});

const { mutateAsync: updateOrganization, isPending: isUpdatingOrganization } =
  useMutation({
    mutationFn: async () => {
      if (!organization.value) return;

      await supabase
        .from("organizations")
        .update({
          name: state.name,
        })
        .eq("id", organization.value.id);

      await queryClient.invalidateQueries({ queryKey: [Query.ORGANIZATIONS] });

      toast.add({
        title: "Organization settings updated",
      });
    },
  });

watch(
  organization,
  () => {
    state.name = organization.value?.name;
  },
  { immediate: true },
);
</script>

<template>
  <UForm
    v-if="organization"
    :schema="schema"
    :state="state"
    class="space-y-6"
    @submit="updateOrganization"
  >
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-3"
    >
      <div>
        <p class="text-gray-900 dark:text-white font-semibold">Organization</p>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Update your organization settings.
        </p>
      </div>

      <UButton
        :loading="isUpdatingOrganization"
        type="submit"
        color="black"
        size="md"
        class="justify-center md:justify-start"
      >
        Save organization settings
      </UButton>
    </div>

    <UDivider />

    <div class="grid md:grid-cols-2 gap-3 md:gap-2">
      <div class="text-sm">
        <label
          for="name"
          class="block font-medium text-gray-700 dark:text-gray-200 after:content-['*'] after:ms-0.5 after:text-red-500 dark:after:text-red-400"
          >Name</label
        >
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          The name of your organization.
        </p>
      </div>
      <div>
        <UFormGroup name="name">
          <UInput
            id="name"
            v-model.trim="state.name"
            name="name"
            leading-icon="heroicons:building-office"
            placeholder="Enter your organization name"
            size="md"
          />
        </UFormGroup>
      </div>
    </div>
  </UForm>
</template>
