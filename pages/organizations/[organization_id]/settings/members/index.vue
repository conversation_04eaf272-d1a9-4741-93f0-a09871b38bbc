<script setup lang="ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query";

import EditMemberModal from "~/pages/organizations/[organization_id]/settings/members/components/EditMemberModal.vue";
import InviteMemberModal from "~/pages/organizations/[organization_id]/settings/members/components/InviteMemberModal.vue";
import type { Enums, Tables } from "~/types/database.types";
import { Query } from "~/types/query";

const organizationId = useOrganizationId();
const supabase = useSupabaseClient();
const queryClient = useQueryClient();
const { isSuperuser } = useUserRole();
const { areYouSure } = useConfirmationDialog();
const toast = useToast();

const roles: Array<{
  label: string;
  value: Enums<"user_role">;
}> = [
  {
    label: "Employee",
    value: "employee",
  },
  {
    label: "Admin",
    value: "admin",
  },
];

const search = ref("");
const isInvitationVisible = ref(false);

const { data: userRoles, isLoading } = useQuery({
  queryKey: [Query.USER_ROLES, organizationId],
  queryFn: async () => {
    const { data, error } = await supabase
      .from("user_roles")
      .select("*, user:users!inner(*)")
      .eq("organization_id", organizationId.value)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },
});

const userRolesFiltered = computed(() => {
  if (!userRoles.value) return [];

  const rows = [...userRoles.value].sort((a) =>
    a.role === "superuser" ? -1 : 1,
  );

  if (!search.value) return rows;

  return rows.filter((row) =>
    row.user?.email?.toLowerCase().includes(search.value.toLowerCase()),
  );
});

const getProfileName = (email: string | null) => {
  return email?.split("@")[0] || "Unknown";
};

const { mutateAsync: updateUserRole, isPending: isUpdatingUserRole } =
  useMutation({
    mutationFn: async ({
      userId,
      role,
    }: {
      userId: string;
      role: Enums<"user_role">;
    }) => {
      await supabase.from("user_roles").update({ role }).eq("user_id", userId);

      await queryClient.invalidateQueries({
        queryKey: [Query.USER_ROLES, organizationId.value],
      });
    },
  });

const editMemberModalState = reactive({
  isVisible: false,
  memberId: "",
});
const openEditMemberModal = (memberId: string) => {
  editMemberModalState.memberId = memberId;
  editMemberModalState.isVisible = true;
};

const { mutateAsync: removeMember } = useMutation({
  mutationFn: async (user: Tables<"users">) => {
    if (!organizationId.value) return;

    await $fetch(
      `/api/organizations/${organizationId.value}/members/${user.id}`,
      {
        method: "DELETE",
      },
    );

    await queryClient.invalidateQueries({
      queryKey: [Query.USER_ROLES, organizationId.value],
    });

    toast.add({
      title: "Member removed",
      description: `Member ${user.email} was removed from your organization`,
    });
  },
});

const getDisplayName = (user: Tables<"users">) => {
  return (user?.raw_user_meta_data as { display_name?: string }).display_name;
};

const getMemberActions = (
  userRole: Tables<"user_roles"> & { user: Tables<"users"> },
) => {
  return [
    [
      {
        label: "Edit member",
        click: () => openEditMemberModal(userRole.user_id),
      },
      {
        label: "Remove member",
        class: "text-red-500 dark:text-red-400",
        click: () => areYouSure(() => removeMember(userRole.user)),
        hidden: userRole.role === "superuser",
      },
    ].filter((item) => !item.hidden),
  ];
};
</script>

<template>
  <div class="flex-1 flex flex-col overflow-y-auto px-4 py-6">
    <div class="grid grid-cols-1 md:grid-cols-3 items-start gap-6">
      <div class="sticky top-0 col-span-1">
        <p class="text-gray-900 dark:text-white font-semibold">Manage access</p>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 mb-4">
          Invite new members by email address.
        </p>

        <UButton
          v-if="isSuperuser"
          color="black"
          @click="isInvitationVisible = true"
        >
          Invite people
        </UButton>
      </div>

      <div
        class="col-span-1 md:col-span-2 rounded-lg ring-1 ring-gray-200 dark:ring-gray-800 shadow bg-white dark:bg-gray-900"
      >
        <div class="border-b border-gray-200 dark:border-gray-800 p-4 sm:px-6">
          <UInput
            v-model="search"
            leading-icon="heroicons:magnifying-glass"
            placeholder="Search members"
            :disabled="isLoading"
          />
        </div>
        <div v-if="isLoading">
          <Loading />
        </div>
        <div v-else-if="!userRolesFiltered.length">
          <p class="text-gray-500 dark:text-gray-400 text-center text-sm p-6">
            No members found.
          </p>
        </div>
        <ul v-else class="divide-y divide-gray-200 dark:divide-gray-800">
          <li
            v-for="(userRole, index) in userRolesFiltered"
            :key="index"
            class="flex items-center justify-between gap-3 py-3 px-4 sm:px-6"
          >
            <template v-if="userRole.user">
              <div class="flex items-center gap-3 overflow-hidden">
                <UAvatar
                  :src="
                    getProfileAvatarUrl(getProfileName(userRole.user.email))
                  "
                  size="md"
                  class="bg-gray-50 shrink-0"
                />
                <div class="overflow-hidden text-sm">
                  <div class="flex items-center gap-1">
                    <p
                      v-if="getDisplayName(userRole.user)"
                      class="text-gray-900 dark:text-white font-medium truncate"
                    >
                      {{ getDisplayName(userRole.user) }}
                    </p>
                    <p
                      v-else
                      class="text-gray-500 dark:text-gray-400 font-medium italic truncate"
                    >
                      No name
                    </p>

                    <UTooltip
                      v-if="!userRole.user.confirmed_at"
                      text="Invitation pending"
                      :popper="{ placement: 'right' }"
                    >
                      <UIcon
                        name="heroicons:clock"
                        class="text-orange-500 dark:text-orange-400 text-base"
                      />
                    </UTooltip>
                  </div>
                  <p class="text-gray-500 dark:text-gray-400 truncate">
                    {{ userRole.user.email }}
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <p
                  v-if="userRole.role === 'superuser'"
                  class="text-gray-500 dark:text-gray-400 italic"
                >
                  Owner
                </p>
                <p
                  v-else-if="!isSuperuser"
                  class="text-gray-500 dark:text-gray-400 italic capitalize"
                >
                  {{ userRole.role }}
                </p>
                <USelect
                  v-else
                  :model-value="userRole.role"
                  :options="roles"
                  option-attribute="label"
                  :disabled="isUpdatingUserRole || !isSuperuser"
                  @update:model-value="
                    updateUserRole({ userId: userRole.user_id, role: $event })
                  "
                />

                <UDropdown
                  v-if="isSuperuser"
                  :items="getMemberActions(userRole)"
                >
                  <UButton
                    icon="heroicons:ellipsis-vertical"
                    color="gray"
                    variant="ghost"
                  />
                </UDropdown>
              </div>
            </template>
          </li>
        </ul>
      </div>
    </div>

    <InviteMemberModal v-if="isSuperuser" v-model="isInvitationVisible" />
    <EditMemberModal
      v-if="isSuperuser"
      v-model="editMemberModalState.isVisible"
      :member-id="editMemberModalState.memberId"
    />
  </div>
</template>
