<script setup lang="ts">
import { useMutation, useQueryClient } from "@tanstack/vue-query";
import { z } from "zod";

import { Query } from "~/types/query";

const schema = z.object({
  email: z.string().email("Invalid email"),
  role: z.enum(["employee", "admin"]),
});

const isVisible = defineModel<boolean>({ required: true });

const organizationId = useOrganizationId();
const queryClient = useQueryClient();
const toast = useToast();

const state = reactive<z.infer<typeof schema>>({
  email: "",
  role: "employee",
});
const inviteError = ref<Error | null>(null);

const { mutateAsync: inviteMember, isPending: isInviting } = useMutation({
  mutationFn: async () => {
    try {
      await $fetch(
        `/api/organizations/${organizationId.value}/members/invite`,
        {
          method: "POST",
          body: {
            email: state.email,
            role: state.role,
          },
        },
      );

      await queryClient.invalidateQueries({
        queryKey: [Query.USER_ROLES, organizationId.value],
      });

      toast.add({
        title: `Invitation sent to ${state.email}`,
      });

      isVisible.value = false;
    } catch (error) {
      throw new Error((error as { data: { message: string } }).data.message);
    }
  },
  onError: (error) => {
    inviteError.value = error;
  },
});

watch(isVisible, (value) => {
  if (value) {
    state.email = "";
    state.role = "employee";
    inviteError.value = null;
  }
});
</script>

<template>
  <UModal v-model="isVisible" prevent-close>
    <UForm :schema="schema" :state="state" @submit.prevent="inviteMember">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              Invite people
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="ph:x"
              class="-my-1"
              @click="isVisible = false"
            />
          </div>
          <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">
            Invite new members by email address
          </p>
        </template>

        <div class="space-y-4">
          <UFormGroup label="Email" name="email">
            <UInput v-model="state.email" placeholder="<EMAIL>" />
          </UFormGroup>

          <UFormGroup label="Role" name="role">
            <USelect
              v-model="state.role"
              :options="[
                { label: 'Employee', value: 'employee' },
                { label: 'Admin', value: 'admin' },
              ]"
              option-attribute="label"
            />
          </UFormGroup>

          <p v-if="inviteError" class="text-red-500 text-sm text-center">
            {{ inviteError.message }}
          </p>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-3">
            <UButton color="gray" variant="ghost" @click="isVisible = false">
              Cancel
            </UButton>
            <UButton type="submit" color="black" :loading="isInviting">
              Invite
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>
