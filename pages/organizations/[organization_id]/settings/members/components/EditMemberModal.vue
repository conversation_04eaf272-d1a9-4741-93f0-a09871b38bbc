<script setup lang="ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query";
import { z } from "zod";

import { Query } from "~/types/query";

const schema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must be at most 50 characters"),
});

type Props = {
  memberId: string;
};

const props = defineProps<Props>();

const isVisible = defineModel<boolean>({ required: true });

const supabase = useSupabaseClient();
const organizationId = useOrganizationId();
const queryClient = useQueryClient();

const state = reactive({
  name: "" as string | undefined,
});

const { isLoading } = useQuery({
  queryKey: [Query.USERS, () => props.memberId],
  queryFn: async () => {
    const { data, error } = await supabase
      .from("users")
      .select()
      .eq("id", props.memberId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    const metadata = data.raw_user_meta_data as
      | { display_name: string }
      | undefined;
    state.name = metadata?.display_name;

    return data;
  },
  enabled: isVisible,
});

const {
  mutateAsync: editMember,
  isPending: isEditingMember,
  error,
} = useMutation({
  mutationFn: async () => {
    try {
      if (!organizationId.value || !props.memberId) return;

      await $fetch(
        `/api/organizations/${organizationId.value}/members/${props.memberId}`,
        {
          method: "PATCH",
          body: {
            name: state.name,
          },
        },
      );

      await queryClient.invalidateQueries({
        queryKey: [Query.USERS, () => props.memberId],
      });
      await queryClient.invalidateQueries({ queryKey: [Query.USER_ROLES] });

      isVisible.value = false;
    } catch (error) {
      throw new Error((error as { data: { message: string } }).data.message);
    }
  },
});
</script>

<template>
  <UModal v-model="isVisible" prevent-close>
    <UForm :schema="schema" :state="state" @submit="editMember">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              Edit member
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="ph:x"
              class="-my-1"
              @click="isVisible = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <UFormGroup label="Member's name" name="name">
            <UInput
              id="name"
              v-model.trim="state.name"
              :disabled="isLoading || isEditingMember"
              name="name"
              leading-icon="heroicons:user"
              placeholder="Enter member's name"
              size="md"
            />
          </UFormGroup>

          <p v-if="error" class="text-red-500 text-sm text-center">
            {{ error.message }}
          </p>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-3">
            <UButton color="gray" variant="ghost" @click="isVisible = false">
              Cancel
            </UButton>
            <UButton
              type="submit"
              color="black"
              :loading="isEditingMember"
              :disabled="isLoading"
            >
              Save
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>
