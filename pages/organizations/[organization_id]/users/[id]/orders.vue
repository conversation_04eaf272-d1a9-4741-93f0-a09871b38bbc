<script setup lang="ts">
import OrderTable from "~/components/OrderTable.vue";
import { useOrdersByUser } from "~/queries/orders/useOrdersByUser";

const route = useRoute();
const organizationId = useOrganizationId();

const userId = route.params.id as string;

const {
  data: orders,
  isLoading,
  page,
  perPage,
  sort,
} = useOrdersByUser(userId);
</script>

<template>
  <OrderTable
    v-model:sort="sort"
    v-model:page="page"
    v-model:per-page="perPage"
    :orders="orders?.data"
    :count="orders?.count ?? 0"
    :is-loading="isLoading"
    :exclude-columns="['user_id']"
    :organization-id="organizationId"
  />
</template>
