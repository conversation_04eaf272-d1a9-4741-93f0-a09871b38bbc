<script setup lang="ts">
import UserTable from "~/pages/organizations/[organization_id]/users/components/UserTable.vue";

const count = ref(0);
const search = ref("");
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <div class="flex items-center gap-1.5">
        <h1 class="font-semibold">Users</h1>
        <UBadge v-if="count" color="green" variant="subtle" size="xs">
          {{ count }}
        </UBadge>
      </div>
    </template>

    <template #subheader>
      <div class="flex">
        <UInput
          v-model="search"
          icon="ph:magnifying-glass"
          placeholder="Search users..."
        >
          <template #trailing>
            <UButton
              v-show="search !== ''"
              color="gray"
              variant="link"
              icon="ph:x"
              :padded="false"
              class="pointer-events-auto"
              @click="search = ''"
            />
          </template>
        </UInput>
      </div>
    </template>

    <UserTable :search="search" @data-update="count = $event?.count ?? 0" />
  </NuxtLayout>
</template>
