<script setup lang="ts">
import type { BreadcrumbLink } from "#ui/types";
import { OrganizationRoute } from "~/types/route";

const route = useRoute();
const organizationId = useOrganizationId();

const userId = route.params.id as string;

const links = computed<BreadcrumbLink[]>(() => [
  {
    label: "Users",
    to: OrganizationRoute.USERS(organizationId.value),
  },
  {
    label: userId,
  },
]);

const tabs = computed(() => [
  {
    label: "Orders",
    icon: "ph:basket",
    to:
      OrganizationRoute.USERS(organizationId.value) + "/" + userId + "/orders",
  },
]);
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <UBreadcrumb :links="links" />
    </template>
    <template #subheader>
      <HorizontalNavigation :tabs="tabs" />
    </template>
    <NuxtPage />
  </NuxtLayout>
</template>
