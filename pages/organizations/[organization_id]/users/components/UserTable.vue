<script setup lang="ts">
import { useQuery } from "@tanstack/vue-query";
import dayjs from "dayjs";

import type { Tables } from "~/types/database.types";
import { Query } from "~/types/query";
import { OrganizationRoute } from "~/types/route";

type Props = {
  search?: string;
};

const props = defineProps<Props>();
const emit = defineEmits(["data-update"]);

const router = useRouter();
const supabase = useSupabaseClient();
const organizationId = useOrganizationId();

const page = ref(1);
const perPage = ref(10);

const { data: users, isLoading } = useQuery({
  queryKey: [Query.USERS, page, perPage, () => props.search],
  queryFn: async () => {
    const query = supabase
      .from("users")
      .select("*", { count: "estimated" })
      .range((page.value - 1) * perPage.value, page.value * perPage.value - 1);

    if (props.search) {
      query.or(
        `email.ilike."%${props.search}%",phone.ilike."%${props.search}%"`,
      );
    }

    const { data, count, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return { data, count };
  },
});

const columns = [
  {
    key: "id",
    label: "ID",
  },
  {
    key: "email",
    label: "Email",
    sortable: true,
  },
  {
    key: "phone",
    label: "Phone",
    sortable: true,
  },
  {
    key: "last_sign_in_at",
    label: "Last Sign In",
    sortable: true,
  },
  {
    key: "created_at",
    label: "Created At",
    sortable: true,
  },
];

const onClickUser = (user: Tables<"users">) => {
  router.push(`${OrganizationRoute.USERS(organizationId.value)}/${user.id}`);
};

watch(
  users,
  (data) => {
    emit("data-update", data);
  },
  { immediate: true },
);
</script>

<template>
  <UTable
    :columns="columns"
    :rows="users?.data"
    :is-loading="isLoading"
    class="border-b border-gray-300 dark:border-gray-700"
    @select="onClickUser"
  >
    <template #phone-data="{ row }: { row: Tables<'users'> }">
      {{ row.phone ? formatPhoneNumber(row.phone) : "" }}
    </template>
    <template #last_sign_in_at-data="{ row }: { row: Tables<'users'> }">
      {{
        row.last_sign_in_at
          ? dayjs(row.last_sign_in_at).format("D MMM YYYY")
          : ""
      }}
    </template>
    <template #created_at-data="{ row }: { row: Tables<'users'> }">
      {{ dayjs(row.created_at).format("D MMM YYYY") }}
    </template>
  </UTable>
  <div v-if="users?.data" class="flex justify-end px-3 py-3.5">
    <Pagination
      v-model="page"
      v-model:per-page="perPage"
      :total="users.count ?? 0"
    />
  </div>
</template>
