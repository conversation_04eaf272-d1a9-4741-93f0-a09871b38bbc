<script setup lang="ts">
import CouponCreationModal from "./components/CouponCreationModal.vue";
import CouponTable from "./components/CouponTable.vue";

const { isSuperuser } = useUserRole();

const search = ref("");
const isCreationVisible = ref(false);
const total = ref(0);
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <div class="flex items-center gap-1.5">
        <h1 class="font-semibold">Coupons</h1>
        <UBadge v-if="total" color="green" variant="subtle" size="xs">
          {{ total }}
        </UBadge>
      </div>
    </template>

    <template #subheader>
      <div class="flex items-center justify-between gap-2">
        <UInput
          v-model="search"
          icon="ph:magnifying-glass"
          placeholder="Search coupons..."
        >
          <template #trailing>
            <UButton
              v-show="search !== ''"
              color="gray"
              variant="link"
              icon="ph:x"
              :padded="false"
              class="pointer-events-auto"
              @click="search = ''"
            />
          </template>
        </UInput>

        <UButton
          v-if="isSuperuser"
          icon="heroicons:plus-small"
          color="gray"
          @click="isCreationVisible = true"
        >
          Create coupon
        </UButton>
      </div>
    </template>

    <CouponTable :search="search" @data-update="total = $event" />
    <CouponCreationModal v-if="isSuperuser" v-model="isCreationVisible" />
  </NuxtLayout>
</template>
