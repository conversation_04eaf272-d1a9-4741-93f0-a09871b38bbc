<script setup lang="ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query";
import { refDebounced } from "@vueuse/core";
import dayjs from "dayjs";

import { useConfirmationDialog } from "~/composables/useConfirmationDialog";
import type { Tables } from "~/types/database.types";
import { Query } from "~/types/query";

type Props = {
  search?: string;
};

const props = defineProps<Props>();
const emit = defineEmits(["data-update"]);

const supabase = useSupabaseClient();
const queryClient = useQueryClient();
const toast = useToast();
const { areYouSure } = useConfirmationDialog();
const { isSuperuser } = useUserRole();
const organizationId = useOrganizationId();

const search = computed(() => props.search);
const searchDebounced = refDebounced(search, 500);
const sort = ref<{ column: string; direction: "desc" | "asc" }>({
  column: "created_at",
  direction: "desc",
});
const sortDebounced = refDebounced(sort, 500);
const page = ref(1);
const perPage = ref(10);

const { data: coupons, isLoading } = useQuery({
  queryKey: [
    Query.COUPONS,
    organizationId,
    searchDebounced,
    sortDebounced,
    page,
    perPage,
  ],
  queryFn: async () => {
    const query = supabase
      .from("coupons")
      .select("*", { count: "estimated" })
      .order(sortDebounced.value.column, {
        ascending: sortDebounced.value.direction === "asc",
      })
      .range((page.value - 1) * perPage.value, page.value * perPage.value - 1);

    if (organizationId.value !== ALL_ORGANIZATIONS) {
      query.eq("organization_id", organizationId.value);
    }

    if (searchDebounced.value) {
      query.or(`code.ilike."%${searchDebounced.value}%"`);
    }

    const { data, count, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      data,
      count,
    };
  },
});

const columns = [
  {
    key: "code",
    label: "Code",
    sortable: true,
  },
  {
    key: "discount_amount",
    label: "Discount",
    sortable: true,
  },
  {
    key: "expires_at",
    label: "Expires At",
    sortable: true,
  },
  {
    key: "created_at",
    label: "Created At",
    sortable: true,
  },
  {
    key: "actions",
  },
];

const { mutateAsync: deleteCoupon } = useMutation({
  mutationFn: async (coupon: Tables<"coupons">) => {
    await $fetch("/api/promo-codes", {
      method: "DELETE",
      query: {
        coupon_id: coupon.id,
      },
    });

    await queryClient.invalidateQueries({ queryKey: [Query.COUPONS] });

    toast.add({
      title: "Coupon deleted successfully",
    });
  },
  onError: () => {
    toast.add({
      title: "Failed to delete coupon",
      color: "red",
    });
  },
});

const { copy } = useCopyToClipboard();

const copyToClipboard = (text: string) => {
  copy(text, {
    title: "Copied to clipboard",
  });
};

watch(
  coupons,
  (value) => {
    emit("data-update", value?.count ?? 0);
  },
  { immediate: true },
);
</script>

<template>
  <UTable
    v-model:sort="sort"
    sort-mode="manual"
    :loading="isLoading"
    :rows="coupons?.data"
    :columns="columns"
    class="border-b border-gray-300 dark:border-gray-700"
  >
    <template #code-data="{ row }: { row: Tables<'coupons'> }">
      <div class="flex items-center gap-1">
        <p class="text-gray-800 dark:text-gray-300 font-medium uppercase">
          {{ row.code }}
        </p>
        <UTooltip text="Copy code" :popper="{ placement: 'top' }">
          <UButton
            icon="heroicons:square-2-stack"
            color="gray"
            size="xs"
            variant="ghost"
            :padded="false"
            @click="copyToClipboard(row.code.toUpperCase())"
          />
        </UTooltip>
      </div>
    </template>
    <template #discount_amount-data="{ row }: { row: Tables<'coupons'> }">
      <p v-if="row.discount_type === 'amount'">
        {{
          formatCurrency(row.discount_amount / 100, {
            currencyDisplay: "narrowSymbol",
          })
        }}
      </p>
      <p v-else>{{ row.discount_amount }}%</p>
    </template>
    <template #expires_at-data="{ row }: { row: Tables<'coupons'> }">
      <p v-if="row.expires_at">
        {{ dayjs(row.expires_at).format("D MMM YYYY") }}
      </p>
      <p v-else>-</p>
    </template>
    <template #created_at-data="{ row }: { row: Tables<'coupons'> }">
      <p>{{ dayjs(row.created_at).format("D MMM YYYY") }}</p>
    </template>
    <template
      v-if="isSuperuser"
      #actions-data="{ row }: { row: Tables<'coupons'> }"
    >
      <UTooltip text="Delete coupon" :popper="{ placement: 'top' }">
        <UButton
          icon="heroicons:trash"
          color="gray"
          variant="ghost"
          @click="areYouSure(() => deleteCoupon(row))"
        />
      </UTooltip>
    </template>
  </UTable>

  <div v-if="coupons?.data" class="flex justify-end px-3 py-3.5">
    <Pagination
      v-model="page"
      v-model:per-page="perPage"
      :total="coupons.count ?? 0"
    />
  </div>
</template>
