<script setup lang="ts">
import { useMutation, useQueryClient } from "@tanstack/vue-query";
import dayjs from "dayjs";
import { z } from "zod";

import { Query } from "~/types/query";

enum CouponType {
  Percentage = "percentage",
  Fixed = "fixed",
}

const schema = computed(() =>
  z.object({
    code: z
      .string()
      .regex(/^[a-zA-Z0-9]*$/, "Invalid code")
      .min(6, "Must be at least 6 characters")
      .max(20, "Must be at most 20 characters")
      .nullable()
      .optional(),
    selectedType: z.nativeEnum(CouponType, { message: "Invalid type" }),
    amount:
      state.selectedType === CouponType.Fixed
        ? z.number().gt(0, "Must be greater than 0")
        : z
            .number()
            .gt(0, "Must be greater than 0")
            .lte(100, "Must be less than or equal to 100"),
    redeemBy: z
      .date()
      .min(dayjs().subtract(1, "day").toDate(), "Must be a future date"),
    usageLimitPerUser: z
      .number()
      .gt(0, "Must be greater than 0")
      .nullable()
      .optional(),
  }),
);

const queryClient = useQueryClient();
const toast = useToast();
const organizationId = useOrganizationId();

const isVisible = defineModel<boolean>({ required: true });

const typeOptions = [
  {
    value: CouponType.Percentage,
    label: "Percentage discount",
  },
  {
    value: CouponType.Fixed,
    label: "Fixed amount discount",
  },
];

const state = reactive({
  code: undefined as string | undefined,
  selectedType: CouponType.Percentage,
  amount: undefined as number | undefined,
  redeemBy: undefined as Date | undefined,
  usageLimitPerUser: undefined as number | undefined,
});

const { mutate: createCoupon, isPending: isCreatingCoupon } = useMutation({
  mutationFn: async () => {
    if (organizationId.value === ALL_ORGANIZATIONS) {
      throw new Error("Select an organization first");
    }

    if (!state.amount) {
      throw new Error("Amount is required");
    }

    if (!state.redeemBy) {
      throw new Error("Redeem by date is required");
    }

    await $fetch("/api/promo-codes", {
      method: "POST",
      body: {
        code: state.code || undefined,
        amount_off:
          state.selectedType === CouponType.Fixed
            ? state.amount * 100
            : undefined,
        percent_off:
          state.selectedType === CouponType.Percentage
            ? state.amount
            : undefined,
        redeem_by: dayjs(state.redeemBy).endOf("day").unix(),
        usage_limit_per_user: state.usageLimitPerUser || undefined,
        organization_id: organizationId.value,
      },
    });

    await queryClient.invalidateQueries({ queryKey: [Query.COUPONS] });

    isVisible.value = false;

    toast.add({
      title: "Coupon created successfully",
    });
  },
});

watch(isVisible, (value) => {
  if (value) {
    state.code = undefined;
    state.selectedType = CouponType.Percentage;
    state.amount = undefined;
    state.redeemBy = undefined;
    state.usageLimitPerUser = undefined;
  }
});
</script>

<template>
  <UModal v-model="isVisible" prevent-close>
    <UForm :schema="schema" :state="state" @submit="createCoupon">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              Create coupon
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="ph:x"
              class="-my-1"
              @click="isVisible = false"
            />
          </div>
        </template>

        <div class="space-y-6">
          <UFormGroup
            label="Code"
            description="Leave blank for a random code"
            name="code"
          >
            <UInput
              v-model="state.code"
              placeholder="eg. WELCOME"
              :model-modifiers="{ nullify: true }"
              input-class="uppercase"
            />
          </UFormGroup>
          <UFormGroup label="Type" name="selectedType">
            <URadioGroup v-model="state.selectedType" :options="typeOptions" />
          </UFormGroup>

          <UFormGroup
            :label="
              state.selectedType === CouponType.Percentage
                ? 'Percentage off'
                : 'Discount amount'
            "
            name="amount"
          >
            <UInput
              v-model="state.amount"
              :model-modifiers="{ nullify: true }"
              type="number"
              min="0"
              step="0.01"
              :placeholder="
                state.selectedType === CouponType.Percentage ? '0' : '0.00'
              "
              input-class="pr-12"
              @update:model-value="
                state.amount = $event === null ? undefined : $event
              "
            >
              <template #trailing>
                <span class="text-gray-500 dark:text-gray-400 text-sm">
                  {{
                    state.selectedType === CouponType.Percentage ? "%" : "CA$"
                  }}
                </span>
              </template>
            </UInput>
          </UFormGroup>

          <UFormGroup
            label="Redeem by"
            description="Coupon expires after this date"
            name="redeemBy"
          >
            <div class="flex">
              <DatePicker v-model="state.redeemBy" :min-date="new Date()" />
            </div>
          </UFormGroup>

          <UFormGroup
            label="Usage limit per user"
            description="Leave blank if no limit"
            name="usageLimitPerUser"
          >
            <UInput
              v-model="state.usageLimitPerUser"
              :model-modifiers="{ nullify: true }"
              type="number"
              min="0"
              placeholder="No limit"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex items-center justify-end gap-3">
            <UButton color="gray" variant="ghost" @click="isVisible = false">
              Cancel
            </UButton>
            <UButton type="submit" :loading="isCreatingCoupon">
              Create coupon
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>
