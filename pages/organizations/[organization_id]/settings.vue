<script setup lang="ts">
import { OrganizationRoute } from "~/types/route";

const organizationId = useOrganizationId();
const { isSuperuser } = useUserRole();

const tabs = computed(() =>
  [
    {
      label: "General",
      icon: "heroicons:user-circle",
      to: OrganizationRoute.SETTINGS(organizationId.value),
    },
    {
      label: "Members",
      icon: "heroicons:user-group",
      to: OrganizationRoute.SETTINGS(organizationId.value) + "/members",
      hidden: !isSuperuser.value,
    },
  ].filter((item) => !item.hidden),
);
</script>

<template>
  <NuxtLayout name="dashboard">
    <template #header>
      <h1 class="font-semibold">Settings</h1>
    </template>
    <template #subheader>
      <HorizontalNavigation :tabs="tabs" />
    </template>
    <NuxtPage />
  </NuxtLayout>
</template>
