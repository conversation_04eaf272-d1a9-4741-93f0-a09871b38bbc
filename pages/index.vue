<script setup lang="ts">
import { OrganizationRoute, Route } from "~/types/route";

const { organizations, isLoading } = useOrganizations();
</script>

<template>
  <div class="flex-1 flex flex-col justify-center">
    <Loading v-if="isLoading" />
    <div
      v-else-if="!organizations?.length"
      class="w-full max-w-lg mx-auto px-4"
    >
      <UAlert
        title="You don't have any teams yet."
        description="Ask for an invitation from your team's admin first."
      />
      <div class="flex justify-center mt-4">
        <UButton :to="Route.LOGIN"> Return to Login </UButton>
      </div>
    </div>
    <div v-else>
      <h1
        class="text-2xl text-gray-900 dark:text-white font-semibold text-center mb-4"
      >
        Your Teams
      </h1>
      <ul class="grid gap-2 w-full max-w-lg mx-auto px-4">
        <li>
          <UButton
            color="white"
            size="xl"
            block
            truncate
            :to="OrganizationRoute.HOME(ALL_ORGANIZATIONS)"
            class="group px-6 py-4"
          >
            <div class="flex items-center gap-2 w-full text-xl">
              <UIcon
                name="heroicons:presentation-chart-bar"
                class="size-8 text-gray-700 dark:text-gray-200"
              />
              <p>Overview</p>
              <UIcon
                name="heroicons:arrow-right-20-solid"
                class="hidden group-hover:block ml-auto"
              />
            </div>
          </UButton>
        </li>
        <li v-for="(organization, index) in organizations" :key="index">
          <UButton
            color="white"
            size="xl"
            block
            truncate
            :to="OrganizationRoute.HOME(organization.id)"
            class="group px-6 py-4"
          >
            <div class="flex items-center gap-2 w-full text-xl">
              <UAvatar :src="getOrganizationAvatarUrl(organization.name)" />
              <p>{{ organization.name }}</p>
              <UIcon
                name="heroicons:arrow-right-20-solid"
                class="hidden group-hover:block ml-auto"
              />
            </div>
          </UButton>
        </li>
      </ul>
      <div class="flex justify-center mt-12">
        <UButton color="gray" variant="link" :to="Route.LOGIN">
          Back to Login
        </UButton>
      </div>
    </div>
  </div>
</template>
