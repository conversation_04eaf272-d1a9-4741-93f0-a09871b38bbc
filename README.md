# HIKO Admin Dashboard

The HIKO Admin Dashboard is a Nuxt 3 application that provides business management interface for the HIKO battery rental system. It allows organizations to manage their battery stations, monitor usage analytics, handle user accounts, create promotional coupons, and configure organizational settings.

## Features

- **Organization Management**: Multi-tenant architecture supporting multiple organizations
- **Battery Station Management**: Monitor and manage battery stations with real-time status
- **User Management**: View user accounts, order history, and activity
- **Analytics Dashboard**: Revenue tracking, usage charts, and performance metrics
- **Coupon Management**: Create and manage promotional codes and discounts
- **Member Management**: Invite and manage organization members with role-based access

## Technologies Used

- **Nuxt 3**: Vue.js framework for building universal applications
- **Nuxt UI**: Component library built on Tailwind CSS and Headless UI
- **Supabase**: Backend-as-a-Service for database, authentication, and real-time features
- **Tanstack Vue Query**: Powerful data fetching and caching library
- **ApexCharts**: Interactive charts and data visualization
- **Stripe**: Payment processing integration
- **Google Maps**: Address autocomplete and geocoding services
- **Cloudflare Turnstile**: CAPTCHA protection for forms

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm, pnpm, yarn, or bun package manager
- Supabase account with a project created
- External service accounts (see Environment Configuration)

### Installation

1. Clone this repository
2. Install dependencies: `npm install`
3. Configure environment variables (see below)
4. Start the development server: `npm run dev`

The application will be available at `http://localhost:5174`

### Environment Configuration

Copy `.env.example` to `.env` and configure the following services:

- **Supabase**: Database and authentication backend
- **Stripe**: Payment processing
- **Google Maps**: Address autocomplete services
- **Cloudflare Turnstile**: CAPTCHA protection (optional for development)
- **Carku**: Battery station hardware API

See `.env.example` for the complete list of required environment variables and their descriptions.

### Supabase Setup

1. Create a new Supabase project (or use existing HIKO project)
2. Configure email authentication in Supabase Auth settings
3. Set up authentication URLs for local development (`http://localhost:5174`)
4. Ensure the database schema includes required tables for organizations, users, stations, orders, and coupons

### Development Data

For local development with admin users and organizational data:

```bash
npm run db:seed            # Create admin users and check existing data
```

This creates admin users and assigns proper roles. If data exists from the user app, it will be detected and used. See [DATABASE_SEEDING.md](../DATABASE_SEEDING.md) for details.

### External Services

Configure the following services and add their API keys to your `.env` file:

- **Supabase**: For database, auth, and real-time features
- **Stripe**: For payment processing and financial data
- **Google Maps**: For address services and geocoding
- **Cloudflare Turnstile**: For CAPTCHA protection (set `NUXT_TURNSTILE_ENABLED=false` for development)

Refer to `.env.example` for service-specific configuration details.

## Available Scripts

- `npm run dev` - Start development server on port 5174
- `npm run build` - Build for production (Cloudflare Pages preset)
- `npm run preview` - Preview production build locally
- `npm run generate` - Generate static site
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run types:generate` - Generate TypeScript types from Supabase schema

## Authentication & Access

The admin dashboard uses email/password authentication with organization-based access control. Users must be associated with an organization and have appropriate roles to access different features.

For development, CAPTCHA protection can be disabled by setting `NUXT_TURNSTILE_ENABLED=false` in your `.env` file.

## Deployment

The application is configured for Cloudflare Pages deployment. Build the application with `npm run build` and deploy the `.output/public/` directory.
