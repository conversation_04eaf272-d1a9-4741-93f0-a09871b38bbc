import type { UndefinedInitialQueryOptions } from "@tanstack/vue-query";

export enum Query {
  STATIONS = "STATIONS",
  STATION_SLOTS = "STATION_SLOTS",
  STATION_LOCATIONS = "STATION_LOCATIONS",
  COUPONS = "COUPONS",
  ADDRESS_AUTOCOMPLETE = "ADDRESS_AUTOCOMPLETE",
  INCOME_PER_DAY = "INCOME_PER_DAY",
  ORDERS = "ORDERS",
  ORDERS_PER_STATION = "ORDERS_PER_STATION",
  USERS = "USERS",
  USER_ROLES = "USER_ROLES",
  ORGANIZATIONS = "ORGANIZATIONS",
}

export type QueryOptions<TData> = Partial<
  UndefinedInitialQueryOptions<TData, Error, TData, MaybeRef>
>;
