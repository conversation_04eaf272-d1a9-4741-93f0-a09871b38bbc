export const StationEvent = {
  HeartBeat: "heartbeat",
  ReturnBack: "return_back",
  PopupConfirm: "popup_confirm",
  RentConfirm: "rent_confirm",
  SyncSetting: "sync_setting",
  QueryConfirm: "query_confirm",
  SlotLock: "slot_lock",
  SlotUnlock: "slot_unlock",
  Reboot: "reboot",
  SyncBattery: "sync_battery",
  PopupConfirmWithCable: "popup_confirm_with_cable",
  EditLockMode: "edit_lockmode",
} as const;

export type BatteryStationResponse = {
  data: unknown;
  code: number;
  msg: string;
};

type ValueOf<T> = T[keyof T];

export type ApiOption = ValueOf<typeof StationEvent>;

export enum BatteryCableType {
  Lightning = 1,
  MicroUSB = 2,
  USBTypeC = 3,
}
