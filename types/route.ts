export enum Route {
  HOME = "/",
  LOGIN = "/login",
  UPDATE_PASSWORD = "/update-password",
}

export const OrganizationRoute = {
  HOME: (organizationId: string) => `/organizations/${organizationId}`,
  DASHBOARD: (organizationId: string) =>
    `/organizations/${organizationId}/dashboard`,
  STATIONS: (organizationId: string) =>
    `/organizations/${organizationId}/stations`,
  COUPONS: (organizationId: string) =>
    `/organizations/${organizationId}/coupons`,
  USERS: (organizationId: string) => `/organizations/${organizationId}/users`,
  SETTINGS: (organizationId: string) =>
    `/organizations/${organizationId}/settings`,
};
