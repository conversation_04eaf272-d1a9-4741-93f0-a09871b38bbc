# Database Seeding Guide

This guide explains how to populate your local development database with realistic test data for both HIKO applications.

## Overview

The HIKO project includes factory-based database seeding scripts that create realistic test data for local development. These scripts:

- ✅ **Environment Protected**: Never run in production
- ✅ **Factory-Based**: Use realistic data generation patterns
- ✅ **Comprehensive**: Cover all major database entities
- ✅ **Relationship Aware**: Maintain proper foreign key relationships
- ✅ **Idempotent**: Safe to run multiple times

## Prerequisites

1. **Supabase Project**: Configured and accessible
2. **Environment Variables**: Properly set in both applications
3. **Database Schema**: Migrations applied
4. **Dependencies**: `@faker-js/faker` installed

## User Application Seeding (hiko-app)

### Installation

```bash
cd hiko-app
npm install  # Includes @faker-js/faker and tsx
```

### Usage

```bash
# Seed development database with comprehensive test data
npm run db:seed

# Alternative command
npm run db:seed:dev
```

### What Gets Created

The user app seeder creates:

- **Organizations** (4): Main development org + 3 additional
- **Stations** (11): 5 Montreal locations + 6 random locations  
- **Station Locations**: Realistic Montreal addresses + random locations
- **Station Slots** (60+): 4-12 slots per station with batteries
- **Users** (10): 3 test users + 7 customers
- **User Roles**: Admin/member/viewer assignments
- **Orders** (150): Realistic rental/purchase history
- **Coupons** (12): Promotional codes per organization
- **Favorite Stations**: User preference data

### Test Users Created

| Email | Role | Purpose |
|-------|------|---------|
| `<EMAIL>` | Admin | Full system access |
| `<EMAIL>` | Member | Demo/presentation user |
| `<EMAIL>` | Viewer | Limited access testing |

### Factory Architecture

The seeding system uses factory classes for each entity:

```
scripts/seed/
├── factories/
│   ├── base.ts              # BaseFactory class + utilities
│   ├── organization.factory.ts
│   ├── station.factory.ts   # Station, Location, Slot factories
│   ├── user.factory.ts      # User, UserRole factories  
│   ├── order.factory.ts     # Order, Coupon, Favorites factories
└── seeders/
    └── development.seeder.ts # Main seeding orchestrator
```

## Admin Dashboard Seeding (hiko-admin)

### Installation

```bash
cd hiko-admin
npm install  # Includes @faker-js/faker
```

### Usage

```bash
# Seed admin users and check for existing data
npm run db:seed

# Alternative command  
npm run db:seed:dev
```

### What Gets Created

The admin seeder:

1. **Checks Existing Data**: Reports what's already in the database
2. **Creates Admin Users**: If no admin users exist
3. **Assigns Roles**: Admin permissions for dashboard access
4. **Smart Detection**: Uses existing data from user app if available

### Admin Users Created

| Email | Role | Purpose |
|-------|------|---------|
| `<EMAIL>` | Admin | Dashboard administration |
| `<EMAIL>` | Admin | Secondary admin account |

## Environment Protection

Both seeders include multiple safety layers:

### 1. Environment Check
```javascript
if (process.env.NODE_ENV === 'production') {
  throw new Error('🚫 Database seeding is not allowed in production environment!');
}
```

### 2. URL Analysis
```javascript
if (supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('localhost')) {
  const isProduction = !supabaseUrl.includes('dev') && !supabaseUrl.includes('test');
  if (isProduction) {
    console.warn('⚠️  Warning: This appears to be a production Supabase URL');
    // 5 second delay with option to cancel
  }
}
```

### 3. Script Configuration
```bash
NODE_ENV=development npx tsx scripts/seed/seeders/development.seeder.ts
```

## Development Workflow

### First Time Setup

1. **Set up environment**:
   ```bash
   cp .env.example .env
   # Configure your Supabase credentials
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Seed user application**:
   ```bash
   cd hiko-app
   npm run db:seed
   ```

4. **Seed admin dashboard**:
   ```bash
   cd hiko-admin  
   npm run db:seed
   ```

5. **Create authentication**:
   - Go to Supabase Auth Users panel
   - Create/update password for `<EMAIL>`
   - Test login at http://localhost:5174

### Daily Development

- **Fresh data**: Re-run `npm run db:seed` to get fresh test data
- **Additional users**: The factories make it easy to create more specific test scenarios
- **Data exploration**: Use the seeded data to test different app features

## Factory Usage Examples

### Creating Custom Test Data

```typescript
// Create a specific organization
const orgFactory = new OrganizationFactory(supabase);
const myOrg = await orgFactory.create({ name: 'My Test Org' });

// Create users for that organization  
const userFactory = new UserFactory(supabase);
const testUsers = await userFactory.createMany(5);

// Create stations with realistic Montreal locations
const stationFactory = new StationFactory(supabase);
const stations = await stationFactory.createMontrealStations(myOrg.id);
```

### Extending Factories

Add new factory methods for specific test scenarios:

```typescript
// In organization.factory.ts
async createTestRestaurantChain() {
  return this.createManyWithOverrides([
    { name: 'Pizza Palace Downtown' },
    { name: 'Pizza Palace Westmount' },
    { name: 'Pizza Palace Plateau' },
  ]);
}
```

## Troubleshooting

### Common Issues

**"Missing Supabase configuration"**
- Verify `.env` file exists and has correct `PUBLIC_SUPABASE_URL` and `PUBLIC_SUPABASE_ANON_KEY`

**"Failed to create [entity]"**
- Check Supabase RLS policies allow inserts
- Verify all required foreign keys exist
- Ensure database schema is up to date

**"Database seeding is not allowed in production"**
- This is working correctly! Don't override this protection.

**Seeding takes a long time**
- Normal for first run with 150+ records
- Subsequent runs are faster due to database optimization

### Reset Database

To start completely fresh:

```bash
# Clear all data (be careful!)
npx supabase db reset  # If using local Supabase

# Re-run migrations
npx supabase db push

# Re-seed
npm run db:seed
```

## Best Practices

1. **Run seeders after schema changes** to ensure data matches new structure
2. **Create specific factories** for testing edge cases
3. **Use realistic data** to catch real-world issues during development
4. **Document custom seed scenarios** for team members
5. **Never modify production protection** - it's there for a reason!

## Contributing

When adding new database entities:

1. Create a factory class following the `BaseFactory` pattern
2. Add realistic data generation with `faker.js`
3. Include the factory in the main seeder
4. Update this documentation

The factory pattern ensures consistent, maintainable test data generation across the entire application.