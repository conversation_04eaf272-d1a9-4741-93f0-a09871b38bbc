# Component Refactoring Analysis

## Overview

This analysis identifies component-specific refactoring opportunities to improve reusability, maintainability, and performance in the HIKO SvelteKit application.

## Critical Component Issues

### 1. BatteryStationMap.svelte - Major Refactoring Required

**Location**: `src/components/BatteryStationMap.svelte`  
**Size**: 405 lines  
**Complexity**: High

#### Problems Identified:
- **Multiple Responsibilities**: Map rendering, station management, user location, order handling
- **Complex State**: 15+ reactive variables managing different aspects
- **Long Functions**: `placeStationMarkers()` spans 40+ lines
- **Tight Coupling**: Direct API calls mixed with UI logic

#### Current Structure Issues:
```svelte
<script lang="ts">
  // Map state
  let map: Map;
  let userMarker: Marker;
  let placeMarker: Marker;
  
  // Station state
  let stations: Tables<"stations">[] = [];
  const stationsLayerGroups: LayerGroup[] = [];
  
  // UI state
  let isPlacePreviewVisible = false;
  let placeAddress: Address;
  
  // User state
  const { latitude, longitude } = useGeolocation();
  
  // Order state
  const { order } = useActiveOrder();
  
  // Complex function mixing concerns
  const placeStationMarkers = async () => {
    // 40+ lines mixing business logic with UI updates
  };
</script>
```

#### Recommended Refactoring:

**Extract Composables:**
```typescript
// src/lib/composables/use-map-manager.ts
export function useMapManager() {
  let map: Map;
  let userMarker: Marker;
  
  const initializeMap = (container: HTMLElement) => {
    // Map initialization logic
  };
  
  const updateUserLocation = (lat: number, lng: number) => {
    // User location updates
  };
  
  return {
    map: readonly(map),
    initializeMap,
    updateUserLocation,
    // Other map-specific methods
  };
}

// src/lib/composables/use-station-markers.ts
export function useStationMarkers(map: Readable<Map>) {
  const stationGroups = writable<LayerGroup[]>([]);
  
  const placeStationMarkers = (stations: Station[]) => {
    // Clean, focused marker placement logic
  };
  
  const clearStationMarkers = () => {
    // Cleanup logic
  };
  
  return {
    stationGroups: readonly(stationGroups),
    placeStationMarkers,
    clearStationMarkers
  };
}
```

**Refactored Component Structure:**
```svelte
<!-- BatteryStationMap.svelte -->
<script lang="ts">
  import { useMapManager } from '$lib/composables/use-map-manager';
  import { useStationMarkers } from '$lib/composables/use-station-markers';
  import { useLocationTracker } from '$lib/composables/use-location-tracker';
  
  const mapManager = useMapManager();
  const stationMarkers = useStationMarkers(mapManager.map);
  const locationTracker = useLocationTracker();
  
  // Simplified component with clear separation of concerns
</script>

<div class="map-container">
  <MapView bind:map={mapManager.map} />
  
  {#if $isStationPreviewVisible}
    <StationPreview />
  {/if}
  
  {#if $isPlacePreviewVisible}
    <PlacePreview />
  {/if}
</div>
```

### 2. OrderItem.svelte - Timer Logic Extraction

**Location**: `src/components/OrderItem.svelte`  
**Issues**: Timer logic mixed with display logic

#### Current Problems:
```svelte
<script>
  let timeRemaining = '';
  
  $: if (order.status === 'active') {
    // Complex timer logic directly in component
    const updateTimer = () => {
      const now = dayjs();
      const endTime = dayjs(order.ends_at);
      const diff = endTime.diff(now);
      
      if (diff <= 0) {
        timeRemaining = 'Expired';
        // Direct API call from component
        api.post('/api/orders/expire', { orderId: order.id });
      } else {
        timeRemaining = formatDuration(diff);
      }
    };
    
    const interval = setInterval(updateTimer, 1000);
    updateTimer();
    
    return () => clearInterval(interval);
  }
</script>
```

#### Recommended Solution:
```typescript
// src/lib/composables/use-order-timer.ts
export function useOrderTimer(order: Readable<Order>) {
  const timeRemaining = writable('');
  const isExpired = writable(false);
  
  const startTimer = () => {
    const updateTimer = () => {
      const current = get(order);
      if (!current || current.status !== 'active') return;
      
      const now = dayjs();
      const endTime = dayjs(current.ends_at);
      const diff = endTime.diff(now);
      
      if (diff <= 0) {
        timeRemaining.set('Expired');
        isExpired.set(true);
      } else {
        timeRemaining.set(formatDuration(diff));
        isExpired.set(false);
      }
    };
    
    const interval = setInterval(updateTimer, 1000);
    updateTimer();
    
    return () => clearInterval(interval);
  };
  
  return {
    timeRemaining: readonly(timeRemaining),
    isExpired: readonly(isExpired),
    startTimer
  };
}
```

**Simplified Component:**
```svelte
<script>
  import { useOrderTimer } from '$lib/composables/use-order-timer';
  
  export let order: Order;
  
  const timer = useOrderTimer(writable(order));
  
  onMount(() => {
    const cleanup = timer.startTimer();
    return cleanup;
  });
</script>

<div class="order-item">
  <div class="time-remaining">
    {$timer.timeRemaining}
  </div>
  
  {#if $timer.isExpired}
    <div class="expired-notice">Order expired</div>
  {/if}
</div>
```

### 3. PaymentMethodForm.svelte - Separation of Concerns

**Location**: `src/components/PaymentMethodForm.svelte`  
**Issues**: Payment logic mixed with form rendering, poor error handling

#### Current Problems:
```svelte
<script>
  let cardNumber = '';
  let errors: Record<string, string> = {};
  
  const handleSubmit = async () => {
    try {
      // Direct Stripe integration in component
      const { token, error } = await stripe.createToken(card);
      
      if (error) {
        errors = { card: error.message };
        return;
      }
      
      // Direct API call
      const response = await api.post('/api/payments/methods', {
        token: token.id
      });
      
      // Business logic in component
      if (response.data.success) {
        goto('/payment-methods');
      }
    } catch (err) {
      errors = { general: 'Something went wrong' };
    }
  };
</script>
```

#### Recommended Solution:

**Extract Payment Service:**
```typescript
// src/lib/domains/payments/services/payment-method-service.ts
export class PaymentMethodService {
  constructor(
    private stripe: StripeClient,
    private api: ApiClient
  ) {}
  
  async createPaymentMethod(cardData: CardData): Promise<PaymentMethod> {
    const { token, error } = await this.stripe.createToken(cardData);
    
    if (error) {
      throw new PaymentError(error.message, 'STRIPE_TOKEN_ERROR');
    }
    
    const response = await this.api.post<PaymentMethod>('/api/payments/methods', {
      token: token.id
    });
    
    return response.data;
  }
}
```

**Form Validation Composable:**
```typescript
// src/lib/composables/use-form-validation.ts
export function useFormValidation<T>(schema: z.ZodSchema<T>) {
  const errors = writable<Record<string, string>>({});
  const isValid = writable(false);
  
  const validate = (data: Partial<T>): boolean => {
    try {
      schema.parse(data);
      errors.set({});
      isValid.set(true);
      return true;
    } catch (err) {
      if (err instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        err.errors.forEach(error => {
          fieldErrors[error.path.join('.')] = error.message;
        });
        errors.set(fieldErrors);
      }
      isValid.set(false);
      return false;
    }
  };
  
  return {
    errors: readonly(errors),
    isValid: readonly(isValid),
    validate
  };
}
```

**Refactored Component:**
```svelte
<script lang="ts">
  import { useFormValidation } from '$lib/composables/use-form-validation';
  import { paymentMethodSchema } from '$lib/domains/payments/schemas';
  import { getPaymentMethodService } from '$lib/domains/payments/services';
  
  const validation = useFormValidation(paymentMethodSchema);
  const paymentService = getPaymentMethodService();
  
  let formData = {
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvc: ''
  };
  
  let isSubmitting = false;
  
  const handleSubmit = async () => {
    if (!validation.validate(formData)) return;
    
    isSubmitting = true;
    try {
      await paymentService.createPaymentMethod(formData);
      goto('/payment-methods');
    } catch (error) {
      // Handle error appropriately
    } finally {
      isSubmitting = false;
    }
  };
</script>

<form on:submit|preventDefault={handleSubmit}>
  <Input
    bind:value={formData.cardNumber}
    error={$validation.errors.cardNumber}
    placeholder="Card Number"
  />
  
  <Button
    type="submit"
    disabled={!$validation.isValid || isSubmitting}
    loading={isSubmitting}
  >
    Add Payment Method
  </Button>
</form>
```

## Component Patterns to Implement

### 1. Consistent Prop Patterns

**Current Issues**: Inconsistent prop definitions
```svelte
<!-- Inconsistent -->
export let order;  // No type
export let station: any;  // Too broad
export let loading = false;  // Inline default
```

**Recommended Pattern:**
```svelte
<script lang="ts">
  import type { Order, Station } from '$types';
  
  interface Props {
    order: Order;
    station: Station;
    loading?: boolean;
  }
  
  let { order, station, loading = false }: Props = $props();
</script>
```

### 2. Event Handling Standardization

**Current Issues**: Mixed event patterns
```svelte
<!-- Inconsistent event handling -->
<button on:click={() => handleClick(order.id)}>
<button on:click={handleAnotherClick}>
```

**Recommended Pattern:**
```typescript
// Define event types
interface ComponentEvents {
  orderSelect: { order: Order };
  stationClick: { station: Station; location: Coordinates };
}

// Use typed event dispatcher
const dispatch = createEventDispatcher<ComponentEvents>();

const handleOrderClick = (order: Order) => {
  dispatch('orderSelect', { order });
};
```

### 3. Loading States and Error Boundaries

**Create Reusable Patterns:**
```svelte
<!-- src/components/ui/AsyncComponent.svelte -->
<script lang="ts">
  interface Props {
    loading?: boolean;
    error?: string | null;
    retry?: () => void;
  }
  
  let { loading = false, error = null, retry }: Props = $props();
</script>

{#if loading}
  <div class="loading">
    <Loader />
  </div>
{:else if error}
  <div class="error">
    <p>{error}</p>
    {#if retry}
      <Button on:click={retry}>Retry</Button>
    {/if}
  </div>
{:else}
  <slot />
{/if}
```

## Performance Optimizations

### 1. Reduce Unnecessary Re-renders

**Problem**: Large lists re-rendering entirely
```svelte
<!-- Current: Full list re-render -->
{#each stations as station}
  <StationItem {station} />
{/each}
```

**Solution**: Keyed each blocks and memoization
```svelte
<!-- Optimized: Keyed rendering -->
{#each stations as station (station.id)}
  <StationItem {station} />
{/each}

<!-- Or use virtual scrolling for large lists -->
<VirtualList items={stations} let:item>
  <StationItem station={item} />
</VirtualList>
```

### 2. Debounce User Interactions

**For Search/Filter Components:**
```typescript
// src/lib/composables/use-debounced-search.ts
export function useDebouncedSearch(delay = 300) {
  const query = writable('');
  const debouncedQuery = writable('');
  
  let timeout: NodeJS.Timeout;
  
  query.subscribe(value => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      debouncedQuery.set(value);
    }, delay);
  });
  
  return {
    query,
    debouncedQuery: readonly(debouncedQuery)
  };
}
```

## Migration Priority

### High Priority (Immediate Impact)
1. **BatteryStationMap.svelte** - Extract composables and split component
2. **PaymentMethodForm.svelte** - Implement proper error handling and validation
3. **Create consistent prop patterns** across all components

### Medium Priority (Quality Improvements)
1. **OrderItem.svelte** - Extract timer logic
2. **Implement loading states** consistently
3. **Add error boundaries** for better UX

### Low Priority (Optimization)
1. **Virtual scrolling** for large lists
2. **Performance monitoring** for complex components
3. **Accessibility improvements** across all components

## Testing Strategy

After refactoring, implement tests for:
1. **Composables**: Unit tests for isolated logic
2. **Components**: Integration tests with mocked services
3. **User Interactions**: End-to-end tests for critical workflows

This component refactoring will significantly improve code maintainability, reduce complexity, and enhance the user experience through better error handling and performance optimizations.