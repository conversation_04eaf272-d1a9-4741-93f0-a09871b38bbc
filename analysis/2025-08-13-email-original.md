Bon<PERSON><PERSON> <PERSON>,
J’ai effectivement travaillé sur l’analyse du projet. Je propose que l’on se fasse un appel aujourd’hui afin de clarifier certain points.

Je me suis concentré à faire fonctionner le front-end et le backend en local. Le projet manquait de documentation et les configurations ne sont disponibles que pour les environnements en ligne, mais j’ai pris soin de régler une partie.

Je m’occupe d’appliquer les changements demandés par l’hôpital ce matin, je pourrai tenter un déploiement, mais j’aimerais valider les changements avec toi avant de mettre en ligne quoi que ce soit.

Nous avions discuté de plusieurs projets à analyser, je n’ai pas terminé mon travail, mais j’ai fait un bon survol des projets existants. 

Je me suis informé concernant la collecte de code postal via services de paiements et POS comme Stripe, Moneris et Clover. Dans tous les cas, il semble être possible de récupérer l’information, mais ceci nécessite d’être le vendeur et de demander explicitement l’information (ex; pour shipping). Les différents services de POS doivent être PCI DSS compliant et ils ne permettent pas de partager facilement les informations de code postal. J’ai valider cet information avec mon collègue qui travail régulièrement avec des POS et la billetterie Reservatech de TLM.

En ce qui concerne les autres projets, j’ai regardé rapidement ce qui est possible de faire avec le compte de displ (Display Force AI) et ils ne semblent pas exposer d’API même avec le compte payant. Le service d’API ne semble disponible que par leur tier White Label (celui plus dispendieux dont tu parlais). Tu pourrais probablement valider cet information en communiquant avec leur service à la clientèle, mais ça fait du sense puisque exposer une API, c’est un peu l’équivalent d’offrir un service de white label (ça permet d’utiliser les données sans que les clients ne sache que c’est leur service. Ce matin, je regarde ce qui est possible de faire avec la version du fichier exporté. On pourrait par exemple automatiser un robot qui récupère l’export des données, mais pour l’instant, ça ne me semble pas une option très intéressante.