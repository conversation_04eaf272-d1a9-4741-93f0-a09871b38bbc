# Hiko Admin - Refactoring Analysis

## Executive Summary

This analysis identifies key areas in the Hiko Admin codebase that would benefit from refactoring to improve maintainability, performance, and code organization. The codebase shows good modern practices with Nuxt 3, Vue 3 Composition API, and TanStack Query, but several patterns can be improved.

## Priority Levels
- 🔴 **HIGH**: Critical refactoring needed for maintainability/performance
- 🟡 **MEDIUM**: Moderate improvements that enhance code quality
- 🟢 **LOW**: Minor improvements for consistency/best practices

---

## 1. Component Structure Refactoring

### 🔴 HIGH: OrderTable.vue - Complex Component Breakdown

**File**: `components/OrderTable.vue`

**Issues**:
- Single component handling multiple responsibilities (180+ lines)
- Complex formatting logic mixed with presentation logic
- Hardcoded column definitions
- Status mapping logic embedded in component

**Recommended Refactoring**:
1. **Extract Order Status Logic**:
   ```typescript
   // composables/useOrderStatus.ts
   export const useOrderStatus = () => {
     const getStatusColor = (status: string): BadgeColor => { /* ... */ }
     const getStatusText = (status: string) => { /* ... */ }
     return { getStatusColor, getStatusText }
   }
   ```

2. **Extract Duration Formatting**:
   ```typescript
   // utils/dateFormatters.ts
   export const formatOrderDuration = (order: Tables<'orders'>) => { /* ... */ }
   ```

3. **Create Configurable Table Columns**:
   ```typescript
   // composables/useOrderTableColumns.ts
   export const useOrderTableColumns = (excludeColumns?: string[]) => {
     const defaultColumns = [/* ... */]
     return computed(() => defaultColumns.filter(/* ... */))
   }
   ```

**Benefits**:
- Improved testability
- Better separation of concerns
- Reusable business logic
- Easier maintenance

---

### 🟡 MEDIUM: RevenueChart.vue - Data Processing Logic

**File**: `pages/organizations/[organization_id]/dashboard/components/RevenueChart.vue`

**Issues**:
- Complex data transformation logic in component
- Hardcoded chart configuration
- Mixed concerns (data fetching + chart configuration)

**Recommended Refactoring**:
1. **Extract Chart Data Composable**:
   ```typescript
   // composables/useRevenueChartData.ts
   export const useRevenueChartData = (start: Date, end: Date, organizationId: Ref<string>) => {
     // Move data fetching and series computation here
     return { series, totalIncome, isLoading }
   }
   ```

2. **Create Chart Configuration Factory**:
   ```typescript
   // utils/chartConfigurations.ts
   export const createRevenueChartConfig = (series: any[], containerSize: any) => { /* ... */ }
   ```

---

### 🟡 MEDIUM: CouponCreationModal.vue - Form Logic Extraction

**File**: `pages/organizations/[organization_id]/coupons/components/CouponCreationModal.vue`

**Issues**:
- Complex form validation mixed with component logic
- Hardcoded coupon type definitions
- Mutation logic embedded in component

**Recommended Refactoring**:
1. **Extract Coupon Form Logic**:
   ```typescript
   // composables/useCouponForm.ts
   export const useCouponForm = (organizationId: Ref<string>) => {
     // Move form state, validation, and mutation logic here
     return { state, schema, createCoupon, isCreating, resetForm }
   }
   ```

2. **Create Coupon Constants**:
   ```typescript
   // constants/coupons.ts
   export const COUPON_TYPES = { /* ... */ }
   export const COUPON_TYPE_OPTIONS = [/* ... */]
   ```

---

## 2. Composables Architecture

### 🔴 HIGH: Query Key Management

**Current Issue**: Query keys scattered across components without centralization

**Recommended Refactoring**:
```typescript
// constants/queryKeys.ts
export const QueryKeys = {
  organizations: () => ['organizations'],
  organization: (id: string) => ['organization', id],
  incomePerDay: (orgId: string, start: Date, end: Date) => 
    ['income-per-day', orgId, start.toISOString(), end.toISOString()],
  orders: (filters: OrderFilters) => ['orders', filters],
  coupons: (orgId: string) => ['coupons', orgId],
} as const

// Update all useQuery calls to use centralized keys
```

**Benefits**:
- Consistent query invalidation
- Easier debugging
- Type-safe query key management
- Better cache management

### 🟡 MEDIUM: Data Fetching Patterns

**Current Issue**: Inconsistent data fetching patterns across components

**Recommended Refactoring**:
1. **Create Dedicated Query Composables**:
   ```typescript
   // queries/useOrders.ts
   export const useOrders = (filters: Ref<OrderFilters>) => {
     return useQuery({
       queryKey: computed(() => QueryKeys.orders(filters.value)),
       queryFn: () => fetchOrders(filters.value),
       staleTime: 5 * 60 * 1000, // 5 minutes
     })
   }
   ```

2. **Standardize Error Handling**:
   ```typescript
   // composables/useQueryWithErrorHandling.ts
   export const useQueryWithErrorHandling = <T>(options: UseQueryOptions<T>) => {
     const toast = useToast()
     return useQuery({
       ...options,
       onError: (error) => {
         toast.add({
           title: 'Error fetching data',
           description: error.message,
           color: 'red'
         })
       }
     })
   }
   ```

---

## 3. Server-Side Architecture

### 🔴 HIGH: API Route Organization

**Current Issues**:
- Minimal error handling in API routes
- No consistent response structure
- Missing input validation
- No centralized error handling

**File Examples**:
- `server/api/organizations/[organization_id]/members/[id].delete.ts`
- `server/api/promo-codes/index.post.ts`

**Recommended Refactoring**:
1. **Create API Response Utilities**:
   ```typescript
   // server/utils/responses.ts
   export const createSuccessResponse = <T>(data: T, message?: string) => ({
     success: true,
     data,
     message
   })

   export const createErrorResponse = (message: string, statusCode = 500) => {
     throw createError({ statusCode, statusMessage: message })
   }
   ```

2. **Input Validation Middleware**:
   ```typescript
   // server/utils/validation.ts
   export const validateInput = <T>(schema: z.ZodSchema<T>, input: unknown): T => {
     const result = schema.safeParse(input)
     if (!result.success) {
       throw createError({
         statusCode: 400,
         statusMessage: result.error.errors[0].message
       })
     }
     return result.data
   }
   ```

3. **Centralized Error Handling**:
   ```typescript
   // server/middleware/errorHandler.ts
   export default defineEventHandler(async (event) => {
     try {
       // Handle the request
     } catch (error) {
       return createErrorResponse(
         error instanceof Error ? error.message : 'Internal server error'
       )
     }
   })
   ```

### 🟡 MEDIUM: Service Layer Organization

**File**: `server/services/stripe.ts` (only 3 lines)

**Issue**: Minimal abstraction over external services

**Recommended Refactoring**:
```typescript
// server/services/stripe.ts
export class StripeService {
  private stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)
  
  async createCoupon(couponData: CouponCreationData) {
    try {
      return await this.stripe.coupons.create(couponData)
    } catch (error) {
      throw new Error(`Stripe coupon creation failed: ${error.message}`)
    }
  }
  
  async deleteCoupon(couponId: string) { /* ... */ }
  
  // More methods...
}

export const stripeService = new StripeService()
```

---

## 4. Type Safety Improvements

### 🟡 MEDIUM: Enhanced Type Definitions

**Current Issues**:
- Generic type usage in several places
- Missing domain-specific types
- Inconsistent error types

**Recommended Refactoring**:
1. **Create Domain Types**:
   ```typescript
   // types/orders.ts
   export interface OrderFilters {
     status?: OrderStatus[]
     dateRange?: { start: Date; end: Date }
     userId?: string
     stationId?: string
   }

   export interface OrderTableProps {
     filters: OrderFilters
     organizationId: string
     excludeColumns?: (keyof Order)[]
   }
   ```

2. **API Response Types**:
   ```typescript
   // types/api.ts
   export interface ApiResponse<T> {
     success: boolean
     data: T
     message?: string
   }

   export interface ApiError {
     success: false
     error: string
     statusCode: number
   }
   ```

---

## 5. Utils and Helpers

### 🟢 LOW: Formatter Functions Enhancement

**File**: `utils/formatters.ts`

**Current State**: Basic currency and phone formatting

**Recommended Additions**:
```typescript
// utils/formatters.ts
export const formatters = {
  currency: (amount: number, options: Intl.NumberFormatOptions = {}) => {
    return amount.toLocaleString('en-CA', {
      style: 'currency',
      currency: 'CAD',
      maximumFractionDigits: 2,
      ...options,
    })
  },
  
  duration: (seconds: number): string => {
    // Enhanced duration formatting with proper pluralization
  },
  
  fileSize: (bytes: number): string => {
    // File size formatting for potential file uploads
  },
  
  truncate: (text: string, maxLength: number): string => {
    // Text truncation with ellipsis
  }
}
```

---

## Implementation Priority

### Phase 1 (Immediate - 1-2 weeks)
1. 🔴 Extract OrderTable component logic
2. 🔴 Centralize query key management
3. 🔴 Implement API response standardization

### Phase 2 (Short-term - 2-4 weeks)
1. 🟡 Refactor chart components
2. 🟡 Create dedicated query composables
3. 🟡 Enhance service layer abstraction

### Phase 3 (Medium-term - 1-2 months)
1. 🟡 Form logic extraction
2. 🟡 Enhanced type definitions
3. 🟢 Utility function improvements

## Testing Strategy

For each refactored component/composable:
1. Unit tests for extracted business logic
2. Integration tests for data flow
3. Component tests for UI interactions
4. API tests for server-side changes

## Migration Notes

- Use incremental refactoring to avoid breaking changes
- Implement feature flags for gradual rollout
- Maintain backward compatibility during transition
- Document new patterns in CLAUDE.md after implementation