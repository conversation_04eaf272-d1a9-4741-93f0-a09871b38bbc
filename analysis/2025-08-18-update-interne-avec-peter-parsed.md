# Update interne - Hiko Technologies

Gabriel: En gros, j'ai commencé l'analyse du projet, je vous explique, j'ai reçu le back-end, le front-end. Dans le fond, on a un petit update interne, ça se peut que j'ai besoin de toi aussi par rapport à ça. En gros, le client, il va aller dans toutes sortes de directions. Je te fais un résumé extrêmement rapide. Imagine Bixi, que tu peux louer des véhicules, pas des véhicules, des vélos, mais à la place de des vélos, c'est des batteries.
Gabriel: Dans un festival, mettons, ou dans un hôpital, le but c'est de réduire l'anxiété des gens et le stress des gens en leur prêtant des batteries.
Peter: En chargeant de l'argent.
Gabriel: En chargeant de l'argent, c'est ça. L'échange, en gros, c'est tu payes pour réduire l'anxiété. Mais là, techniquement, mettons, ça peut être une compagnie ou l'hôpital, des choses comme ça, où à la limite, c'est eux autres qui fournissent le service, c'est eux autres qui payent pour les affaires, il faut que le monde ramène les affaires. Puis tu peux mettre ta carte, puis là, lui, il prend un dépôt. Puis il y a déjà plein d'affaires qui fonctionnent. Le système pour prendre un dépôt, ça fonctionne déjà.
Gabriel: La grosse affaire, en fait, puis là, c'est ça que je n'ai pas commencé à explorer encore. Je viens juste d'ouvrir le Code Bay, j'ai commencé à explorer, j'ai pitché des IA, j'ai dit c'est quoi les gros problèmes là-dedans, puis ça m'en a sorti. Mais il n'y a rien qui est important nécessairement à refactorer de ce que j'ai vu là. Il y a de l'organisation de code, des gros modules qui ont trop de responsabilités, qui se connectent à trop de services, des URL hardcoded, mais ça marche sur on app.
Gabriel: Je veux dire, le gars, il veut faire du cache. C'est sûr que c'est rien que ça. En gros, le principal point, en fait, il y a comme... Il va dans plein de directions, puis une des grandes questions qu'il m'a posé, en gros, il dit si on règle ça, moi, genre, je commence à faire de l'argent comme de l'eau parce qu'il, dans le fond, son hint, son... En gros, il dit si je suis capable d'offrir une espèce de plateforme pour, mettons, des festivals ou des...
Gabriel: N'importe quelle organisation, qui eux autres ont plein de commerçants qui passent par différents POS, qui est capable de capturer les données des transactions, genre démographiques, mettons, code postal, des affaires comme ça, puis qui redistribue ces statistiques-là à, mettons, au, je sais pas moi, le festival de la Tourée de la Marmotte, je sais pas trop quoi, ben t'sais, Il y aurait de l'argent à faire là.
Gabriel: Écoute, moi je veux dire, c'est pas à moi de juger ça, mais en gros, lui il veut savoir est-ce qu'on est capable d'extraire, mettons, le code postal de différents fournisseurs, puis d'avoir un point d'entrée pour fournir un dashboard de stats pour des festivals, des affaires de même. Mais tu sais, je sais pas à quel point c'est tiré par les cheveux, puis est-ce que cette information-là a réellement de la valeur, mais de ce que j'ai compris, lui, il aurait pas de misère à vendre ce genre d'affaires-là.
Peter: Comme je disais, il voulait attaquer le fait que les festivals aux 4-5 ans ont besoin de faire des sondages et des statistiques qui sont obligés par la loi à quelque chose d'un même pour obtenir leur subvention. Puis il engage du monde pour faire ça, puis ça va, ça coûte cher faire préparer ça. Puis il veut attaquer le côté statistique. Mais du côté biétrie, être attaché à Réservatech, on s'entend que c'est rien qu'un système, ça c'est correct.
Peter: Mais s'il veut rentrer et aller chercher des POS de bord, toutes sortes d'affaires, chaque festival va être une sale nouvelle aventure d'avoir à faire.
Gabriel: Ben c'est ça l'affaire, tu sais, pis là j'étais en train de rédiger un peu la question aussi, je voulais poser quasiment la question à tel âge de la TLM, s'il y en a qui ont de l'expérience là-dedans. En même temps, on n'a pas grand monde qui ont travaillé avec des POS, ça fait que je suis pas sûr, là, mais tu sais.
Gabriel: Pis là, j'ai sorti des exemples un peu de mon derrière, là, je lui ai pas posé la question, mais peut-être qu'il pourrait nous valider à quel point c'est ça, mais tu sais, un fou de truc qui utilise Monéris pour intégrer la plateforme puis partager les stats aux organismes, aux organisations comme le festival, mettons. J'ai comme l'impression que c'est un peu ça qui va, mais t'sais, c'est-tu réellement utile ou pas?
Gabriel: Pis là, si tu commences à combiner les stats, t'es pas capable de dire, ben cette personne-là qui a fait une transaction, c'est la même que dans telle autre, t'sais. C'est genre, j'ai pas trouvé.
Peter: Ça n'a pas de bon sens. Aïe aïe.
Gabriel: Aïe aïe. Fait que t'sais, on dirait que c'est pas évident.
Peter: Non, ça ne l'est pas du tout. Quand j'étais avec Bachelard, c'était surtout, tu es avec Réservatech, ce n'est pas pire. Ça fait un contact. Ils partaient aux endroits où ils pouvaient aller. Ils disaient, si on se met en équipe avec Réservatech, vous allez avoir tout le stade complet, même des gens à l'entrée, surtout les gens qui rentrent sur le site qui sortent.
Peter: Mais on s'entend que dans toutes les les food trucks et partout, c'est tout des indices, des entreprises indépendantes qui utilisent en plus des TPV, que non, tu touches pas à ça, tu sais, un TPV, on pourra pas acheter, il faudrait qu'ils nous sortent des rapports, là, tu sais, que le food truck dise, ben, vous en avez un rapport de vente, après.
Gabriel: Ouais, c'est ça, là, tu sais, je dis food truck, c'est-tu vraiment ça, c'est-tu vraiment plus billetterie, c'est, tu sais, on n'a pas parlé énormément, mais il dit, tu sais, si t'es capable de me donner une réponse, pis que la réponse, c'est oui, il dit après ça, on part, là, tu sais, pis il est capable de financer ce projet-là.
Gabriel: Moi, je suis comme, moi, je suis super ambigu, là, pis pour vrai, j'ai aucune idée, là, je veux dire, moi, Tu sais, je veux dire, j'ai déjà eu un Paywest à Vienne, téléphone, puis je gossais avec, mais tu sais, il n'y a pas plus qu'il faut.
Peter: Mais là, dans le fond, je ne savais pas si c'était limité seulement à la connexion biétricitate, mais tu as reçu le code base de ta patente. Est-ce que tu penses qu'on va aussi se faire charger de travailler sur le système de location de la batterie?
Gabriel: C'est ça. Ça c'est un des points critiques qui est potentiellement un revenu potentiel. Je pense qu'il m'avait parlé de 110 ou 115. Il avait mentionné des chiffres à ce niveau-là. Je l'ai dans mes notes là, mais quelque part. Mais en gros, impact potentiel à 115 000 funding opportunities. En gros, c'est ça. C'est vraiment le premier point, c'est comment on fait pour extraire les codes postaux des cartes de crédit du monde. Puis là, avec Stripe, Clover et Moneris, on pourrait commencer avec ça.
Gabriel: C'est comme là, peut-être que ça se fait, mais encore là, la partie complexe, c'est s'intégrer avec n'importe qui qui est invité sur le festival, selon moi que là, c'était de la marde. Puis comment tu coupes les données? Ça, c'est une chose. Après ça, les autres points que j'avais mentionné, que j'avais noté, dans le fond, la deuxième priorité, c'est lui, il veut déployer dans des hôpitaux bientôt. Entre autres, le 20, je pense qu'il est comme dans deux jours, il veut envoyer une demande pour une sub par rapport à ça.
Gabriel: Fait que là, il aimerait ça que je puisse lui fournir un résultat d'analyse concernant ces résultats-là. Fait que dans le fond, c'est... Lui, en ce moment, il veut pouvoir mettre à jour sa plateforme. Je n'ai pas regardé encore. Je n'ai pas eu le temps de regarder, mais c'est tel quel. Il faut que j'aille voir comment c'est fait. Puis, il y a un GitHub Actions. Puis, je vais aller voir comment c'est déployé. Mais tu sais, je vois qu'il y a des Cloudflare Worker. Puis après ça, il y a du super base.
Gabriel: Moi, pour partager tous les credentials, j'ai accès à tout, donc je vais pouvoir regarder ça. Puis en gros, tu sais, les hôpitaux, dans le fond, il y a comme des demandes d'un hôpital qui dit « Ah, rends, change le signe de pièce à quelque part. » Tu sais ça, je vais pouvoir m'en occuper, c'est tel quel, c'est vraiment un petit morceau. Mais tu sais, à la place de commencer à y faire une année, je vais juste y faire la job dans le code base pour regarder si je suis capable de le déployer.
Gabriel: Il n'y a plus de développement. Ben c'est ça, il n'y a plus de dev, il y a des amis techniques qui sont capables de faire une couple d'affaires, mais tu sais, c'est comme, il dit dans le fond, lui qui a développé la patente, il est parti, puis ça ne me semble pas être en mauvais terme de ce que j'ai compris quand je lui ai posé la question, mais tu sais, il dit dans le fond, il est juste passé à autre chose.
Gabriel: Après ça, il y a une autre affaire aussi, ils utilisent le service DisplayForce, je ne sais pas si tu connais, c'est genre un DisplayForce AI, D-I-S-P-L en fait, puis ça c'est une espèce de, je vais pouvoir aller voir un peu plus d'informations, mais tu sais en gros, eux autres, DisplayForce, c'est genre Il paye pour le service en ce moment, puis il y a différentes intégrations qui font la partie analytique. Parce qu'en gros, lui, la grande affaire qui se distingue avec ses bornes, avec des écrans, puis ses batteries, c'est qu'il fait rouler des publicités sur l'écran.
Gabriel: Puis après ça, il veut fournir, entre autres, un dashboard d'intégration pour le monde. Il veut que ce soit vraiment le moins d'interaction possible pour qu'ils puissent vendre le service avec la plateforme, que tout le monde se connecte, qu'ils peuvent gérer, mettons, peut-être le campagne par là, ils uploadent une vidéo, pis c'est tout, mettons, de ce que j'ai compris.
Peter: Ouais, c'était déjà comme la mouv', là, effectivement, fait que...
Gabriel: Fait que t'sais, à ce moment-là... C'est ça. À ce moment-là, lui... Puis il y a un autre défi qu'il y a aussi, c'est le fait qu'il n'y a pas de... Toutes, mettons, ses bornes, tous ses appareils, il n'y a pas de système de monitoring de sa flotte de machines, mettons. Je ne sais pas, il va falloir peut-être que je lui pose la question combien il y en a. Je sais qu'il y a différents modèles de trucs de chargement.
Gabriel: Mais en gros, un de ses principaux problèmes aussi, c'est le fait qu'il travaille avec un espèce de fournisseur chinois qui est celui qui fait entre autres les bornes. Puis ça arrive avec leur propre dashboard, puis les propres systèmes de stats, puis d'analytics. Puis il y a même genre des caméras reconnaissance. Ça floute le monde, mais il est quand même capable de sortir des stats par rapport à ça. Le gros problème que j'ai avec ça, c'est que c'est tout chinois et je n'ai pas le contrôle là-dessus.
Gabriel: Après ça, ce qu'il me disait, c'est que j'aimerais réexposer ces analytics au festival ou aux compagnies qui l'ont.
Peter: Il y avait comme une caméra infrarouge pour compter les personnes ou quelque chose d'un même, puis ça, ça serait la technologie chinoise, la cloud chinoise qui s'amuse à faire ça présentement, probablement.
Gabriel: Oui, c'est ça. Puis lui, il veut comme remplacer ça. Là aussi, j'ai noté ça. Dans le fond, tu sais, lui, il veut peut-être continuer de regarder, d'utiliser Display Force, qui est sa plateforme d'analytics, puis Visiteur Insight, Demographic Data, Facial Expression Analysis, puis tout ça. Mais faire en sorte que, quand ils pitchent le data à ça, que ça passe pas par le truc chinois, pis que ça intègre vraiment directement avec eux autres, avec ses propres affaires. Pis là, ben il fait affaire avec un espèce de truc de... Comment est-ce qu'on appelle ça, là?
Gabriel: Un accélérateur, comme un garage accélérateur, mais côté ardoir, là, mettons. Fait que t'sais, il travaille avec du monde pour le côté hardware. Fait qu'on pourrait peut-être à la limite mettre des recommandations. Mais en gros, il va avoir une machine Linux, pis il dit eux autres, toutes les affaires, ça va mieux sur Linux aussi. J'me dis, ben écoute, tu devrais pas te tromper avec ça. Après ça, donc, il y avait une autre affaire aussi, Battery Ownership Tracking System. Ouais, c'est ça, dans le fond.
Gabriel: Dans les enjeux qu'il y a aussi, mettons, t'sais, le monde qui part avec des batteries, ben vu que ça a pris un... un dépôt, bien là tout d'un coup ça garde le dépôt puis est considéré, mettons, acheté après 2-3 jours. Je n'ai pas regardé qu'est-ce qui fonctionne et qu'est-ce qui ne fonctionne pas dans sa plateforme par rapport à ça. J'ai ça de l'analyse. Après ça... Ça ne marche pas d'acheter après 2-3 jours.
Peter: Ce n'est pas fini.
Gabriel: Oui, j'ai comme l'impression que oui. Je ne sais pas s'il y a des problèmes là-dedans. Il faudrait que je regarde aussi ce qui est géré dans le code. Il a dit qu'il y avait une couple d'affaires, qu'il y avait peut-être potentiellement des mini-bugs par-ci, par-là, mais qu'en gros, la base de sa plateforme, ça fonctionne quand même. Je vais aller vérifier les edge cases, mais il n'y a pas de testing dans la plateforme déjà. Mettons qu'il y a des trucs de logique, il n'y a rien qui est validé ou qui est vérifié.
Gabriel: Ça fonctionne parce que ça a été codé, mais il y a-tu des bugs, on ne sait pas. Après ça... Bref, il faudrait améliorer le ownership tracking des batteries, des choses comme ça potentiellement.
Gabriel: Parce qu'une chose qu'en ce moment ils supportent pas et qu'ils disent ça serait peut-être nice, c'est le fait que une fois que la batterie est rendue à quelqu'un, il peut quand même la faire recharger par la machine en tant que telle, mais je sais pas si c'est tu repayes encore ou je sais pas trop pour le truc de chargement, mais en même temps la batterie tu peux la brancher n'importe où. Mais là, j'ai demandé, j'ai dit, t'sais, y'a-tu vraiment un avantage de genre prendre cette batterie-là pis la remettre dans le bidule pour la faire charger?
Gabriel: Il dit oui, ça charge plus vite parce qu'ils ont comme des... Ouais, pis dans le fond, il dit ça charge plus vite avec les brins pis tout, là, fait qu'en tout cas... Fait que ça c'est une chose. Sinon, il a déjà vécu le fait que genre des flots ont saccagé sa machine pis qu'ils ont zigonné après pis qu'ils ont fait rouler des... Ils ont ouvert Google sur la borne pis ils disaient en fait de la même. J'suis comme ben c'est...
Gabriel: T'as une première expérience de le fait que t'as du monde qui vont gosser avec pis après ça va arriver, tu sais. J'veux dire, tu laisses une machine inattendue, c'est sûr que le monde va gosser après. Après ça, il y a des enjeux aussi de température sur son POS. C'était un gros meeting, c'est-à-dire qu'il y avait du stock. En gros, dans le fond, il dit qu'en ce moment, il y a un POS chinois dessus.
Gabriel: En gros, il y a une intégration tape-toupée, je pense, mais dans le fond, en ce moment, les températures supportées, quand il fait trop chaud, ça se met à chier, de ce que j'ai compris. Genre, il voudrait que ça marche à plus haute température, je sais pas trop. Fait qu'en gros, il veut pouvoir changer le POS, parce qu'en ce moment, le POS qu'il y a, c'est de la merde, de ce que j'ai compris. Mais ça, il faudrait peut-être creuser pour avoir plus de détails.
Gabriel: Ensuite, il y a la plateforme self-service, comme je disais, uploader des vidéos de pub. Après ça, location spécifique.
Peter: Ça serait par Display Force cette plateforme.
Gabriel: Ouais, en gros, moi, là, ça, faut que j'aille voir aussi, là, t'sais, en gros, c'est qu'est-ce que Display Force expose. Il m'a donné les accès, pis dans le fond, là, la paye, elle est pas publique. Fait que je vais aller voir à quoi qu'on a accès pis qu'est-ce qui est possible de faire. Pis t'sais, mais je veux dire, clairement, là, on est rendu à genre 8 projets, là.
Peter: Ouais, ouais, absolument, ouais, tout à fait, ben ouais. Tu veux qu'on refasse ta machine?
Gabriel: Ouais, c'est ça. En gros, je pense que ce qu'il veut savoir, c'est où est-ce qu'on peut l'aider en priorité. Là, je suis rendu dans les priorités qui sont moins intenses. Mettons, API, Unification Platform, dans le fond, plein de third party, puis avoir un endroit centralisé pour, entre autres, gérer ces flottes de devices, comme j'ai dit tantôt. Après ça, son infrance en ce moment, c'est à quel point ça peut scale, puis sa connexion avec les trucs chinois. En gros, de ce que j'ai compris, il veut se débarrasser de tout ce qui est des API chinois.
Gabriel: Fait que là, il faut que j'explore qu'est-ce qu'il l'est et qu'est-ce qu'il ne l'est pas aussi. Tu sais, ce qu'il faut comprendre, c'est que le gars, il est non tech. Il est là-dedans, puis il magne là-dedans, ça fait un bout. Mais tu sais, je veux dire, il n'a pas ouvert Cursor dans son codebase, puis il n'a pas posé des questions à son codebase. Il n'a pas utilisé les IA là-dedans, ou il n'a pas VibeCoded. Il n'est pas là-dedans. En gros, ils voudraient nous faire confiance pour développer des affaires pour amener ça plus loin.
Gabriel: En ce moment, c'est du Svelte, TypeScript, Tailwind, CSS, Superbase, ZRest, Route53, Cloudflare, j'utilise Stripe. Le reste, c'est des services externes. J'utilise Twilio pour des SMS, des trucs comme ça. Puis il voudrait regarder pour s'intégrer avec d'autres affaires que Stripe parce que je pense qu'en ce moment qu'il dit que ça coûte cher, ben t'as l'avantage que toi avec Stripe, c'est qu'il t'expose des API puis tu peux faire ce que tu veux. Fait que c'est à voir. Puis après ça... Ouais, c'est ça. Dans le fond, il veut avoir son business model, il est multiple là.
Gabriel: Fait que c'est genre... L'occasion de, donc l'occasion de batterie, publicité, puis vente d'analytics en gros, c'est son modèle, ça se situe pas mal là-dessus. Ça ressemble à ça. Le gros point qu'il m'a dit, le truc sur lequel il voulait que je regarde en priorité, c'est vraiment le côté intégration POS, extraire les codes postaux. Je ne sais pas si tu as de l'expérience à ce niveau-là parce que je peux aller fouiller, voir la doc des différents fournisseurs, qu'est-ce qui est possible de faire et fouiller dans cette direction-là.
Gabriel: puis essayer d'élaborer une stratégie pour s'intégrer avec différents fournisseurs, puis réussir à fournir des stats pour les festivals. Mais tu sais, comme je dis, il aimerait peut-être s'attaquer à cette affaire-là, puis il sait que ça paye du monde, mais j'ai comme l'impression que la stratégie devrait plutôt être qu'est-ce que le monde fond, puis comment qu'on peut remplacer une partie de ce job-là pour... Mais tu sais, ça reste quand même l'idée de remplacer la job de quelqu'un par une plateforme qui coûte moins cher puis qui donne les mêmes résultats. C'est un peu... C'est whack, là, mais...
Gabriel: Moi, j'ai...
Peter: Ce que je crois comprendre, c'est qu'ils pensent obtenir les codes postaux à partir des cartes de crédit.
Gabriel: Oui, c'est ça.
Peter: Je ne vois pas ça possible. Je vois ça possible à partir d'un système, comme on dit à la billetterie, si tu t'intègres avec nous, le code postal, on le ramasse lors de la vente, si on est capable de le donner. Mais de dire que tu questionnes un fournisseur comme MoneyRice pour dire donne-moi les codes postaux, ça c'est une carte. Pour moi, t'es dans le... On t'a envoyé promener avec un méga giga coup de pied. Ça n'a aucun bon sens parce que le code postal sert à la sécurité des cartes de crédit. La vérification des villes.
Peter: Ouais, si la banque va donner le code postal, tu n'as qu'une carte de crédit.
Gabriel: Ouais c'est ça, lui dans le fond j'ai l'impression que c'est une hypothèse, c'est quelque chose qu'il voulait qu'on creuse, mais c'est ça, j'ai pas l'impression qu'on peut récupérer cette info-là pendant qu'il y a la transaction, parce que le moment où tu pourrais l'avoir c'est une fois que quelqu'un a payé, puis là c'est la page de callback, puis ça doit même pas renvoyer cette information-là dans le callback, ça doit rien te dire si ça a été réussi ou pas.
Peter: Quelqu'un utilise Stripe et dit dans son processus de paiement, je veux que tu ramasses les codes postaux des gens pour mes stats, je suis sûr que c'est correct. Mais de dire que tu ne les ramasses pas et que tu essaies de les obtenir sans les avoir demandé au client.
Gabriel: Ouais c'est ça, ça je pense pas que c'est possible. Fait que tu sais tu vois cette partie là, moi j'ai comme l'impression que je vais déjà lui dire genre écoute ton point 1, on a des gros doutes sur la faisabilité pis je veux dire mon objectif c'est pas de le bullshitter ou de lui faire brûler de l'argent dans un projet impossible non plus.
Peter: C'est vraiment à moi d'être capable d'intégrer à l'échelle des fournisseurs populaires. POS, il y avait dans l'open source, il y a quelqu'un à Montréal qui en vend à tout le monde, Odu, c'est le système de POS open source. Maintenant, on se dit, l'INTEG, je choisis les deux ou trois plus populaires en premier. Quand il y a un festival, je viens de demander à l'organisation, peux-tu me donner un token dans ton système, s'il te plaît, que je le mette dans le mien. Peut-être des trucs de la même. Ouais, c'est ça.
Gabriel: Y'a-tu une stratégie de même qu'on pourrait... C'était tiré, pareil. Moi, j'ai montré que le défi, c'est... Mettons que tu... Comme tu dis, avec la billetterie, oui, fine, ça peut le faire. Pis là, t'as comme un point d'entrée pour tout le monde qui passe par là. Mais si tu commences à regarder avec les différents commerçants sur le site, dans le festival, pis de leur dire, OK, tout le monde a écrit... intégrer-vous, fournissez-nous des clés d'API de vos comptes Stripe, etc.
Gabriel: Ça n'arrivera pas, juste dans la faisabilité de ce chemin-là, j'ai comme l'impression que ça se plante vite de bonheur, parce que c'est genre... Moi, le gars du food truck, il va dire juste non.
Peter: Ça va pas avoir les churros, spécifique-moi ça. Le gars, il va même dire qu'il n'est pas sèche.
Gabriel: Quoi?
Peter: Qu'est-ce que c'est? Qu'est-ce que c'est?
Gabriel: Qu'est-ce que c'est? Qu'est-ce que c'est? Qu'est-ce que c'est? Qu'est-ce que c'est? Qu'est-ce que c'est?
Laurent: Qu'est-ce que c'est? Qu'est-ce que c'est?
Peter: Qu'est-ce que c'est? Qu'est-ce c'est?
Laurent: Qu'est-ce que c'est? Qu'est-ce que c'est?
Peter: Qu'est-ce que c'est? Qu'est-ce que c'est?
Laurent: Qu'est-ce Ils ont que c'est?
Peter: Plus d'infos. La bièterie a beaucoup d'infos sur au moins la quantité de monde qu'il y a, les personnes. On connaît tout le monde qui Qu est rentré. C'est au moins ça. On sait s'ils sont rentrés aussi avec la scanne. On est capable de dire s'ils sont vraiment passés dans la porte.
Gabriel: Est-ce que dans le fond, c'est parce que, autrement dit, ce qu'il viendrait faire, ce serait un espèce d'agrégat, un système d'agrégateur de statistiques, de démographie pour le mouvement de foule, puis pour les billets on top de différentes billetteries. Autrement dit, comment supporter différentes billetteries? Puis à quel point ça se fait? Ça dépend de qu'est-ce que les billetteries peuvent exposer. On dirait que je ne le sais pas.
Gabriel: Sinon, est-ce que les billetteries ont des API quand c'est un événement et qu'ils exposent assez d'informations et qu'ils seraient capables de prendre ça, remettre ça dans un dashboard et que ça fournit des stats intéressantes ou des rapports qu'il y a besoin ou remplir ce fameux rapport-là automatiquement? Donc moi j'ai comme l'impression que pour cette partie-là, il faudrait que je reparle avec pour ajuster les attentes. Parce que à ce point-là, moi je pense pas qu'il peut aller nulle part avec ça.
Gabriel: Moi, rien qu'avec ce que tu viens de me dire aussi, je veux dire que c'est suffisant pour que je puisse continuer la conversation avec. Puis pour le reste, je vais dire c'est du développement classique, c'est comment se débarrasser de ses différents API, puis je suis en train d'explorer le code base, puis après ça, implémenter les petites modifications pour son déploiement d'hôpital. Puis après ça, on lui dit, voici... Parce que dans le fond, lui, il serait comme prêt à décoller.
Gabriel: Une fois qu'on lui dit combien ça peut coûter, quel genre de projet qu'on peut y faire, tout ça, puis qu'est-ce qu'on peut y faire. On peut lui dire, genre, OK, on va mettre une ou deux personnes sur ton projet pendant tant de temps, je sais pas moi, temps partiel, peu importe, puis on décolle du développement des fixes dans ces affaires. C'est parce qu'il y a personne en ce moment à faire avancer ces trucs, puis il dit, j'ai besoin de moi.
Peter: Ça, ça l'a fait. Penses-tu que... sa base marche déjà un peu, mais... Oui, mais là, je ne.
Gabriel: L'Ai pas fait tourner à la canne encore.
Peter: Oui, mais par rapport à son projet d'hôpital, penses-tu que ces gens d'Afrique, ils pensent qu'il est correct, mais au moment où on le met, en réalité, il manque un an de travail parce que c'est vraiment pas correct par rapport aux clients qu'il va chercher. Oui, il l'a déjà fait virer quand même, puis il est comme pas pire.
Gabriel: J'ai comme l'impression que vous l'avez fait virer déjà un peu, mais dans les affaires qui manquent aussi, il y a des petits bugs à des places. Je pense qu'il y a quelque part dans le code, je vais peut-être le trouver dans mon analyse, il y a une transaction qui est jamais à deux piastres. Il y a un choix entre soit gratuit, soit deux piastres. Quand il met n'importe quoi d'autre, ça affiche à l'écran un chiffre, mais ça devient deux piastres. Pour le renting des batteries.
Gabriel: C'est juste pour lui, mais c'est quand même chiant parce qu'il ne peut pas ajuster ses prix. Ça se corrige, c'est une affaire. Mais c'est toutes des petites affaires comme ça, j'ai l'impression. Je ne sais pas quel point il y a vraiment de quoi plus à faire.
Peter: On dirait que pour tout ce qui est de la statistique, la solution la plus intéressante serait dans l'analyse de faux. De continuer à aller vers les caméras. C'est comme être capable de déplacer quasiment d'un air qui pointe des photos, peu importe à quel endroit. Là, il y a du AI dedans qui compte la circulation des jeux, quelque chose de même. C'est ce que tu veux faire avec ta borne, faire des caméras. Peut-être qu'il pourrait y avoir juste plus de modules de caméras qui vont se créer face à son système.
Peter: Ça serait peut-être mieux que de se laisser se connecter au monnaie risque du monde.
Gabriel: Ben oui, c'est ça. Pis t'sais, après ça, est-ce que, mettons, est-ce qu'il pourrait y avoir un programme sur sa borne? Pis là, je sais pas si ces écrans sont tactiles ou non, là, avec des claviers. Mais t'sais, rendu là, est-ce que le monde peut faire une espèce de self-check-in, rentrer le code postal, pis ils ont pas besoin de répondre à quelqu'un qui est en avant pis qui est payé pour faire ça, t'sais.
Peter: Ouais, répondre au sondage pis courir la chance d'être content.
Gabriel: Ouais. T'sais, y'a-tu de quoi dans ce genre-là, rendu là? Je pourrais reparler avec pour voir genre vers quoi que ça peut se diriger, t'sais. Quelque chose, ça répond soit aux mêmes besoins, mais t'sais. Mais j'ai comme l'impression qu'il veut quand même quelque chose qui scale vraiment beaucoup. En tout cas, je ne sais pas à quel point lui, son affaire, c'est viable ou pas. Il faudrait vérifier ça parce qu'il n'y avait pas l'air... Il est early dans le process, mettons. En ce moment, il pose des questions sur la faisabilité.
Gabriel: Puis après ça, moi, je pense que c'est pas encore assez pour dire OK, oui, on aura de l'avant sur un projet de la sorte, mais il y a d'autres affaires à côté que ça, on peut y adresser. Il y a déjà une application, il y a déjà des trucs qu'on peut y cotiser. Mais il faut que le temps qu'on y passe, ça augmente sa valeur, somehow, que ça aille de la valeur pour lui. C'est pas mal ça. Ben quoi?
Peter: Écoute. Ça faisait les codes postaux d'un écran. À la limite, mettre un QR après les gens, ça faisait ça sur leur cellulaire, c'est encore mieux. Le labo en fait. Ça vient de bypasser le produit bien vite.
Gabriel: Ouais. Oui, c'est ça. Autrement dit, si son objectif, c'est ce qui paie le sondage, ou simplifier le process du sondage, peut-être qu'il peut faire des trucs justement pour le faire. Est-ce que c'est un QR? l'événement, des trucs comme ça. Mais encore là, tu sais, c'est tout le temps, c'est tout le temps un peu tricky. Le monde peut remplir n'importe quoi. Puis tout comme quelqu'un peut dire n'importe quoi à quelqu'un, quelqu'un qui est sur perso aussi. Peut-être que tu as un pourcentage que c'est pas clair. Peu importe. Ouais, ça ressemble à ça.
Gabriel: Sinon, pour l'instant, j'ai pas d'autres affaires. Moi, je vais juste continuer de mon côté l'analyse technique. Puis je regarde qu'est-ce qui... Je vais essayer de faire tourner le système en local, mais je viens juste de commencer dans le fond.
Peter: Je vais vous lancer des petites infos.
Gabriel: Yes, puis en fait je pourrais te partager même à l'écrit aussi, j'ai du stocker un peu, un résumé des notes et des projets que j'ai eu aussi, de ce que j'ai fait jusqu'à l'instant.
Peter: Oui c'est très bien, je vais te placer ça dans le dossier, j'ai tout pris en note de ce que tu as dit, mais moins vite un peu que ce que tu disais.
Gabriel: Oui et sinon tu as le transcript dans Circleback, tu peux copier le transcript de ce qu'on a dit dans Circleback.
Peter: Et si, ouais, c'est correct, non, je me demandais si on avait eu un pour avec les clients, c'est correct, c'est bon.
Gabriel: Ah ouais, bien, je peux te partager le résumé de ce que j'ai eu avec l'autre client aussi, je vais te partager le courrier. C'est pertinent, tu vas voir, mais tu sais, social, c'est tout ce que je t'ai dit, il y avait bien, bien d'autres affaires. Il m'a partagé aussi toutes sortes de documents que je n'ai pas regardé encore, mais dans le fond, il m'a partagé des, bien, ça, je pourrais te partager mon écran, en fait.
Peter: On a, on a un panel Slack.
Laurent: Là, fait qu'on peut, on pourrait te mettre l'info, là.
Peter: Exact.
Gabriel: Il m'a partagé minidevice, operational control et interface document. Il y a comme des documents d'API comme ça que je vais pouvoir explorer.
Laurent: Tu les mettra dans le channel.
Gabriel: Ouais, il m'a tout garroché ça en des courriels séparés. Mais tu sais, ça dans le fond, on dirait que c'est l'API que ces bornes utilisent. Sync, batterie, slot, etc. Il faudrait que je regarde qui fournit ces PDF-là ou ces customs. Mini device operational control API interface document. C'est pas trop grand, c'est quoi?
Peter: Ouais, c'est peut-être le device de locking de batterie qui est fourni. C'est peut-être la doc de l'appareil qui sert à locker des batteries. C'est pas lui qui a fabriqué dans le fond. C'est fabriqué dans le fond ou c'est juste un rassemblement d'hardware qu'il a acheté un peu partout?
Gabriel: C'est principalement un rassemblement de hardware. C'est ça. Il y avait Laurent qui m'avait partagé des URL justement en lien avec ça. Mettons ça ici. En gros, c'est bon, c'est exactement ça. C'est juste que je pense qu'il n'y a pas le logo, peut-être.
Peter: Je ne sais pas trop.
Laurent: D'après moi, c'est le genre de fournisseur et tu le personnalises d'un look. C'est le fabricant chez moi.
Gabriel: C'est ça. Tu vois les autres qui ont plein d'intégrations de POS et tout. C'est garantissant l'absence de perte de fonds, n'importe quoi. Mais en gros, ça, c'est le truc que lui, il cherche à remplacer après. Là, c'est tricky un peu parce que c'est comme, OK, il achèterait ça. Après ça, il ferait un spin-off de cet ardoir là en mettant son propre... Ouais, tu veux dire que tu devrais être chinois là?
Peter: Ouais, c'est ça, exactement.
Gabriel: C'est peut-être.
Peter: Que ce devise-là est vendu, mais qu'ils disent dans une borne de même, à l'intérieur, le matériel, ça reste du matériel, ça reste du devise, qui s'achèterait indépendant. Puis c'est peut-être correct pour eux autres de dire tu peux installer ton propre système puis coder avec chacun des devises, puis voici les logs de chaque grenade.
Gabriel: Ils sont peut-être bien ouverts à ça.
Peter: Ça sert à ça.
Gabriel: Ça, tu vois, ça s'embête les devises avec l'écran. Qu'est-ce qui est supporté? Je sais pas si c'est la borne en tant que tel, mais tu vois, il y a comme toute l'espèce de grand guide de comment ça marche. C'est l'écran.
Peter: On va pouvoir jeter les bits parfaitement. J'aime ça, j'aime ça les bits.
Gabriel: Un CD, mais oui, c'est ça, c'est comme, d'ombelle étalée et de viarge.
Laurent: Pour tenir cette phrase-là, c'est ça?
Peter: Oh, ouais, ça ressemble à ça, que le bug va s'en écriper.
Laurent: C'est bon.
Gabriel: Packing order.
Peter: Ça, on nous mentionne que... Ça, c'est.
Gabriel: Comment tu les as... Ça, c'est comment tu leur... le package pour le shippage, je sais pas trop. Les panels... Ah, c'est un paquet d'écrans.
Peter: Ok, il y en a une douzaine, une dizaine dans le même bois, ok, c'est nice. Mais oui, c'est définitivement l'écran, c'est pas mal.
Gabriel: Ça ressemble à ça. Après ça, il n'y en avait plus qu'une. Quoi d'autre aussi? Caméra. Ça, c'est l'aspect de sa caméra qui est utilisé. Fait que là, je vais essayer d'extraire du data de ça. Je ne sais pas à quel point c'est pertinent.
Peter: C'est écrit « On-Coin » ou quoi? Non? C'est quoi ça?
Gabriel: C'est ça. Module mécanical dimension. C'est vrai, sérieux, je regarde ça, je fais comme « OK, man.
Peter: » Ça manque d'arrêt.
Gabriel: Ça manque d'arrêt. C'est écrit dans un EI, au pire. Je dis, il y a quoi? C'est quoi qui est intéressant là-dedans? C'est ça, c'est son POS dans le fond. Fait que là tu vois, il y a quand même des calls, client secret, payment ID, puis tu vois les transactions. Puis c'est quand même boboche, tu sais, batterie ID, return ID. Tu sais, le data que ça envoie, puis c'est qu'est-ce que ça pitch à Stripe. Fait que c'est vraiment comme intégré pour.
Gabriel: Là tu vois que ça call le SDK, collectPayment, puis après ça, c'est pas mal ça, c'est collectPayment.
Peter: Ouais, c'est comme une doc de Stripe, mais qui parle quand même de batterie. Ça veut dire que c'est vraiment une doc qui est faite spécialement pour... Ouais.
Gabriel: Intégrer Stripe à ça. Après ça, Screen BOS. Ça, c'est une autre affaire d'écran. J'imagine que c'est peut-être le plus gros écran, ça. Ça a l'air d'être la même affaire que tantôt.
Peter: Ouais, parce qu'il y en avait un sous forme d'AutoStar qu'on voyait tantôt.
Gabriel: Ouais, exact. Après ça, j'ai la doc de Display. Ah non, ça, il m'a partagé les insights en RAW. C'est un Excel en gros.
Peter: Un exemple de rapport?
Gabriel: Non, dans le fond, en gros, ce qu'il veut faire, c'est que, tu vois, j'ai un exemple ici, mais ce qu'il veut faire, c'est si jamais, mettons, DISPL, je peux te montrer, c'est quand même nice, leur plateforme DISPL, displayforce.ai. C'est genre... Login... Regarde, on le voit sur la page d'accueil, c'est que ça rend flou les faces pour pas identifier, mais c'est quand même capable de sortir le gender, ça te sort vraiment des stats, c'est quasiment creepy, je regarde ça et je suis comme... Welcome à toute la société de surveillance.
Gabriel: Puis après ça, ça permet de sortir des stats démographiques.
Gabriel: Puis lui, vu qu'il a une caméra sur son bidule, dans les écrans qu'il peut mettre un peu partout, il l'a connecté à ça, puis après ça, il peut extraire du data avec ça, puis fournir des stats à ceux-là qui ont ça dans le magasin ou dans le peu importe, tu sais, ça fait que c'est comme, c'est intéressant quand
Peter: même. C'est ça que je parlais tantôt, c'est bon, c'est juste ça que je me disais, finalement, on voit dans les démos en haut qu'il est vraiment pas loin de ce que je parlais, là, je me disais au lieu d'aller voir les POS des machines, des food trucks, c'est peut-être mieux de mettre plus de caméras connectées là-dessus, tu fais des devices packagés qui s'accrochent facilement des places, puis... Ben.
Gabriel: Oui, c'est ça, exact, puis après ça, ben c'est...
Peter: Puis il y a plein de monde qui ont l'air d'acheter, là, c'est bon.
Gabriel: Exact. Puis après ça, lui, il cumule les stats dans cette plateforme-là. Fait que là, tu vois, il peut faire des campagnes. Là, il y a une campagne. Mettons qu'on va voir la campagne qu'il y a là. Ça, c'est la campagne qu'il y a sur la vidéo, je pense, mettons, sur son écran. Fait que là, je pense que si je peux explorer, il y a une place que j'étais capable d'aller voir la caméra quelque part. C'est pas la caméra, là. Chaud. Regarde, tu vois, ça, c'est la petite vidéo qu'il joue sur son écran. Ah, OK.
Peter: C'est la configuration de la campagne en question qui va sur l'écran.
Gabriel: Exact. Il peut configurer ses affaires-là. Lui, ce qu'il aimerait, c'est pouvoir réexposer ce que Display Force AI fait, mais de son côté.
Peter: Limité pour le client.
Gabriel: Limité pour le client, c'est ça. Ça, c'est une chose. Fait que là tu vois il y a les campagnes, puis après ça tu as les insights and data, puis la partie du data, genre, mettons là toutes ces affaires sont offline, mais Devices, Statistiques, Visitors Insights, tu vois là il y a comme plein, il y a un gros dashboard avec genre, tu peux passer des dates ou des ranges, puis tu sais c'est comme... C'est vraiment capoté, mais tu sais, c'est comment réexposer ça de manière intelligente et utile pour ses clients, dans le fond.
Gabriel: Parce que là, tout le data, il est dans Display Force, mais comment on le pointe et qu'on le ré-expose. Donc là, tu sais, il dit, ah, mais tout le data, je le lis dans Excel. Donc là, il m'a partagé un Excel qu'on va exporter, mais je suis comme, il y a-tu vraiment tout? Je regarde ça ici, il y a quasiment rien dans l'Excel, mais bon, c'est peut-être mon jeu d'Excel qui est pas mal. C'est ça, parce que ça c'est juste l'éditeur.
Peter: Il a pas payé, il est limite à 10 lignes.
Gabriel: C'est ça, il va falloir que je regarde c'est quoi qu'on peut faire. Ça c'est une chose, je peux m'occuper de cette partie-là. Ça c'est tel quel, parce que tu sais, je regarde là-dedans pis je veux dire, il n'y a pas de settings. À moins qu'il y ait de quoi te cacher là-dedans, mais il n'y a pas grand-chose. Il n'y a pas l'air d'avoir d'API nécessairement jusqu'à présent. De même, ça va pas sauter de l'en face. Fait que j'ai bien des doutes. À moins que ce soit ici. C'est peut-être qu'il a fait un doc.
Peter: Genre il donne une URL et tout, puis il a dit c'est pour obtenir ton token, procède de telle façon, ou écris ton mot de passe, je sais pas trop. Ouais, même encore là. Quand je serais pas par clé, qu'est-ce qu'on figure?
Gabriel: C'est ça, il a pas l'air à avoir grand chose. Fait que t'sais, à quel point je suis capable d'extraire du data, de t'sais, en tout cas, ça c'est une affaire. Je vais continuer de fouiller cet après-midi, mais je suis pas sûr que je vais... Autrement dit, je suis pas sûr que je vais avoir ben des bonnes nouvelles pour lui.
Peter: On va envoyer un AI à regarder l'écran qu'il y a là, puis à extraire la donnée en texte.
Gabriel: Ouais, c'est ça, c'est ça, c'est pas vraiment top. Mais pour vrai, ça l'arrête. Le data, il pourrait scroller, là, je veux dire, je pourrais inspecter les requêtes, qu'est-ce que ça fait, puis refaire les requêtes pour aller extraire la data, puis qu'on le réexpose à notre bar, mais t'sais, on est à la merci des gens qui changent d'âme, c'est ça. C'est pas top en STI non plus. Pour vrai, il n'y a pas grand chose de ça qui est tant utile, qui est tant facile. Bref, il y a trois projets.
Gabriel: Dans les trois projets, il y en a un qui est facile, c'est faire les petits fixes visuelles dans son app à lui. Puis il y a des trucs qui est afficher sur ses écrans, ces affaires-là. Parce que le reste, je veux dire, c'est comme plus gros, plus complexe, avec full d'incertitudes.
Peter: Ouais, ouais.
Gabriel: Pas mal ça.
Peter: À offrir un service aussi fort qu'on voyait sur la vidéo, il doit bien avoir la façon de... Il a dit qu'il voulait sortir d'Excel. Il doit bien avoir une façon d'exporter ces données stat-là qui fait que tu n'as pas à utiliser la plateforme pour pouvoir l'intégrer à tes systèmes. Ça pourrait être de dire, je vais avoir certaines données directement dans mon système. Ce n'est pas que je ne veux pas discipler force, accéder à leur page, mais je veux que ce soit intégré.
Gabriel: Oui, c'est ça.
Peter: J'imagine que c'est ça.
Gabriel: Ils ont parlé d'un API à quelque part, Externet Public API. Normalement, ils ont de quoi, mais j'ai comme l'impression qu'il faut les contacter pour y avoir accès en premier lieu. Ça se pourrait que je vais juste dire genre, bon ben yo, Occupe-toi de contacter avec eux pour t'offrir exposer l'API. Ouais, c'est vrai que c'est ça.
Peter: Ils enverraient moi la doc par la soirée. Ils sont comme trop jeunes peut-être ou quoi. Ils n'ont pas fait de course sur l'API.
Gabriel: C'est ça, vite de même sur la plateforme, une fois connecté, j'ai rien. Mais ils ont clairement, ils parlent de l'API sur le site. Fait que j'imagine qu'il y a de quoi, je ne sais juste pas comment y accéder. En tout cas, c'est ça. C'est là que j'en suis rendu. À moins qu'il y ait de quoi dans users, je ne pense pas. Pas grand chose ici. Extensions peut-être. Subscription, visiteurs, designers, display ads.
Peter: Programmatique. Private.
Gabriel: Programmatique and trading desk.
Peter: User.
Gabriel: Programmatique. D. O. O. H. solution on every broadcasting point. Je ne pense pas que ce soit ça.
Peter: Red Wars. OK.
Gabriel: Subscription. J'ai pas trouvé en tout cas. Il va falloir leur parler, puis dire.
Peter: Voici mon compte, puis ça me donne une clé.
Gabriel: Tu vois, DOH and Retail Media with Ad Sales Automation. White Label Self Service Portal SSP DSP plus API. C'est peut-être ça, là. Puis ça, dans le fond, c'est... Quantity Exists... Il le dit pas ici, mais je pense que lui a parlé avec, puis ça coûterait peut-être 25 000 par... C'est-tu par mois ou par année? Je sais plus. Genre c'est quand même cher. Je ne sais pas si... En fait, je ne sais pas c'est combien. Il faudrait que j'en retourne voir dans mes notes.
Gabriel: Je ne vais pas payer 25 000$ pour que je mette mon nom dessus. Ouais, c'est ça, c'est le truc de label. J'ai le transcript, il avait dit ça. Il dit qu'il ne voulait pas payer 25 000$. J'ai comme l'impression que ça coûte... Pour ça, je ne sais pas si c'est par année ou par mois, mais bref, c'est 25 000$ potentiellement pour être White Label et on top de ça. Autrement dit, j'ai comme l'impression qu'ils voudraient le contourner, mais on s'entend. Display Force, ils savent.
Peter: J'ai l'impression que ça vient de moi. Ils ont marqué plus API en bas. C'est comme genre, non, ben si tu veux l'API, c'est parce que tu veux être White Label dans ton logicier à la port. Peu importe, après tu veux White Labeler.
Gabriel: C'est ça. C'est ça que j'ai l'impression aussi. Je vais être obligé de dire ça sûrement, parce que je regarde, sinon ailleurs, ça n'en parle pas d'API. Donc, c'est pas mal ça. Ouais, je sais pas si tu as d'autres idées ou des choses vite comme ça, mais moi je vais juste continuer dans l'analyse de ce patent-là pour voir qu'est-ce qu'on peut y proposer. À date, son principal projet, j'ai des gros doutes. Je vais pouvoir y fixer les petites affaires qu'il m'a demandé.
Gabriel: Dans le fond, je ne l'ai pas dans mes notes-là, je ne l'ai pas téléchargé, mais en tout cas, il m'a partagé un pdf avec genre les demandes de l'hôpital mais c'est genre tout changé du texte dans son app puis je vais essayer de, je vais voir si je suis capable de l'en déployer ou non. Fait que je vais l'essayer sur, il y a comme des environnements de dev en plus avant.
Gabriel: Fait que je vais l'essayer mais c'est comme Twiki un peu, d'essayer un déploiement sur un stack qu'on connaît pas en même temps tout le temps ça, tout le temps Twiki. Fait que si jamais j'ai un gros doute, je calerai du monde pour vérifier avant d'exécuter les commandes. Fait pas mal ça. Sinon sous son Github, il y avait quoi de drôle? Sous mon Github, c'est un coup de jeu qui court. On a ton app.
Peter: Oups.
Gabriel: Je vais l'avoir là. C'est ça, il dit t'sais, je suis allé changer de quoi pis ça a pété. Mais là c'est ça, il déploie avec Cloudflare Pages. Pis là c'est dessus, il y a un build failed. Faut que j'aille analyser, faut que j'aille checker ça. C'est pas trop stressant, mais normalement il va partager les accès. Faut que j'aille pas aller voir.
Peter: Ok.
Gabriel: Ouais.
Peter: Réparons. Rythme du monteur, ouais. Les affaires.
Gabriel: Les affaires, ouais. Y'a même trop de comptes Cloudflare.
Peter: Ça.
Gabriel: Là, c'est l'arpège sur l'hôpital que j'avais.
Peter: Nico.
Gabriel: Arpèges là. T-Fields. View logs. Building application per hour. Il y a de quoi qui a pété ici. C'est l'heure d'arrêter une histoire de syntaxe. Il a dû voulu changer, genre, des valeurs ou un symbole. Je vais aller voir dans l'historique Git, mais c'est clairement de quoi que je crois pas de fixer. C'est ça. J'ai comme accès à toutes les affaires. Je vois comment c'est déployé et tout. C'est pas compliqué son truc là. Je vois que c'est genre echo-tech.com. Fait que that's it.
Gabriel: En tout cas, si jamais tu as des infos à me partager concernant les POS, puis qu'est-ce qu'on peut retrouver comme information, ça peut m'être utile. Parce que moi, je vais quand même vouloir aller fouiller le chemin qu'il m'a demandé d'aller fouiller, mais je n'ai pas grand-chose que je peux faire de plus que tout ce qu'on a dit.
Peter: C'est peut-être une alternative qui pourrait être disponible aussi. Dans le fond, lui, DisplayForce, ça ne venait pas avec les bornes, comme tu as dit tantôt. Il s'est chargé de connecter ça, je pense. Il y a peut-être d'autres choses, d'autres alternatives plus ouvertes un peu.
Gabriel: C'est comme tu disais, stride, follow strict PC. Justement, quand tu veux être PCI DSS compliant, tu ne peux pas commencer à liquider le data du monde. La réponse est directe, tu ne peux pas.
Peter: C'est un seul coup de pied d'en faire ça. Comme je disais, en plus, quand on valide le code postal, quand on fait la validation d'adresse, c'est le code postal que la personne doit entrer. Ce serait terrible s'ils se mettaient à donner des codes postaux associés à des cartes. Ce serait comme une sécurité du monde.
Gabriel: En même temps, tu vois ici.
Peter: Je te donne le CV Rembox. Oui, vas-y.
Gabriel: Ouais, c'est ça. Regarde, tu vois, j'ai XcardOptions, ElementOptions, StripeChange ici. Mettons, dans un certain cas, avec Stripe, dans un événement, tu es capable de capturer l'événement. Puis là, tu vois, dedans, il y a Value, PostAddCode, puis il y a de quoi.
Peter: Ouais, mais est-ce que tu peux aussi demander, mettons, quand tu fais du checkout avec Paypal ou tout ça, tu peux demander tu sais comme toi des fois maintenant tu fais je vais finaliser ma commande avec Paypal, ben ils utilisent le checkout process de Paypal puis eux autres, le fournisseur, le système peuvent dire à Paypal ramasse-moi l'adresse de la personne, l'adresse de livraison, l'adresse de ça tu sais ils demandent à Paypal ramasse-moi l'info c'est toi tu vas être bon de faire un formulaire et finalement c'est Stripe qui fait le formulaire de saisie de l'adresse de shipping
Peter: puis de toutes les choses donc à la fin oui le système peut après ça dire donne-moi l'info que t'as ramassé que je t'ai demandé de me ramasser.
Gabriel: Ok ouais. Fait qu'il y a cette manière-là.
Peter: C'est là que je vois ça, là, ouais. Mais c'est la même chose que je te disais.
Gabriel: Ouais, comment ça le fait à l'accueil pour d'autres fournisseurs, c'est chiant.
Peter: Ouais, c'est ça, puis ça revient vraiment au phénomène de genre, t'as demandé le code postal à la personne. C'est pas, je te donne un code postal, même si la personne ne me l'a pas donné. Ça revient à dire, pose la question au client à chaque fois, puis rentre l'armée.
Gabriel: As part of the postal code, you can access the postal code via API or dashboard if you enable address collection as part of your implementation. C'est potentiellement, tu peux le faire en activant, c'est ça, l'adresse collection. L'adresse collection, c'est ça.
Peter: Fait que la personne qui fait des churros, faudrait qu'elle demande l'adresse à la personne pour la taper dans le site.
Gabriel: Ouais.
Peter: Quand vous rendez-vous dans les magasins, je veux savoir votre code postal, s'il vous plaît.
Gabriel: Ouais, puis tu peux dire non, tu sais. It's included in the verification check. Ça va l'avoir dans le check out form qu'on peut associer avec l'achat. Ça peut être partiel. Il y a de l'air à y avoir une manière de le faire quand la personne fait une transaction, mais il faut que la personne le rende. À quel point je vais pouvoir regarder avec cette stratégie-là. C'est pour Stripe. Après ça, je pose la même question. Forme monérisse. Bonne chance. Ça revient au système de vente.
Peter: Si tu demandes ça à Stripe, c'est parce que le système de vente utilise Stripe en mode checkout page. Il dit à Stripe que tu colles la partie, ce qui est rare. Ce n'est pas si rare que ça, mais la majorité du temps, la carte est quasiment demandée directement sur le site de la personne. C'est lui-même qui a collecté sa propre adresse. On demande ça à Steve souvent qu'on fait un plus petit système. Puis on lui demande, hey, peux-tu me ramasser toute l'info à la fin? Parce que moi, je n'ai pas ça de mon bord.
Peter: Tu m'enverras le rapport à la fin que je puisse faire mon shipping puis mes affaires.
Gabriel: Oui, exact. Mais ça semble être qu'il faut vraiment, dans chacun des cas, il faut vraiment le configurer spécifiquement pour que ça fasse ça. Puis, tu sais, on s'entend que n'importe quel vendeur qui est sur place ne l'aura pas de configuré.
Peter: Pour moi, non. En plus, c'est du DPV manuel terriblement. C'est ça, c'est ça.
Gabriel: Ben oui, ben oui, exact. Tu veux pas commencer à complexifier le système, là, ils font déjà pas assez d'argent.
Peter: Ben oui, c'est ça.
Gabriel: All right. Bon, ben c'est bon. En tout cas, je t'ai pas mal partagé tous les détails que j'avais.
Peter: Oui, absolument.
Gabriel: Puis, ben, Laurent, je te ferais signe pour la suite de qu'est-ce que moi, je vais aller fouiller comme information. Puis, t'sais, pour le prochain cas, je pourrais discuter avec pour ces points-là. Autrement dit, genre, oui, ça semble être possible avec les différents POS de récupérer l'information, mais c'est pas sans défi. Puis, il faut que tu poses soit la question ou il faudrait creuser davantage pour avoir, t'sais...
Laurent: Mais on a reçu... Moi, je me demandais, c'est correct de faire tout ce travail-là, mais est-ce qu'il y a de la job pour faire de quoi rapidement, pour occuper quelqu'un? Moi, c'est plus ça. Je me disais, si on embarque rapidement un dev pour l'aider, est-ce qu'on est capable d'y offrir de quoi rapidement? Au lieu de dire, ça va te coûter 100 000 pour la prochaine année pour faire ce que tu as à faire. Mais si tu embarques quelqu'un là, ça va te coûter 15 000 par mois. pour un dev qui va t'aider ou.
Gabriel: Il n'y a pas assez d'ouvrages pour... Oui, c'est ça. C'est là que je suis rendu aller regarder l'ouvrage. C'est exactement ça que je vais aller voir. En ce moment, clairement, le premier point sur lequel on aurait pu avoir de l'ouvrage, c'est très incertain. Je ne pense pas qu'on serait en mesure de monter une plateforme de statistiques. Et puis qui permet d'extraire le data comme les codes postales tant que ça facilement. Je vois pas vraiment d'avenue de projet qu'on pourrait réaliser en ce moment qui pourrait coûter dans les alentours de 100 000 pour faire ça.
Gabriel: Après ça, pour ce qui est des autres points, parce que tu sais, C'était là, justement, dans l'appel, il y avait mille affaires qu'il voulait faire. Moi j'ai dépilé ça en priorité. Tu vois, j'ai ici, dans le fond, j'ai un document ici. POS Integration with Postal Code Extraction, c'était le premier point qu'on risque de dire, ben écoute, malheureusement, quand on en fait, ça prend le bord. Il y a les fixes pour le truc d'hôpital, que ça, c'est pas assez long pour dire qu'on commence à mettre quelqu'un là-dessus, même si ça prend un certain temps.
Gabriel: Je veux dire, moi, en analysant le code base, puis en regardant le PDF, En moins que je vois des affaires qui prennent plus de temps, je vais lui dire. Je pense que c'est parce qu'il le sent en plus, il le veut comme en priorité. Il aimerait ça de l'avoir avant le 20 pour qu'il puisse signer avec l'hôpital.
Gabriel: Je suis comme, écoute, tant qu'il a ouvert le projet, je veux dire, on peut bien aller updater des lignes de code pour changer des signes de pièces à quelque part pour que les hôpitaux soient contents et qu'il puisse signer un contrat. Mais après ça, les prochaines affaires, c'est là qu'il faut commencer à regarder quelqu'un d'autre qui passe dessus.
Gabriel: Display for CI dashboard integration de comment on fait comme là tu vois ça c'est là que je disais on risque d'avoir une mauvaise nouvelle parce qu'il voulait éviter de payer 25 000$ pour être White Label mais les PIs s'en battent rien que disponible pour le White Label. Ça peut être l'option de se dire contacte-les pour vérifier ce qui est possible parce qu'en ce moment moi ce que je vois c'est qu'il n'y a rien qui est possible. Après ça, on tombe dans les trucs pour augmenter les user profiles.
Gabriel: Il voulait commencer à améliorer sa plateforme, parce qu'il y a déjà une plateforme avec laquelle on peut se connecter. Est-ce qu'il peut avoir un login avec Google, etc. Mais il ne se connecte pas sur Google, sur ses plateformes de chargement. J'ai des questions à y poser. Je ne peux pas creuser ce point-là. Après ça, le système de Battery Ownership Tracking, je pense que c'est ça qu'il disait.
Gabriel: Dans le fond, quand on se ramène à comment fonctionnent les vélos Bixi, il faudrait regarder comment ça marche quand tu fais tes paiements avec les vélos Bixi, puis comment ça se fait que c'est simple et que ça fonctionne bien de même. Parce qu'il avait mentionné justement dans l'appel que c'était comme le monde, il chialait quand il faisait des dépôts. Comment qu'on peut réellement gagner ça? Mais dans le fond, Bon, il m'a vraiment rien dit de ça. Il dit les dépôts. Mettons que je regarde dépôts.
Peter: As-tu fait du vélo STS?
Gabriel: Non, je ne l'ai même pas essayé. Tu vois, Jean-Christophe, il a dit qu'il y a un dépôt qui est pris pour justement s'assurer que la batterie revienne. Après ça, il m'avait mentionné aussi les dépôts. Il avait dit, bon, il demande une carte de crédit, blablabla, pour prendre le dépôt. C'est où qu'il disait ça? Je ne sais pas. Je ne me souviens plus.
Gabriel: En tout cas, il a mentionné, je me souviens que dans l'appel, il a mentionné que le monde, il se plaignait quand il y avait des dépôts, mais il n'y a pas mis l'option non plus. Je n'ai rien vu. Ah oui.
Peter: Il a une carte de crédit 500 piastres et ça fait comme bourré temporairement.
Gabriel: Ouais, c'est ça. C'est dans les différents projets qu'il veut faire. dans les autres affaires que nous autres on peut réellement s'impliquer. Il y a le côté Display Force, puis il y a le côté balancer le truc chinois, puis regarder les affaires pour le supporter d'autres POS, mais ça, ça va dépendre de son endroit. Pour vrai, moi en ce moment, il n'y a pas grand projet qu'on peut décoller, à moins que lui débloque des affaires ou qu'il dise, OK, bien...
Gabriel: Je veux dire, les deux principaux projets qu'il aimerait avoir ne sont pas vraiment possibles en ce moment.
Peter: C'est un projet qui nécessite des itérations, des analyses, vérifier, se consulter, se parler par-ci par-là, faire des recherches, puis qu'il appelle un peu partout.
Gabriel: Oui, c'est ça. Il n'y en a pas tant. En fait, c'est ça. Techniquement, les projets pourraient aller de l'avant, mais c'est ça, c'est faux l'incertitude. Comme le truc de POS, je veux dire, il faudrait essayer des affaires, mais tu sais, il ne sera peut-être pas content avec la réponse. Ben, là, c'est son problème, il n'est pas content avec ça, mais je veux dire, on n'a pas un projet de, on peut mettre quelqu'un de junior qui va être capable de travailler là-dessus ou même quelqu'un d'intermédiaire qui va être capable de faire avancer la patente.
Gabriel: Parce que malgré tout, il faut qu'on ait quelqu'un qui a de l'expérience à la limite avec les POS et qui comprenne ces affaires-là. Il y a des enjeux qui ne sont pas techniques en ce moment. Mettons qu'il veut vraiment s'intégrer avec le gars du foodtruck, je vais dire bonne chance. Le défi n'est pas technique, il est littéralement comment tu vas faire pour convaincre le foodtruck. Puis dans l'appel, il avait dit, ah, ça, c'est mon problème, c'est moi qui vais s'en occuper. Je suis comme moi, mais tu sais, ça marche pas. C'est faux savoir, c'est ça.
Gabriel: Ça marche pas à cette étape-là. On n'a pas de projet. Tu sais, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est.
Peter: C'Est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'est, c'.
Gabriel: L'autre affaire qu'il a discuté, parce qu'il avait rendu à la fin de l'appel, moi j'ai demandé depuis le mois dans l'ordre, c'est quoi tes priorités, pis rendu vers la fin, c'est là qu'il m'a parlé du côté self-service, clients, plateformes pour genre les festivals pis ces affaires-là, mais t'sais on commence à être loin dans ces affaires de priorité, mais ça tu vois on serait capable de mettre quelqu'un pis montrer un dashboard pour faire ça, mais c'est essentiellement refaire qu'est-ce que DisplayForce fait, mais... Avec un scope.
Gabriel: avec un scope, mais est-ce qu'on est capable d'aller cumuler les mêmes stats que lui, puis est-ce qu'il y a un autre... Les affaires à préciser par rapport à ça, c'est la caméra, comment elle fonctionne, est-ce que c'est nous autres qu'on fait le tracking visuel, puis on recode finalement un display force sur lequel tu as le contrôle, où tu es mieux de payer 25 000$, puis dans le fond ça t'expose le data, il est peut-être mieux de payer 25 000$ aussi.
Gabriel: C'est ça l'affaire, même si ça ne l'étend pas, ça ne coûtera pas 100 000$ de projet et il va y avoir peut-être de quoi dans lequel il peut déjà s'intégrer et sauver tout le temps de développement. C'est ça, ça ressemble à ça. C'est un drôle de projet, c'est là qu'on en est. Moi je vais aller continuer côté back-end front-end, aller voir si je suis capable de faire tourner ça, faire les petits fixes pour l'hôpital et après ça on aurait dû pour ça boucler un appel avec.
Gabriel: Mais je pourrais pas y fournir déjà un résultat ou un rapport d'analyse quelconque. Principalement, je vais y répondre que la plupart des affaires qu'il veut faire, soit on n'est pas capable d'y faire, soit il y a trop d'incertitude.
Peter: C'est ça. Bon travail.
Gabriel: All right. Écoute, Laurent, je te reçois.
Peter: Merci.
Gabriel: Merci Peter pour les réponses aux questions.
Peter: Merci à vous autres.
