# Client Requirements - Hiko Project

**Client:** <PERSON>  
**Date:** August 13, 2025  
**Priority Order:** Based on client emphasis and business impact

## Tier 1 - Critical Priority (Immediate Revenue Impact)

### 1. POS Integration with Postal Code Extraction ⭐ TOP PRIORITY
**Business Impact:** Potential $115,000 funding opportunity

**Requirements:**
- Extract postal codes from credit card transactions across multiple POS systems:
  - Stripe (current)
  - Clover
  - Moneris
- Capture transaction amount + postal code for demographic analytics
- Generate statistics: "73% local customers, 45% revenue from tourists"
- Target customers: Festival organizers, Tourism Quebec compliance
- Must work with existing station hardware

**Technical Constraints:**
- Different POS systems have varying API limitations
- Legal/compliance considerations for payment data
- Security requirements for credit card information handling

### 2. Hospital Deployment Modifications
**Business Impact:** Deployment blocker for confirmed contract

**Requirements:**
- Simple text/formatting corrections:
  - Dollar sign placement (French vs English formats)
  - Icon changes (replace gear icon)
  - Minor wording adjustments
- Must be completed before hospital deployment
- Quick turnaround needed

### 3. Display Force AI Dashboard Integration
**Business Impact:** Avoid $25,000 white-label fee, enable self-service

**Requirements:**
- Integrate Display Force AI analytics into unified platform
- Self-service client dashboard access
- Export capabilities for:
  - Visitor insights
  - Demographics data
  - Facial expression analysis
- API integration to replace manual data extraction
- Client-specific access levels

## Tier 2 - High Priority (Business Growth)

### 4. Enhanced User Profile System
**Current State:** Basic phone + deposit model  
**Target State:** Full user engagement platform

**Requirements:**
- Google/Microsoft OAuth integration
- Social media integration (Instagram, Facebook, TikTok)
- Usage-based billing instead of deposits
- Marketing incentives for social media posts about batteries
- User preference tracking
- Loyalty program foundation

### 5. Battery Ownership Tracking System
**Business Model:** Convert rentals to sales

**Requirements:**
- Track batteries considered "purchased" after 2-3 days
- Allow owned battery recharging at any station
- Personal branding options for owned batteries
- Corporate bulk battery programs
- Transfer ownership capabilities
- Battery lifecycle management

### 6. Multi-POS Payment System Replacement
**Current Issues:** Chinese POS system limitations

**Requirements:**
- Replace existing Chinese POS with local control
- Tap-to-pay integration
- Temperature resistance (35°C to 60°C operating range)
- Prevent unauthorized access (lockdown mode)
- Variable pricing control (not hardcoded $2/hour)
- Remote monitoring and control capabilities

### 7. Self-Service Client Platform
**Target Users:** Festivals, businesses, establishments

**Requirements:**
- Client portal for content management
- Upload advertising videos/images
- View location-specific analytics
- Tiered access based on subscription level
- Revenue sharing dashboard
- Campaign scheduling tools

## Tier 3 - Important (Long-term Strategy)

### 8. Comprehensive Reporting Dashboard
**Scope:** Real-time business intelligence

**Requirements:**
- Real-time station monitoring
- Transaction analytics by location/time
- Hardware status tracking (POS online/offline, screen status)
- Client-specific reporting views
- Revenue breakdown by customer type
- Maintenance scheduling integration
- Alert system for hardware issues

### 9. API Unification Platform
**Problem:** Multiple competing third-party systems

**Requirements:**
- Unified control interface for:
  - Battery management systems
  - Screen advertising platforms
  - Analytics systems
- Prevent conflicts between third-party systems
- Single platform control dashboard
- Standardized API responses
- Error handling and fallback systems

### 10. Infrastructure Migration
**Current Risk:** Heavy dependency on Chinese technology

**Requirements:**
- Migrate away from Chinese hardware/software dependencies
- Linux-based system architecture
- Local hardware control systems
- Enhanced security and data ownership
- Compliance with Canadian data sovereignty requirements

## Technical Architecture Context

### Current Technology Stack:
- **Frontend:** Svelte, TypeScript, Tailwind CSS
- **Backend:** Supabase
- **Infrastructure:** AWS Route 53, Cloudflare
- **Payment:** Stripe
- **Analytics:** Display Force AI
- **Communication:** Twilio (SMS verification)

### Key Integration Points:
1. **Multiple POS Systems** - Stripe, Clover, Moneris APIs
2. **Display Force AI** - Computer vision analytics API
3. **Battery Management** - Third-party hardware APIs
4. **Screen Advertising** - Digital display management
5. **Customer Authentication** - OAuth providers
6. **SMS Services** - Twilio integration

### Business Model Requirements:
- **Multi-revenue streams:** Battery rental, advertising, data analytics
- **B2B Focus:** Establishments, festivals, tourism organizations
- **Self-service capability:** Reduce manual intervention
- **Scalable architecture:** Support multiple client configurations
- **Data privacy compliance:** Anonymous analytics, GDPR considerations

## Success Metrics:
1. **POS Integration:** Successfully extract postal codes from 3 POS systems
2. **Client Self-Service:** 80% reduction in manual dashboard requests
3. **Revenue Diversification:** 30% revenue from data analytics services
4. **System Reliability:** 99.5% uptime for critical payment processing
5. **Client Satisfaction:** Self-service platform adoption by 90% of clients