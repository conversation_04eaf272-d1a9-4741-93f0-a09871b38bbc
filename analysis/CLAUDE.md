# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an analysis repository for the Hiko project - a battery charging station business that combines portable battery rental with digital advertising and visitor analytics. The project involves integrating multiple third-party systems through APIs:

- Battery management systems
- Digital display/advertising platforms (mentioned: Display4CI with Tailwind CSS, Superbase backend)
- Point-of-sale (POS) systems (currently using Stripe)
- Visitor analytics and demographic tracking through camera systems

## Repository Structure

This repository contains analysis documents and specifications rather than source code:

- `2025-08-13-initial-meeting.md` - Raw meeting transcription (2+ hours)
- `2025-08-13-initial-meeting-parsed.md` - Formatted meeting notes with timestamps
- `api/` - Technical specifications and documentation for hardware components
- `displ/` - Analytics reports and visitor insights

## Key Technical Context

### Architecture Goals
- Unified platform integrating multiple third-party APIs
- Client management system allowing customers to sequence different services
- Real-time data aggregation from battery systems, advertising displays, and visitor analytics
- Payment processing integration for both customer transactions and establishment billing

### Third-Party Integrations
- **Display Systems**: Tailwind CSS frontend with Superbase backend
- **Payment Processing**: Stripe API (potential for custom tap-to-pay solutions)
- **Analytics**: Visitor demographic tracking with privacy protection (faces blurred, anonymous data export)
- **Hardware**: Battery management, screen control, POS terminal integration

### Development Considerations
- All third-party systems expose APIs for integration
- Need to handle API rate limits and access controls
- Security focus on payment processing compliance
- Privacy-first approach to visitor analytics
- Multi-tenant architecture for different client configurations

## Business Context

The system serves establishments (restaurants, bars, etc.) by providing:
1. Portable battery rental for customer convenience
2. Digital advertising revenue through integrated displays
3. Visitor foot traffic analytics and demographic insights
4. Multiple revenue streams (device rental, advertising, data insights)

This context is crucial for understanding feature requirements and data flow patterns when working on related development tasks.