# Service Layer Improvements

## Overview

The current service layer in HIKO lacks proper abstractions, has inconsistent error handling, and exhibits tight coupling between components and external services. This analysis provides recommendations for building a robust, maintainable service architecture.

## Current Service Layer Problems

### 1. Minimal Service Abstractions

**Current State**: Services are too thin and provide minimal value

**`src/lib/services/api.ts`** (24 lines):
```typescript
export const api = axios.create({
    baseURL: "/api",
});

api.interceptors.response.use(
    (response) => response,
    async (error) => {
        const errorStatus = error.response?.status;
        if (errorStatus === 401) {
            await supabase.auth.signOut();
            await goto(Route.Login);
        }
        return Promise.reject(error);
    },
);
```

**Problems**:
- No request/response transformation
- No centralized error handling beyond 401
- No retry logic or timeout handling
- No request logging or monitoring

**`src/lib/services/supabase.ts`** (6 lines):
```typescript
import { createClient } from "@supabase/supabase-js";
import { env } from "$env/dynamic/public";
import type { Database } from "$types/supabase";

export const supabase = createClient<Database>(env.PUBLIC_SUPABASE_URL, env.PUBLIC_SUPABASE_ANON_KEY);
```

**Problems**:
- No error handling wrapper
- No query optimization
- No caching layer
- No connection management

### 2. Tight Coupling in Components

**Problem**: Components directly import and use multiple services
```svelte
<script>
  import { api } from '$lib/services/api';
  import { supabase } from '$lib/services/supabase';
  import { stripe } from '$lib/services/stripe';
  
  // Component handling multiple external dependencies
  const handleAction = async () => {
    const { data: order } = await supabase.from('orders').select();
    await stripe.confirmPayment(/* ... */);
    await api.post('/api/notifications', { orderId: order.id });
  };
</script>
```

**Issues**:
- Hard to test (must mock multiple services)
- Violates single responsibility principle
- Creates brittle dependencies
- Business logic scattered across components

## Recommended Service Architecture

### 1. Robust API Client

**`src/lib/services/api-client.ts`**:
```typescript
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    page?: number;
    totalCount?: number;
    hasMore?: boolean;
  };
}

export class ApiClient {
  private axios: AxiosInstance;
  private requestQueue = new Map<string, Promise<any>>();

  constructor(baseURL = '/api') {
    this.axios = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for authentication
    this.axios.interceptors.request.use(
      (config) => {
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const status = error.response?.status;
        
        if (status === 401) {
          await this.handleUnauthorized();
          return Promise.reject(new UnauthorizedError());
        }
        
        if (status === 429) {
          // Handle rate limiting
          await this.handleRateLimit(error);
          return this.axios.request(error.config);
        }
        
        if (status >= 500) {
          // Server errors - could retry
          throw new ServerError(error.response?.data?.message || 'Server error');
        }
        
        throw new ApiError(error.response?.data?.message || 'Request failed');
      }
    );
  }

  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const cacheKey = `GET:${url}:${JSON.stringify(params)}`;
    
    // Prevent duplicate requests
    if (this.requestQueue.has(cacheKey)) {
      return this.requestQueue.get(cacheKey);
    }

    const request = this.axios.get<ApiResponse<T>>(url, { params })
      .then(response => response.data)
      .finally(() => this.requestQueue.delete(cacheKey));

    this.requestQueue.set(cacheKey, request);
    return request;
  }

  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.axios.post<ApiResponse<T>>(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.axios.put<ApiResponse<T>>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.axios.delete<ApiResponse<T>>(url);
    return response.data;
  }

  private async handleUnauthorized() {
    // Clear auth state and redirect
    await supabase.auth.signOut();
    goto('/login');
  }

  private async handleRateLimit(error: any) {
    const retryAfter = error.response?.headers['retry-after'];
    if (retryAfter) {
      await new Promise(resolve => setTimeout(resolve, parseInt(retryAfter) * 1000));
    }
  }

  private getAuthToken(): string | null {
    // Implementation depends on auth strategy
    return localStorage.getItem('auth-token');
  }
}
```

### 2. Database Service Layer

**`src/lib/services/database-service.ts`**:
```typescript
export class DatabaseService {
  constructor(private client: SupabaseClient<Database>) {}

  async query<T extends keyof Database['public']['Tables']>(
    table: T,
    options: {
      select?: string;
      filters?: Record<string, any>;
      orderBy?: { column: string; ascending?: boolean }[];
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<Database['public']['Tables'][T]['Row'][]> {
    let query = this.client.from(table).select(options.select || '*');

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else if (value !== null && value !== undefined) {
          query = query.eq(key, value);
        }
      });
    }

    // Apply ordering
    if (options.orderBy) {
      options.orderBy.forEach(({ column, ascending = true }) => {
        query = query.order(column, { ascending });
      });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query;
    
    if (error) {
      throw new DatabaseError(error.message, error.code);
    }
    
    return data || [];
  }

  async insert<T extends keyof Database['public']['Tables']>(
    table: T,
    data: Database['public']['Tables'][T]['Insert']
  ): Promise<Database['public']['Tables'][T]['Row']> {
    const { data: result, error } = await this.client
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(error.message, error.code);
    }

    return result;
  }

  async update<T extends keyof Database['public']['Tables']>(
    table: T,
    id: string | number,
    data: Database['public']['Tables'][T]['Update']
  ): Promise<Database['public']['Tables'][T]['Row']> {
    const { data: result, error } = await this.client
      .from(table)
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(error.message, error.code);
    }

    return result;
  }

  async delete<T extends keyof Database['public']['Tables']>(
    table: T,
    id: string | number
  ): Promise<void> {
    const { error } = await this.client
      .from(table)
      .delete()
      .eq('id', id);

    if (error) {
      throw new DatabaseError(error.message, error.code);
    }
  }

  // Real-time subscriptions with cleanup
  subscribe<T extends keyof Database['public']['Tables']>(
    table: T,
    callback: (payload: any) => void,
    filters?: Record<string, any>
  ): () => void {
    let subscription = this.client
      .channel(`${table}_changes`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: table as string,
          ...(filters && { filter: Object.entries(filters).map(([k, v]) => `${k}=eq.${v}`).join(',') })
        }, 
        callback
      )
      .subscribe();

    // Return cleanup function
    return () => {
      subscription.unsubscribe();
    };
  }
}
```

### 3. Domain-Specific Services

**`src/lib/domains/orders/services/order-service.ts`**:
```typescript
export interface CreateOrderParams {
  stationId: string;
  slotId: number;
  orderType: OrderType;
  paymentMethodId?: string;
}

export interface OrderFilters {
  userId?: string;
  status?: OrderStatus[];
  dateFrom?: Date;
  dateTo?: Date;
}

export class OrderService {
  constructor(
    private database: DatabaseService,
    private api: ApiClient,
    private payment: PaymentService,
    private notification: NotificationService
  ) {}

  async createOrder(params: CreateOrderParams): Promise<Order> {
    // Business logic validation
    await this.validateOrderCreation(params);

    // Handle payment if required
    let paymentIntent: PaymentIntent | null = null;
    if (params.orderType === OrderType.Purchase && params.paymentMethodId) {
      paymentIntent = await this.payment.createPaymentIntent({
        paymentMethodId: params.paymentMethodId,
        amount: await this.calculateOrderAmount(params),
      });
    }

    // Create order in database
    const order = await this.database.insert('orders', {
      station_id: params.stationId,
      slot_id: params.slotId,
      order_type: params.orderType,
      status: 'pending',
      payment_intent_id: paymentIntent?.id,
      created_at: new Date().toISOString(),
    });

    // Send notifications
    await this.notification.sendOrderConfirmation(order);

    return order;
  }

  async getOrders(filters: OrderFilters = {}): Promise<Order[]> {
    const dbFilters: Record<string, any> = {};
    
    if (filters.userId) dbFilters.user_id = filters.userId;
    if (filters.status) dbFilters.status = filters.status;

    let orders = await this.database.query('orders', {
      filters: dbFilters,
      orderBy: [{ column: 'created_at', ascending: false }],
    });

    // Apply date filters (could be moved to database layer)
    if (filters.dateFrom || filters.dateTo) {
      orders = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        if (filters.dateFrom && orderDate < filters.dateFrom) return false;
        if (filters.dateTo && orderDate > filters.dateTo) return false;
        return true;
      });
    }

    return orders;
  }

  async getActiveOrder(userId: string): Promise<Order | null> {
    const orders = await this.database.query('orders', {
      filters: { 
        user_id: userId, 
        status: ['active', 'pending'] 
      },
      orderBy: [{ column: 'created_at', ascending: false }],
      limit: 1,
    });

    return orders[0] || null;
  }

  async cancelOrder(orderId: string, reason?: string): Promise<void> {
    const order = await this.getOrderById(orderId);
    
    if (!this.canCancelOrder(order)) {
      throw new BusinessRuleError('Cannot cancel order in current state');
    }

    // Handle payment refund if necessary
    if (order.payment_intent_id) {
      await this.payment.refundPayment(order.payment_intent_id);
    }

    // Update order status
    await this.database.update('orders', orderId, {
      status: 'cancelled',
      cancelled_at: new Date().toISOString(),
      cancellation_reason: reason,
    });

    // Send notifications
    await this.notification.sendOrderCancellation(order);
  }

  // Real-time order updates
  subscribeToOrderUpdates(orderId: string, callback: (order: Order) => void): () => void {
    return this.database.subscribe('orders', 
      (payload) => {
        if (payload.new.id === orderId) {
          callback(payload.new);
        }
      },
      { id: orderId }
    );
  }

  private async validateOrderCreation(params: CreateOrderParams): Promise<void> {
    // Check if station exists and is available
    const station = await this.database.query('stations', {
      filters: { id: params.stationId, status: 'active' },
      limit: 1,
    });

    if (!station.length) {
      throw new ValidationError('Station not available');
    }

    // Check if slot is available
    // Additional business rule validations...
  }

  private canCancelOrder(order: Order): boolean {
    return ['pending', 'active'].includes(order.status);
  }

  private async calculateOrderAmount(params: CreateOrderParams): Promise<number> {
    // Implement pricing calculation logic
    return 1000; // cents
  }

  private async getOrderById(orderId: string): Promise<Order> {
    const orders = await this.database.query('orders', {
      filters: { id: orderId },
      limit: 1,
    });

    if (!orders.length) {
      throw new NotFoundError('Order not found');
    }

    return orders[0];
  }
}
```

### 4. Service Container & Dependency Injection

**`src/lib/services/container.ts`**:
```typescript
type ServiceFactory<T> = () => T;
type ServiceInstance<T> = T;

export class ServiceContainer {
  private singletons = new Map<string, ServiceInstance<any>>();
  private factories = new Map<string, ServiceFactory<any>>();

  register<T>(key: string, factory: ServiceFactory<T>, singleton = true): void {
    this.factories.set(key, factory);
    if (!singleton) {
      this.singletons.delete(key); // Ensure it's not cached
    }
  }

  get<T>(key: string): T {
    // Return singleton if exists
    if (this.singletons.has(key)) {
      return this.singletons.get(key);
    }

    // Create new instance
    const factory = this.factories.get(key);
    if (!factory) {
      throw new Error(`Service "${key}" not registered`);
    }

    const instance = factory();
    
    // Cache if singleton (default behavior)
    if (!this.singletons.has(key)) {
      this.singletons.set(key, instance);
    }

    return instance;
  }

  has(key: string): boolean {
    return this.factories.has(key);
  }
}

// Global container instance
export const container = new ServiceContainer();

// Setup function to register all services
export function setupServices() {
  // Core services
  container.register('apiClient', () => new ApiClient());
  container.register('databaseService', () => new DatabaseService(supabase));
  
  // Domain services
  container.register('orderService', () => new OrderService(
    container.get('databaseService'),
    container.get('apiClient'),
    container.get('paymentService'),
    container.get('notificationService')
  ));
  
  container.register('paymentService', () => new PaymentService(
    container.get('apiClient'),
    // stripe client
  ));
  
  // ... other services
}

// Helper function for components
export function getService<T>(key: string): T {
  return container.get<T>(key);
}
```

## Error Handling Strategy

### 1. Custom Error Classes

**`src/lib/errors/index.ts`**:
```typescript
export class BaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class ValidationError extends BaseError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

export class BusinessRuleError extends BaseError {
  constructor(message: string) {
    super(message, 'BUSINESS_RULE_ERROR', 422);
  }
}

export class UnauthorizedError extends BaseError {
  constructor(message = 'Unauthorized') {
    super(message, 'UNAUTHORIZED', 401);
  }
}

export class NotFoundError extends BaseError {
  constructor(message = 'Resource not found') {
    super(message, 'NOT_FOUND', 404);
  }
}

export class DatabaseError extends BaseError {
  constructor(message: string, public dbCode?: string) {
    super(message, 'DATABASE_ERROR', 500);
  }
}
```

### 2. Global Error Handler

**`src/lib/error-handling/global-handler.ts`**:
```typescript
export class GlobalErrorHandler {
  static handle(error: Error, context: string = 'Unknown'): void {
    console.error(`[${context}] Error:`, error);
    
    if (error instanceof BaseError) {
      this.handleKnownError(error, context);
    } else {
      this.handleUnknownError(error, context);
    }
  }

  private static handleKnownError(error: BaseError, context: string): void {
    // Log to external service (Sentry, LogRocket, etc.)
    // Show user-friendly message
    // Track metrics
  }

  private static handleUnknownError(error: Error, context: string): void {
    // Log full error details
    // Show generic error message
    // Alert development team
  }
}
```

## Service Integration in Components

**Example Usage in Components:**
```svelte
<script lang="ts">
  import { onMount } from 'svelte';
  import { getService } from '$lib/services/container';
  import type { OrderService } from '$lib/domains/orders/services/order-service';
  
  const orderService = getService<OrderService>('orderService');
  
  let orders: Order[] = [];
  let loading = true;
  let error: string | null = null;
  
  onMount(async () => {
    try {
      orders = await orderService.getOrders({ userId: $user?.id });
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load orders';
    } finally {
      loading = false;
    }
  });
  
  const handleCancelOrder = async (orderId: string) => {
    try {
      await orderService.cancelOrder(orderId, 'User requested');
      orders = orders.filter(o => o.id !== orderId);
    } catch (err) {
      // Handle error
    }
  };
</script>
```

## Benefits of Improved Service Layer

1. **Better Testability**: Services can be easily mocked and tested in isolation
2. **Loose Coupling**: Components depend on abstractions, not concrete implementations
3. **Consistent Error Handling**: Centralized error handling across the application
4. **Improved Maintainability**: Business logic centralized in service layer
5. **Better Performance**: Request deduplication, caching, and optimization
6. **Scalability**: Easy to extend and modify service behavior

## Migration Strategy

### Phase 1: Create Core Infrastructure
1. Implement `ApiClient` and `DatabaseService`
2. Create error classes and global error handler
3. Set up service container

### Phase 2: Extract Domain Services
1. Start with `OrderService` (most complex domain)
2. Migrate components to use service container
3. Add comprehensive error handling

### Phase 3: Optimize and Extend
1. Add caching layer
2. Implement retry mechanisms
3. Add performance monitoring
4. Create additional domain services

This service layer architecture will provide a solid foundation for the HIKO application, making it more maintainable, testable, and scalable.