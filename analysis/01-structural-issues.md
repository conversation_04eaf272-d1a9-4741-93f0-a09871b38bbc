# Structural Issues & Code Organization

## Overview

The HIKO codebase has several structural issues that affect maintainability and scalability. This analysis identifies key problems in file organization, naming conventions, and architectural patterns.

## Critical Issues

### 1. Inconsistent Naming Conventions

**Problem**: Mixed naming patterns throughout the codebase
- Files: `useActiveOrder.ts` (camelCase) vs `payment-methods.ts` (kebab-case)
- Components: `BatteryStationMap.svelte` (PascalCase) - good
- API routes: Mixed patterns in route structure

**Impact**: Reduces developer productivity and creates confusion

**Recommendation**: 
```
Adopt consistent conventions:
- Files: kebab-case (payment-methods.ts, active-order.ts)
- Components: PascalCase (BatteryStationMap.svelte) ✓ Already consistent
- Directories: kebab-case
- Variables/functions: camelCase
```

### 2. Deep Nested API Route Structure

**Problem**: Overly complex route nesting
```
src/routes/api/payments/orders/purchase-current/+server.ts
src/routes/api/cron/sms/users-without-orders/+server.ts
```

**Current Structure Issues**:
- Routes are 5+ levels deep
- Difficult to navigate and remember
- Creates long import paths

**Recommended Flatter Structure**:
```
src/routes/api/
├── orders/
│   ├── purchase/+server.ts
│   ├── rent/+server.ts
│   └── purchase-current/+server.ts
├── payments/
│   ├── methods/+server.ts
│   └── customers/+server.ts
├── cron/
│   ├── orders-collect/+server.ts
│   ├── orders-resolve/+server.ts
│   └── sms-inactive-users/+server.ts
```

### 3. Business Logic Distribution

**Problem**: Order management logic scattered across multiple layers
- `src/lib/composables/useActiveOrder.ts` - UI state management
- `src/lib/utils/orders.ts` - Data manipulation utilities
- `src/lib/stores/order.ts` - Global state
- `src/routes/api/payments/orders/` - API endpoints
- Components directly calling multiple services

**Impact**: 
- Difficult to maintain business rules
- Logic duplication
- Testing challenges
- Inconsistent behavior

**Recommended Architecture**:
```typescript
// Domain-based organization
src/lib/domains/orders/
├── services/
│   └── order-service.ts          // Core business logic
├── types/
│   └── order-types.ts           // Domain-specific types
├── stores/
│   └── order-store.ts           // State management
├── composables/
│   └── use-order-manager.ts     // UI integration
└── utils/
    └── order-validators.ts      // Pure functions
```

### 4. Missing Abstraction Layers

**Problem**: Components directly importing and using multiple services
```svelte
<!-- Current: Components doing too much -->
<script>
  import { api } from '$lib/services/api';
  import { supabase } from '$lib/services/supabase';
  import { stripe } from '$lib/services/stripe';
  
  // Component handles multiple external services directly
</script>
```

**Recommended**: Service layer abstraction
```typescript
// src/lib/domains/orders/services/order-service.ts
export class OrderService {
  constructor(
    private api: ApiClient,
    private payment: PaymentService,
    private database: DatabaseService
  ) {}

  async createOrder(params: CreateOrderParams): Promise<Order> {
    // Centralized business logic
  }
}
```

## File Organization Problems

### 1. Mixed Responsibilities in Single Files

**Example**: `src/components/BatteryStationMap.svelte` (405 lines)
- Map rendering and interaction
- Station data management
- User location tracking
- Order state management
- UI state handling

**Solution**: Extract responsibilities
```
src/components/map/
├── BatteryStationMap.svelte      // Main component (UI only)
├── MapControls.svelte            // Map interaction controls
└── StationMarkers.svelte         // Station rendering logic

src/lib/composables/
├── use-map-manager.ts            // Map state and interactions
├── use-station-manager.ts        // Station data management
└── use-location-tracker.ts       // Location services
```

### 2. Utility Function Organization

**Problem**: Utils scattered without clear purpose
- `src/lib/utils/pricing.ts` - Pricing calculations
- `src/lib/utils/payments.ts` - Payment utilities
- `src/lib/utils/orders.ts` - Order manipulation
- Pricing logic also in components and API routes

**Solution**: Domain-based utility organization
```
src/lib/domains/
├── payments/
│   ├── utils/
│   │   ├── pricing-calculator.ts
│   │   ├── payment-validator.ts
│   │   └── currency-formatter.ts
│   └── services/payment-service.ts
└── orders/
    ├── utils/
    │   ├── order-validator.ts
    │   └── status-calculator.ts
    └── services/order-service.ts
```

## Architecture Recommendations

### 1. Adopt Domain-Driven Design (DDD)

Organize code by business domain rather than technical layers:

```
src/lib/domains/
├── auth/
│   ├── components/
│   ├── services/
│   ├── stores/
│   ├── types/
│   └── utils/
├── orders/
│   ├── components/
│   ├── services/
│   ├── stores/
│   ├── types/
│   └── utils/
├── payments/
├── stations/
└── shared/
    ├── components/ui/
    ├── services/
    └── utils/
```

### 2. Implement Dependency Injection

Create a service container for better testability and loose coupling:

```typescript
// src/lib/container.ts
export class ServiceContainer {
  private services = new Map();

  register<T>(key: string, factory: () => T): void {
    this.services.set(key, factory);
  }

  get<T>(key: string): T {
    const factory = this.services.get(key);
    if (!factory) throw new Error(`Service ${key} not found`);
    return factory();
  }
}

// Usage in components
import { getService } from '$lib/container';
const orderService = getService<OrderService>('orderService');
```

### 3. Standardize Error Boundaries

Create consistent error handling patterns:

```typescript
// src/lib/error-handling/
├── error-boundary.svelte         // UI error boundary component
├── error-types.ts               // Domain-specific error types
└── error-handler.ts             // Global error handling utility
```

## Migration Strategy

### Phase 1: Naming Consistency (Low Risk)
1. Rename files to follow kebab-case convention
2. Update imports and references
3. Update documentation

### Phase 2: Extract Large Components (Medium Risk)
1. Start with `BatteryStationMap.svelte`
2. Extract composables first (use-map-manager, etc.)
3. Split component into smaller, focused components
4. Test thoroughly after each extraction

### Phase 3: Implement Domain Organization (High Impact)
1. Create domain directories
2. Move related files into domains
3. Update import paths
4. Implement service layer abstraction

### Phase 4: API Route Restructuring (Medium Risk)
1. Plan new route structure
2. Create new routes alongside existing ones
3. Update client code to use new routes
4. Remove old routes after migration

## Benefits of Restructuring

1. **Improved Maintainability**: Clear separation of concerns
2. **Better Testability**: Isolated business logic
3. **Enhanced Developer Experience**: Intuitive file organization
4. **Reduced Coupling**: Service layer abstraction
5. **Scalable Architecture**: Domain-based organization supports growth