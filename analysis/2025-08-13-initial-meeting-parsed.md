# Réunion

Laurent: <PERSON><PERSON><PERSON>!
Christophe: Ça va bien?
Laurent: Ça te sent bien, toi?
Christophe: Je ne t'entends pas. Bon. Allô? Bonjour!
Gabriel: Bonjour! Désolé de mes deux petites minutes de retard.
<PERSON>: Je ne t'entends pas.
<PERSON>: <PERSON><PERSON><PERSON>, nous on t'entend.
<PERSON>: Oh yes!
Laurent: <PERSON><PERSON>!
Gabriel: <PERSON>ô?
Christophe: <PERSON><PERSON><PERSON>, c'est moi.
Gabriel: Salut!
Christophe: C<PERSON>était moi, c'est mon écouteur qui était fermé.
Gabriel: Ah, parfait. <PERSON><PERSON><PERSON> <PERSON>, enchanté.
Christophe: <PERSON>ô? Vous m'entendez?
Gabriel: <PERSON>ui.
<PERSON>: <PERSON>, ok, c'est ça, mes écouteurs étaient fermés, je sais pas pourquoi.
<PERSON>: Pas de problème. <PERSON><PERSON><PERSON>, enchan<PERSON>. <PERSON><PERSON>, je vois que je vois qu'on a un circle back avec nous. J'imagine que tu étais à l'aise avec ça. Je ne sais pas si c'est toi <PERSON>, je pense qu'il l'a invité.
Laurent: <PERSON><PERSON>, c'est moi qui l'avais invité, mais.
<PERSON>: On peut le suivre, il n'y a pas de problème. <PERSON><PERSON>, c'est ça. Exactement. En fait, donc, est-ce que est-ce que Christophe, tu es à l'aise? Est-ce qu'on est un robot avec nous, en fait? Les informations restent à l'interne, tu vas recevoir un courriel en fait avec le résumé.
Christophe: Je sais, j'en ai eu moi aussi et il faut que j'aille le renouveler justement.
Gabriel: Parfait, c'est un outil bien pratique. Super, donc on va garder Circleback avec nous dans l'appel. Bref, enchanté. C'est un plaisir. J'étais très occupé juste avant l'appel. Je viens juste de faire passer des tests. J'ai envoyé tout ça à une autre équipe. J'ai pas eu le temps d'aller. Je suis un peu plus préparé que ça avant les appels. Malheureusement, j'ai été un peu plus occupé aujourd'hui. Bref, je vais quand même me présenter rapidement. Gabriel Labreton, programmeur analyste chez TLM, ça fait une bonne dizaine d'années. Passionné d'informatique, je fais plein plein de choses dans la vie.
Gabriel: Entre autres, beaucoup de développement, mais j'accompagne les entrepreneurs autant côté logiciel, côté technique, mais même des fois un peu corporatif aussi parce que j'ai mon entreprise de jeux vidéo. Où est-ce que je fais? Je fais des jeux, c'est très compliqué, très technique, puis j'ai en fait participé dans toutes les étapes au complet de la création de l'entreprise, ça fait que c'était bien compliqué. Puis sinon, en fait, j'ai de l'expertise en jeu vidéo, puis j'ai fait aussi de l'enseignement programmant, donc programmation avec des moteurs de jeu à l'université.
Gabriel: Donc, j'ai enseigné quelques années, environ quatre ans, à des cohortes d'une cinquantaine d'étudiants. Puis c'était vraiment une belle expérience. Puis finalement, j'ai tout arrêté ça parce qu'à un moment donné, avoir trois jobs, c'est pas pertinent non plus.
Christophe: Et t'es épuisé à la fin de la journée.
Gabriel: C'est ça exactement. Finalement, je me concentre un peu plus sur, entre autres, l'accompagnement et mon entreprise. Puis sinon, quand je dis accompagnement, je veux dire je fais du développement, je me mets les mains dans le code. Et sinon, je fais beaucoup d'analyse de projets et de cas de choses comme ça. Puis j'ai une spécialisation en fait le côté automatisation, exécution de tests, mise en place d'infrastructures, d'architecture. Donc bref, souvent on me qualifie de pilier technologique chez TLM. J'en ai vu beaucoup et je vais continuer d'en voir beaucoup.
Christophe: C'est bon ça.
Gabriel: J'ai qu'il faut. Voilà, donc c'est moi. Bref, Christophe, qui êtes-vous? Est-ce que je peux tutoyer?
Christophe: Oui, tu peux tutoyer. Écoute, n'hésite même pas. En gros, je suis un entrepreneur. Puis là, j'ai ma propre entreprise. Puis ça fait à peu près un an et quelques mois que je me suis lancé à temps plein là-dedans. Et je peux te dire, c'est tout un défi. Mais mon plus gros problème, c'est que je ne suis pas un gars technique. Alors, généralement, j'ai besoin de le monde de l'autre côté pour m'aider à ça.
Gabriel: Je comprends très bien.
Christophe: Puis c'est ça. Donc, en gros, moi, ce que je fais, mon entreprise, c'est on élimine l'anxiété reliée aux batteries de téléphone. Alors, tu sais, quand la barre rouge apparaît, il y a beaucoup de monde qui a un trigger qui embarque. Alors nous, ce qu'on fait, c'est qu'à travers des partenaires commerciaux, on installe des stations avec des batteries portatives, avec tous les fils intégrés dedans. Ce qui permet aux visiteurs ou aux clients de l'établissement de prendre une batterie, continuer à faire leurs affaires en liberté de mouvement et quand ils ont fini, nous la ramener.
Christophe: Alors tout dépendant, c'est quoi l'entendre? C'est soit qu'ils payent ou soit c'est l'établissement qui le paye.
Gabriel: Ah, c'est cool.
Christophe: OK. Il y a un dépôt qui est pris pour justement s'assurer que la batterie revienne.
Gabriel: OK, excellent. Alors, j'imagine que ça, c'est distribué déjà dans plusieurs endroits. On commence.
Christophe: Parce que là, j'ai eu une première génération, puis après, je me suis un peu pété la gueule, puis j'ai réalisé c'est quoi les problèmes. Alors, à partir de là, j'ai fait La plupart du monde me disait, ils mettaient mes machines dans le fond, perdu dans du restaurant ou du bar, dans un coin sombre, personne ne savait c'était quoi, puis blablabla. Puis là, j'étais comme ok, mais là, moi, je veux être dans l'entrée. Là, il fait comme, donne-moi une raison d'être dans l'entrée.
Christophe: Là, j'étais comme, ok, je vais te donner une raison de me mettre dans l'entrée. Et qu'est-ce que tu as besoin? Trouve-moi des choses intéressantes pour moi, parce que c'est intéressant pour le service, mais je veux dire, il m'en manque. À partir de là, j'ai fait intégrer des écrans dans les stations pour du advertising revenue. Là, j'étais comme, ce n'est pas assez intéressant. Qu'est-ce qui est quelque chose de très dur à avoir et qu'on ne peut pas mettre la main facilement dessus? C'est du data présentiel.
Christophe: Donc, à travers des caméras et des programmes externes, je peux analyser tous ceux qui passent devant la station. Donc, je peux savoir c'est quoi le foot trafficking avec l'analytique démographique présentielle.
Gabriel: Oh, intéressant. Ben oui, vraiment.
Christophe: OK.
Gabriel: Puis, est-ce qu'il y a des enjeux de confidentialité ou de... parce que là, il y a des cumuls de stats, des choses comme ça. J'imagine qu'on a le droit de compter le nombre de personnes qui passent à un endroit.
Christophe: Ah non, moi, j'ai vraiment plus soin que ça. Mais tout ça est fait de façon à protéger l'anonymité du monde.
Gabriel: OK.
Christophe: Ce qui fait que je n'ai pas accès au stream de vidéo, ni quand la personne est devant la caméra, sa face est bleuée. Donc... Je ne sais pas qui c'est. Mais à partir de là, il y a aussi le data qui est exporté. À partir de là, on peut avoir les stats d'interaction qui passent. C'est quoi la fréquence?
Gabriel: Intéressant. Puis à ce moment-là, j'imagine qu'il y a plein de défis à la fois humains et techniques de mise en marché de tout ça.
Christophe: Ah oui. J'en vois déjà plein.
Laurent: On peut les imaginer un peu.
Gabriel: Oui, c'est ça. On dirait que je n'ai pas de misère. Ça n'a pas l'air facile.
Christophe: C'est ça. Donc, mon gros problème, c'est que j'utilise beaucoup de tierces parties. Alors, quand j'ai des grandes stations ou des stations avec des écrans, J'ai le programme des batteries qui veut prendre le dessus. Puis là, il y a le programme des écrans qui veut aussi prendre le dessus. Alors, des fois, il bug. OK. Puis c'est là, ce qui est le fun, c'est que tout peut être mis sur des API, le programme d'analytique, le programme des écrans, le programme des batteries. C'est tout fait par un tierce parti, les trois.
Gabriel: OK. Quand tu mentionnes le tierce parti, est-ce que c'est un service existant ou un truc sur mesure? Les deux. Genre, ok. Fait que, mettons, les analytiques, je suppose que tu utilises un service existant.
Christophe: C'est ça. Eux, ils sont plus dans le retail, dans le fond.
Gabriel: Ok.
Christophe: Eux, ils vont être dans des commerçants retail pour pousser des brands comme, tu sais, on va dire tu achètes au Pharmaprix un aller de brand, ils vont mettre des écrans au dessus. Puis, à partir des interactions avec l'écran, ils vont mettre des publicités basées sur le brand qui est en dessous et les produits de la personne devant toi.
Gabriel: OK, OK.
Christophe: L'Oréal pourrait avoir ça, puis ils pourraient analyser, c'est une femme avec les cheveux rouges, alors ils pourront montrer ce produit-là.
Gabriel: OK. Mais là, toi, c'est sur ton use case, c'est pas ça, mais bref.
Christophe: Je rentre des programmes dans des places où est-ce qu'ils ne devraient pas rentrer, puis je les mets à des places où est-ce qu'ils ne devraient pas être. Même eux autres font comme, qu'est-ce que tu fais avec nos systèmes? C'est bien intense, ton affaire.
Gabriel: Ils sont découragés.
Christophe: Ils font comme, comment tu arrives à faire ça, toi là?
Gabriel: Je comprends. C'est de la débrouillardise à un moment donné. Quand le monde ne fait pas ce qu'il faut qu'il fasse, on le pauvre et on le change. Ça fait que j'aime ça.
Christophe: Bingo. Ils me permettent d'avoir de l'analyse parce qu'ils ont déjà leur compliance pour l'Européen, Amérique du Sud. Oui, c'est ça. C'est comme je peux piggy-hop sur leur par le compliance sans devoir le faire moi-même.
Gabriel: Oui, exact. Ça, c'est extrêmement pertinent parce que, justement, le faire soi-même, c'est long, c'est coûteux et c'est compliqué, mais ça se fait. Puis, il peut y avoir un intérêt à le faire aussi.
Christophe: En fait, il y a une entreprise québécoise qui est en train de le faire, mais elle n'est pas rendue là. Alors, c'est comme, il y a peut-être, j'ai peut-être l'option de pouvoir juste lui couper l'herbe en dessous du pied puis de l'acheter avant qu'elle ait sa compliance pour pouvoir, comme, l'intégrer dans mes systèmes, puis c'est québécois. Pendant que j'ai déjà les clients pour ça, tu comprends?
Gabriel: Oui, oui. OK. Puis mettons les clients présentement, tu l'as mentionné un peu tantôt, mais le genre de marché ou le genre de clients que tu vas avoir en général, tu t'as mentionné des restaurants, t'as mentionné...
Christophe: C'est ça, j'ai quatre types de clients. Permanent, où est-ce que c'est le end-user qui va payer. Permanent, où est-ce que c'est la business qui paye. Temporal, où est-ce que c'est le end-user qui paye. Temporal, où est-ce que c'est la business qui paye. Chacun, temporal, où est-ce que c'est le end-user, ça va être les restaurants, les bars, où est-ce que la marge de manœuvre est très courte, très petite. Puis, c'est pour ça que l'end-user va payer généralement. Où est-ce que la business va payer permanent?
Christophe: Ça va être des endroits comme coworking, des gyms, des endroits où est-ce que tu dois payer ou que peu de monde a accès facilement. Il faut que tu aies des autorisations pour rentrer là. Alors là, ça devient un service que l'établissement ou la business offre à ses membres de plus.
Gabriel: Ok, est-ce que tu as visé par exemple aussi les hôpitaux?
Christophe: Bien, justement, c'est un des contrats que je suis en train de finaliser là.
Gabriel: Ok, ok, intéressant.
Christophe: Quand je te dis finaliser, le contrat est signé. Puis j'attends qu'ils me donnent, genre, le visuel à imprimer sur mes machines, là, t'sais. Je rentre du lot, là.
Gabriel: Nice, parce que t'sais, c'est un gros marché. Ben oui, t'sais, je regarde qu'est-ce que ça fait, puis clairement, ta cible, c'est certainement les hôpitaux, c'est certainement là où il y a beaucoup de gens, puis qu'eux, ils peuvent ouvrir les paies, là, du financement pour avoir la machine en tant que telle, puis que ça fonctionne.
Christophe: Bien, puis que les gens ne payent pas, t'sais. La façon qu'eux, ils marchent, c'est qu'ils me laissent l'espace, puis c'est le end-user qui paye. J'ai pas le droit de mettre d'écran, j'ai pas le droit de mettre de caméra.
Gabriel: Ouais, c'est ça, c'est ça. C'est exactement là que j'allais. J'étais comme, mais là, tes analytiques, c'est hyper, t'sais. Puis, est-ce que ça, c'est un enjeu en tant que tel ou toi, t'es, non, c'est ça.
Christophe: Non, non, j'ai des machines de base que c'est juste un écran lumineux avec des consignes et des batteries en dessous. Puis, ça, c'est l'ancien. En fait, l'ancien modèle, on dirait un toaster avec dix batteries. C'est eux qu'on le fait en projet pilote. Mais j'ai déjà, ça fait tellement longtemps que je les attends que j'ai déjà le deuxième génération de 4. Il y a un système de POS dessus, puis c'est comme, comparé à l'autre, puis c'est des blocs qu'on peut rajouter.
Christophe: Alors, tu sais, comme je vois qu'il en manque tout le temps, je rajoute un bloc de plus, de 4. Alors, c'est comme, je peux gager le nombre de batteries que je peux avoir dans chacun de ces établissements-là.
Gabriel: Ok, ok, intéressant. Puis, je suppose que tu as déjà toute l'espèce de mécanisme en place, de, mettons, stratégique au business, de genre quand tu as des batteries qui brisent, ils peuvent te renvoyer des choses ou comment c'est? As-tu des plans de comment ça marche quand justement ta machine est en place et tu as une batterie qui ne fonctionne plus? Je suppose qu'il y a des procédures déjà d'établir.
Christophe: Je ne sais pas. Je vais être honnête avec toi. Quand ça va arriver, ça va arriver.
Gabriel: Ok.
Christophe: Ce n'est pas encore arrivé. Généralement, la pire chose qui m'arrive, c'est que quelqu'un part avec la batterie et je garde le dépôt.
Gabriel: OK. C'est bien. C'est plus simple. À ce moment-là aussi, c'est peut-être plus scalable de cette manière-là, j'imagine.
Christophe: C'est là où est-ce que, comme j'ai dit, parce que j'étais en dilemme, est-ce que j'engage quelqu'un pour telle affaire ou J'engage l'entreprise pour pouvoir bâtir le projet plus... Parce que là, comme je dis, je suis en MVP. Là, ça marche, le monde sont intéressés, puis on avance. Là, il faut quelque chose de plus stable, que j'ai un meilleur contrôle, que les programmes ne se battent pas entre eux autres.
Laurent: C'est un bon point.
Gabriel: Oui, c'est ça. De ce que je comprends, ça, clairement, nous autres, c'est une affaire sur laquelle on peut facilement intervenir, j'ai l'impression, côté technique.
Christophe: Alors, là, tu comprends que j'ai, dans le fond, si tu veux que je te le montre, que je te montre un peu mes produits, comme ça, tu vas pouvoir.
Gabriel: Oui, oui, complètement. Ça va me donner une bonne idée de la compréhension. Parce que tu sais, évidemment, l'objectif de cet appel-là, c'est de voir comment, entre autres, TLM peut t'aider et où est-ce qu'on peut t'amener avec ça.
Christophe: Je sais exactement comment tu peux m'aider et je sais exactement ce qu'il faut qu'il soit fait, mais c'est très clair dans ma tête. C'est pas une question. Mais c'est parce qu'à un moment donné, j'ai des limites. Moi, je ne suis pas un gars technique. Puis là, c'est rendu que je commence parce que j'ai engagé un technicien qui m'a bâti un portail pour justement... Parce qu'à la base, j'importais ça des Chinois. Alors, les Chinois, ils faisaient juste des batteries et tout. Là, j'étais comme, je ne veux pas que...
Christophe: un enjeu de confidentialité et le monde n'aime pas trop la technologie chinoise, alors on va en faire des API comme ça je vais pouvoir enlever un peu plus cette contrainte-là. Excuse-moi, mon ordinateur est rush depuis un certain temps et je ne comprends pas pourquoi.
Gabriel: Ça va, ça va. En fait, ça doit être Teams.
Christophe: Non, non, non. C'est vraiment l'ordinateur qui commence à être overcharged. J'ai des moments donnés que mon CPU était à 100% et je suis comme « Why?
Gabriel: » Il ne se passe rien.
Christophe: Je vais t'avouer que je suis très... J'ouvre beaucoup de pages internet, puis les...
Gabriel: Les anglais cool.
Christophe: Ouais, genre comme... J'en ai facilement comme une quarantaine, genre, d'ouverts, là. Tu sais, je sais, je devrais pas faire ça. Pas besoin de me le dire.
Gabriel: On le fait tout, toi.
Laurent: On le fait tout, on le fait tout.
Christophe: C'est ça, parce qu'à un moment donné, j'ouvre ça, puis là, je commence, je vais aller revenir, puis là, ça fait trois semaines que j'y ai pas revenu. Excuse-moi. J'ai besoin de chaque pas, là, mais là, là.
Gabriel: Ah, chope-toi. On est déjà rendu là, j'aime ça.
Christophe: ST, d'ordi. Je fais aussi des demandes de SE. Je fais des applications pour des appels d'offres en même temps.
Gabriel: Ben oui, c'est sûr.
Christophe: Pourquoi ça bug?
Gabriel: Est-ce que, dans le fond, là, ce que tu m'as expliqué, c'est que tu avais un bon, un technicien qui a développé un dashboard.
Christophe: La plateforme, c'est ça.
Gabriel: La plateforme en tant que telle. Cette plateforme-là, c'est essentiellement, toi, un espèce de truc de contrôle pour savoir.
Christophe: C'est deux, parce que quand je gérais l'application, parce que les Chinois m'ont donné genre le back-end pour que je puisse faire des gestions, puis je regardais ça, puis j'étais comme, À chaque deux minutes, il y avait quelque chose qui ne marchait pas. C'était vraiment... Il y avait tellement de choses. Il y avait tellement, tellement de choses. Juste pour te le dire, c'est... J'en pleurais, presque. Il y avait des journées où j'étais comme... Pourquoi je fais ça? Je suis tanné. Je suis tanné de gérer des imbéciles. OK, parfait. Ça a ouvert.
Gabriel: On voit l'équilibre, en tout cas, c'est en train de loader. Oh yes, ça marche.
Christophe: C'est bon. Donc, comme tu peux voir, ça, c'est le petit modèle que je vais mettre dans les restaurants et les bars. Après ça, c'est comme ceux-là, c'est ceux que je vais tester dans les hôpitaux. Puis, dès que je vais avoir une occasion, je vais les sortir et je vais mettre le nouveau modèle. Mais je veux dire, j'ai des anciens modèles qui traînent. On va les utiliser, on va les monétiser.
Gabriel: OK, c'est ça que tu disais, pas d'écran, tu as les instructions sur le devant.
Christophe: C'est la même affaire ici aussi, je peux avoir un écran ou je peux en mettre juste comme, je peux en mettre un. C'est un vrai, je peux en mettre un écran ou je peux mettre un panneau lumineux, c'est ça.
Gabriel: Ok, ok. Puis ça, ce boîtier-là, dans le fond, versus le nouveau, c'est des trucs qui étaient déjà existants, que t'as re-bundle puis re-brand, genre, ou c'est un modèle qui.
Christophe: Était basé sur le même? Ces modèles-là, ouais. Comme la coquille existe, mais le hardware à l'intérieur a été modifié pour pouvoir prendre les programmes que je mets.
Gabriel: Ouais, c'est ça, ok, je comprends. Ça se trouve avec quoi, entre autres, qu'est-ce qu'il y a à l'intérieur, par curiosité? Je pense qu'il y a un enjeu technique là-dedans. C'est quoi? C'est comme un pie?
Christophe: C'est un quoi?
Gabriel: Un Raspberry Pi ou un micro-ordinateur?
Christophe: Écoute, j'ai un motherboard. C'est ça, j'ai des caméras, puis tout est connecté dessus, genre.
Gabriel: OK, OK. Mais le circuit imprimé, je veux dire, le fournisseur de ça, tu disais que c'était une technologie chinoise.
Christophe: Ça vient d'eux autres, je ne sais pas c'est laquelle. Je peux te le sortir, le modèle, ils me l'ont déjà sorti.
Gabriel: Ah, ok, parfait. Ça risque d'être utile pour savoir à quel point on est capable de... J'imagine qu'on a une modification à faire quelconque.
Christophe: Oui, bien, en fait, moi, je suis incubé aussi. Tu sais c'est quoi un incubateur?
Gabriel: Oui, absolument.
Christophe: Parfait. Je suis incubé au garage Inco pour justement travailler le côté hardware de la machine. Parce que là, ce que tu vois, c'est les petits modèles. Puis là, après, tu as le gros modèle qui est en festival.
Gabriel: Ok, ok.
Christophe: Puis ce modèle-là, ben t'sais, j'ai des caméras pour faire des statistiques d'achalandage, sauf que c'est pas assez fort, ça va pas assez loin, la qualité de l'image est pas assez bonne. J'ai fait « Garage Inco, trouve-moi de quoi », parce que je t'avais tiré la balle sur quelqu'un. Je t'avais de pogner les nerfs, puis je suis comme « reverse engineer-moi ça pour que je puisse enlever le contrôle chinois parce que c'est de la merde ». Et c'est vraiment de la marge, je suis tanné de gérer ça.
Gabriel: J'imagine que pour lui d'événement grand public, t'es capable d'avoir un ordinateur plus sophistiqué à l'intérieur. Ça a l'air d'être assez gros, fait que t'as sûrement accès à un système d'exploitation ou un truc comme ça, j'imagine.
Christophe: Pas là, parce qu'ils sont au cheap, alors ils ont mis la même affaire que dans le petit.
Gabriel: Ok, je comprends. Puis là, c'est ça que tu as de l'aide de l'incubateur qui va peut-être t'amener une bonne place pour ça.
Christophe: En fait, il est en train de reverse engineer pour qu'on puisse augmenter chacune des pièces pour qu'ils soient compatibles à être à l'extérieur, cas de saison, et faire du moins 35 degrés, puis du plus 60 degrés.
Gabriel: Pour pouvoir... Ben oui, c'est ça, parce que même dans les festivals, tu vas avoir les intempéries avec ça, mais tu veux pas avoir tout le temps quelqu'un à côté.
Christophe: Ok, ok. Puis c'est ça. Donc, tu sais, puis même le côté batterie, moi, j'ai trouvé quelqu'un qui a développé une nouvelle, un nouvel type de batterie. Présentement, la plupart, toutes les batteries au monde ont des traces de lithium dedans. Moi, j'ai trouvé quelqu'un qui a tout éliminé ça, puis c'est des batteries à base de carbone.
Gabriel: Ok.
Christophe: Puis, à quel point que si ça va bien, moi et lui, on est en train d'aller visiter une usine pour commencer à tester, passer de, c'est à marche en laboratoire, je suis capable d'avoir mes premières batteries, mais que je fais à la main, à je m'en vais, je l'industrialise et on commence à vendre ça. Alors, moi, c'est le côté énergétique.
Gabriel: Ben oui.
Christophe: C'est ça. Alors t'sais, moi j'ai trois services. Batterie, énergie, écran publicitaire, data collecting.
Gabriel: OK. OK. Effectivement, parce qu'après ça, tu peux venir afficher des affaires pendant que le monde est en avance sur ta machine là.
Christophe: Bingo. Puis en plus, je peux voir l'interaction qu'ils ont avec l'écran. Donc, je peux faire du A-B testing genre, mais en présentiel.
Gabriel: Ça, c'est hot ça. Ok, ok.
Christophe: C'est une autre paire de manches, hein?
Gabriel: Ouais, ouais, je ne m'attendais pas à ça, je trouve ça cool.
Christophe: Oh ouais, écoute, quand je te dis que je suis très ingénieux et que je fais des affaires que je ne devrais pas faire, c'est exactement ce que je veux dire.
Gabriel: Là, je comprends mieux ta phrase de tantôt. Ah, c'est bon ça, c'est bon.
Christophe: Bien, c'est comme, puis avec les datas, je peux collecter les, je peux collecter comme le présentiel et tout. Ben oui, c'est ça, c'est... Donc, Présentement, j'ai une plateforme. Elle est faite sur... Attends, on va arrêter ça. Ma plateforme est faite sur... GitHub. Elle est écrite... Hein?
Gabriel: Oui, oui, on ne voit pas ton écran pour ça.
Christophe: Oui, je sais, je l'ai... Est-ce que tu voulais le voir? Ça ne me dérange pas.
Gabriel: Ben, ouais, sur GitHub, décris-moi le projet, la tech, effectivement, si jamais nous autres, on finit par avoir accès, là, OK. Fait que t'as un dossier qui est là-dedans.
Christophe: Ouais, ben en fait, le technicien a fini par tout me transférer ça, pis ça a été un esti-chial de marde.
Gabriel: Ah ouais, OK.
Christophe: Ben oui, parce qu'il l'avait mis à son nom personnel.
Gabriel: Ah ouais, ouais, je comprends.
Christophe: Pis, mais tu sais, transférer le code d'une personne à l'autre dans GitHub, c'est pas aussi facile que je pense.
Gabriel: Ouais, ouais, ouais.
Christophe: Alors, tu vois le langage, j'ai deux types de langage. Celui-là.
Gabriel: Svel, TypeScript, c'est bon. PL, PGSQL.
Christophe: En gros, la plateforme gère tout ce qui est location de batterie. OK. Donc, ça va super bien.
Gabriel: Je vois déjà les différentes technologies, SupaBase, Tailwind, etc. J'imagine qu'ils utilisent un peu des outils de vibe coding pour générer une partie de ces affaires-là. C'est au goût du goût.
Christophe: En fait, ça a été fait il y a à peu près deux ans, la plateforme. Donc, il n'y a peut-être pas de vibe coding.
Gabriel: Mais moi, j'ai essayé de faire des.
Christophe: Corrections et comme tu peux voir, ça a brisé.
Gabriel: C'est rouge.
Christophe: Ouais, mais c'est parce que je ne suis pas un codeur. Puis là, j'étais comme, ah, il voulait modifier du texte. Alors, je suis comme, ah, bien, je vais essayer de modifier mon texte. À un moment donné, ça a brisé.
Gabriel: Effectivement, mais tu sais, en même temps, parce qu'en fait, on est rendu quand même un peu là avec l'intelligence artificielle, où est-ce que techniquement, s'il y a des choses, on peut vraiment s'assurer que quand tu fais des modifications, tu serais en mesure de le faire sans qu'il y ait rien qui casse.
Gabriel: En fait, chez TLM, on commence beaucoup à faire ça avec des entrepreneurs aussi, où est-ce que, non seulement nous, on s'occupe des trucs un peu plus complexes, les pipelines de distribution, ces affaires-là, ou les trucs qui ont plus de, disons, de complexité, mais qu'on expose des manières à ce que les entrepreneurs, même non-tech, soient en mesure d'effectuer des modifications, parce que l'objectif c'est que tu sois en contrôle de ce qu'il se passe, et c'est à la taille que ça peut avoir, surtout si c'est juste le board qui sert de faire les locations.
Gabriel: Tout d'un coup, tu veux avoir une manière de remplacer un visuel, il faut que ce soit facile pour toi de le faire. À quel point tu as besoin d'un développeur à chaque fois que tu veux juste changer l'image? Pourquoi ne pas réussir à mettre ces affaires-là en place aussi? En fait, moi, ce qui m'intéresse beaucoup pour aujourd'hui, j'ai vu rapidement les techno, j'ai vu que c'est du tailwind, il y a un super bass derrière. Ce sont des affaires avec lesquelles on est super à l'aise chez TLM. Ce n'est vraiment pas un enjeu.
Gabriel: TypeScript, heureusement que c'est en TypeScript. Si ça avait été en JavaScript, j'aurais fait, bon, première étape, on va le convertir en TypeScript. Mais là, c'est le fun, on n'a pas besoin de le faire.
Christophe: Avant que j'oublie quelque chose, il paraît que je... Je texte l'autre qui n'a pas signé les documents qu'on signe.
Gabriel: Puis oui, effectivement, donc le code en ce moment, il t'appartient à 100% ou non? C'est vraiment.
Christophe: Oui, OK. Ce code là, il m'appartient, mais je veux dire, il y a beaucoup de pays qui t'envoient chercher avec les Chinois. Moi, mon but ultime, c'est de me débarrasser du code base de Chinois et que je rapatrie ça chez moi à 100%. Ça, c'est un des affaires. Le deuxième, c'est avoir la plateforme que j'utilise de mon fournisseur. Montrer un peu plus l'autre là. Oui. Parce que là, j'ai... Attends une minute, je me logue. Puis, je te partage ça. Donc, tu vois un peu plus?
Gabriel: Oui. Eco display for AI.
Christophe: C'est ça. Donc, moi, avec ça, je peux avoir mes insights. C'est ça. Je vois les interactions, on va dire. Ça vient de passer. J'ai un événement, alors ça va plus paraître. Là, tu vois le nombre de visiteurs que j'ai « breached », le nombre de fois que j'ai eu des contacts avec, l'âge, le « gender », les expressions faciales.
Gabriel: Ça c'est basé sur les résultats de la caméra et ça c'est le service en tant que tel qu'utilise parce que dans le fond, si je comprends bien, Display Force AI, je ne connaissais pas, mais Marketing Automation Platform, ça c'est américain?
Christophe: C'est européen.
Gabriel: C'est européen, c'est celle-là que tu disais tout à l'heure. Déjà les compliance, GDPTR, CCPA compliant aussi.
Christophe: Mais en gros, ce que j'aime, parce qu'il y a beaucoup d'API avec eux autres, alors ce qui serait le fun, c'est d'avoir, de tout intégrer comme toutes mes affaires sur une plateforme, puis que les clients puissent aller sur la plateforme, puis je peux les séquencer différents.
Gabriel: Je ne suis pas certain, non. En fait, ce que tu veux dire, c'est de pouvoir les traquer tout au long de l'utilisation en sachant que c'est dans les personnes?
Christophe: Un festival va pouvoir voir les statistiques de son événement, genre un dashboard, puis il va pouvoir voir les interactions, puis il va pouvoir mettre ses propres vidéos, genre, puis s'occuper de toute la gestion de ça sans que j'ai besoin d'interagir avec.
Gabriel: Ok, je comprends. Donc autrement dit, comment transformer ça un peu en, bref, fournisseurs de batteries as a service ou est-ce qu'ils sont self-serve? C'est-à-dire que même tes clients sont self-serve.
Christophe: Oui, c'est ça. En fait, le mieux que je voudrais, c'est comme, que j'ai juste besoin de transport puis l'implantation. Tu comprends?
Gabriel: Oui, oui, c'est ça. Puis après ça, tu pourrais leur offrir un service où est-ce qu'ils peuvent aller configurer les panneaux puis leur expliquer clairement que s'ils perdent leurs accès ou s'ils se font voler leurs accès, n'importe qui peut commencer à jouer avec leurs panneaux.
Christophe: Et c'est ça.
Gabriel: Ça responsabilise les utilisateurs en même temps, il faut vraiment que le risque soit bien mentionné parce que t'as du monde un peu cave.
Christophe: Plus souvent que je pense.
Gabriel: Oui, c'est ça, exact. On s'entend là-dessus. À ce moment-là, ok. Là, ce que tu mentionnes entre autres, c'est qu'en fait, Display4CI, eux, exposent des API. L'idée entre autres de ce qui était développé, c'est comment prendre capturer les informations qu'eux ont techniquement capturées, accumulées, puis réexposer ça, divisé par clients pour tes clients finalement.
Christophe: C'est ça. Puis en même temps, c'est comme, tu sais, j'ai le côté advertising que si jamais j'ai des clients qui veulent commencer à faire du advertising, ils peuvent aller sur ma plateforme puis envoyer comme des soumissions à tous les écrans. Comme ça, le commerçant de l'établissement peut autoriser genre comme certains types de publicités supplémentaires. Comme ça, on augmente nos revenus des deux bords.
Gabriel: Ok, ok, ok. Intéressant. Bref, une plateforme de gestion de publicité à Embed dans les...
Christophe: J'avais vu, il y en a un, c'est un co-pédisseur par rapport aux écrans, qui s'appelle Orange Ad. C'est pas mal, genre, si jamais tu veux aller voir. C'est pas mal ce que j'avais l'intention d'avoir comme plateforme pour ceux qui veulent aller s'installer et avoir des écrans. Tu comprends?
Gabriel: Ok.
Christophe: C'est ça. Donc, ça, c'est pour... Je veux pouvoir aussi que le commerçant puisse, on va dire qu'il y a de quoi il puisse devoir une ou deux batteries, genre. Il puisse faire un petit peu de la gestion des écrans. Si il veut changer des visuels, il va pouvoir le faire tout seul. Il va pouvoir avoir accès à des statistiques, mais par rapport à ce qu'il paye. Ça veut dire qu'il y en a qui vont payer pour l'année. Tu vas avoir les stats de l'année.
Gabriel: Ok.
Christophe: Alors, si tu me dis que tu veux des stats au trois mois, tu vas avoir des stats des trois mois.
Gabriel: Ça, il faudrait voir, évidemment, à quel point, comment on est capable de sortir le data, finalement, de Display Force AI.
Christophe: Le Display Force peut sortir en Excel.
Gabriel: Ok, ok. Donc à ce moment-là, nous autres, on peut facilement réagir à ça.
Christophe: C'est très exportable, c'est ça.
Gabriel: Ok, parfait. Bien, c'est ça. Puis sinon, c'est en fonction de leur API, t'sais, est-ce qu'ils ont des limites d'appel et tout ça, mais c'est sûrement que si tu payes pour le service, à ce moment-là, t'es censé avoir accès aux informations.
Christophe: Au pire, je paye pas, ils sont mieux de me donner ce que je veux.
Gabriel: Excellent, mais c'est quand même un point à s'assurer justement que tu es en contrôle du data parce que si les autres, justement, il faut que tu aies accès à ça si tu veux le réexposer au client. Sinon, s'il offre les API, puis est-ce qu'il te limite par exemple au nombre d'API call, des choses comme ça. Ce sera des petits détails peut-être à avoir long-term, mais ça me semble être très utile.
Christophe: Puis, il y avait aussi une autre affaire que j'aimerais savoir qu'est-ce qui est possible pour vous autres de faire. Oui. Dans le fond, des systèmes de paiement, les POS. Oui. Eux autres, ils font...
Gabriel: En particulier de pré-sélectionner déjà ou...
Christophe: J'en ai un qui sont associés à mes machines, mais éventuellement, on va vouloir changer.
Gabriel: OK.
Christophe: Parce que Stripe coûte fucking cher.
Gabriel: Oui. En même temps, l'intégration au niveau développement est très accessible.
Christophe: Comme j'ai toujours dit, si tu surveilles avec Stripe, tu surveilles avec n'importe qui.
Gabriel: C'est un bon point.
Christophe: C'est ça. Donc, pour l'instant, j'ai Stripe. Est-ce que je veux rester avec Stripe? Pas nécessairement, mais il me faut des systèmes de paiement en présentiel, premièrement. Donc ça, je suis en train de regarder, d'avoir comme customiser mon propre affaire pour que ça soit juste comme un tap to pay. Puis au-dessus, j'ai un petit écran tactile qui fait juste comme louer genre. Puis c'est comme ça vient envoyer un tap ici genre. Puis tu fais juste comme à même la machine.
Gabriel: Ok, ok, ça je pourrais m'informer personnellement, j'ai pas l'expérience, j'ai travaillé avec des POS, j'ai travaillé avec AIDEN, j'ai travaillé avec des, mais j'ai pas, j'ai, j'ai pas comme implémenté des trucs plus custom au niveau paiement. Fait que je sais pas si il y a des, j'imagine, est-ce que t'as fait des recherches un peu?
Christophe: Ben moi, ce que je sais, c'est que la plupart utilisent une application. Donc, cette application là, je peux juste comme la mettre sur un écran tactile. Ça finit là. Oui, oui, oui.
Gabriel: C'est exactement ce que je veux voir. Ok, ok. Fait que dans le fond, toi, le même, mettons, avec Stripe, juste le fait de, genre, le petit truc avec le tape, puis t'affiches l'écran, il est suffisant. Fait que t'as même pas besoin d'un appareil POS, techniquement. Surtout pour les grands écrans, si je comprends bien. Ça, c'est un besoin pour ton, ta plateforme. Est-ce que les plus petits, c'est aussi le genre de truc que tu voudrais finir par intégrer ou non? Parce qu'il n'y a pas d'écran, c'est compliqué.
Christophe: Non, non, non, c'est ça. Puis aussi, présentement, c'est, les POS sont fournis par les Chinois. Ça s'en va directement dans mon compte, mais je n'ai pas le contrôle là-dessus.
Gabriel: Ok, ok.
Christophe: Ça, ça me gosse.
Gabriel: Ben oui, c'est sûr, c'est sûr. Ça fait qu'à ce moment-là, si tu remplaces ça sur ton casing par juste un devesse, j'imagine qu'il y a un enjeu aussi que tu ne veux pas que les gens sauvent avec le POS, tu sais, somehow. Ça fait que s'il y a ben une embelle sur le truc, c'est plus facile.
Christophe: Ben justement, j'ai eu un problème en fin de semaine parce que je n'avais pas barré l'écran. Alors, il y a des jeunes qui ont commencé à jouer sur le système de POS, puis ont ouvert Google pour jouer à des jeux sur le POS. Puis là, j'étais là, j'étais comme... Aïe! Là, j'étais en tabarnak. Une chance que j'étais là, j'étais comme, je vais apprendre, je vais apprendre.
Gabriel: Mais oui, c'est ça, mais les gens font ça. C'est vraiment un bon apprentissage parce que justement, ce genre d'affaires-là arrive. C'est comme il faut vraiment. C'est tout le minimum que tu peux faire que tu as le plus de gains. Dans le fond, pour les mêmes au niveau du matériel que tu verses, si tu as moins d'affaires à gérer, c'est mieux. Si directement dans ta borne, ton affaire de paiement est vraiment bien intégrée et tu ne peux pas le sortir de là. Tu sais, si tu le sautes dedans, quasiment, je ne sais pas.
Gabriel: Je ne sais pas c'est quoi la meilleure stratégie.
Christophe: C'est ce qu'on va faire sûrement là. C'est comme... Mais c'est parce que là, j'ai un autre problème. S'il fait trop chaud, mon POS, il surchauffe. C'est comme un téléphone.
Gabriel: OK, ouais. Oui, mais souvent, il y en a même que c'est des téléphones.
Christophe: En fait, oui, c'est des téléphones, là, on va s'entendre. Mais c'est ça. Alors là, quand je le mets dehors, bien, il surchauffe.
Gabriel: OK.
Christophe: À 35 degrés, là, il fait chaud en tabarnak, puis les téléphones sont noirs.
Gabriel: Ouais, c'est ça, exactement.
Christophe: Il y a peut-être des couleurs.
Gabriel: À moins que tu y mettes un petit panneau par dessus, mais même à 35 degrés, t'as le problème pareil. C'est sûr que, c'est bon, évidemment, notre expertise chez TLM, c'est pas tant côté hardware, c'est un peu plus technique, mais effectivement, si on est plusieurs à y réfléchir, c'est intéressant.
Christophe: D'où le pourquoi j'ai... Ouais, l'incubateur garage. Garage, c'est ça. Mais mon problème aussi, c'était... Mais statistiques d'achalandage, les statistiques d'achalandage doivent venir avec des statistiques de provenance.
Gabriel: Donc, tu veux savoir quel bord n'a capturé quelle information?
Christophe: Non, pas du tout. Quand on dit de provenance, on parle de la personne qui est là, elle vient d'où?
Gabriel: Ah, ok, ok, ok.
Christophe: Pour les festivals.
Gabriel: Ouais, c'est ça, mais c'est un enjeu d'identification quand même.
Christophe: Oui, mais quand je te dis je suis ingénieux et créatif. Je suis très ingénieux, je suis très créatif.
Gabriel: J'écoute.
Christophe: Donc, il s'avère que n'importe qui qui utilise des cartes de crédit ou des interacts, tous ces belles petites meubles-là sont associés à des codes postales.
Gabriel: Ouais, c'est capable de capturer quand même.
Christophe: En fait, c'est là où est-ce que, où est-ce que je vais peut-être avoir besoin de vous autres, parce que là, j'aimerais savoir des programmes qui puissent extraire ça de POS, peu importe c'est lesquels.
Gabriel: OK, le peu importe c'est lesquels, je ne sais pas, mais avec STRIPE, il y a moyen d'aller sortir les informations via API.
Christophe: OK, je vais reformuler ça. Si tu ne veux pas dire que c'est mon POS. mais que la personne, elle aimerait ça peut-être avoir ses pourcentages de provenance de son commerce.
Gabriel: Oui, c'est ça. Autrement dit, capturer l'information de plus quand même, finalement, puis la réexposer dans les statistiques.
Christophe: Oui, c'est ça. Donc, si tu es capable de me dire si c'est possible, faisable, si tu es capable de me le programmer et de me l'avoir, je vais te le dire tout de suite, je suis devenu quelqu'un de très riche.
Gabriel: Nice, on aime ça. En fait, cette partie là, évidemment, il faudrait vérifier ce qui est disponible dans les API des POS. Parce que c'est sûr que tu ne veux pas commencer à faire le traitement toi-même, commencer à toi à manipuler la carte de crédit ou les informations, puis extraire les numéros, puis stocker l'information. C'est la route vers la catastrophe. C'est sûr qu'on ne peut pas aller là. Moi, évidemment, je vais aller faire quelques recherches dans mon côté, voir qu'est-ce qui sont exposés dans les différents API des POS.
Gabriel: Je vais aller voir un avec lesquels j'ai travaillé. Chez DRM, évidemment, on est plusieurs à avoir travaillé avec différents POS aussi. Je voulais lancer la question en fait en interne, voir s'il y en a qui ont. qui ont déjà eu à faire ça. Parce qu'entre autres, c'est mis en contexte chez TLM, on a aussi une billetterie.
Christophe: Oui, je le sais. On est en train de parler justement, parce qu'il y a plusieurs façons d'aller chercher des statistiques de provenance pour les festivals. Puis un des, parce que c'est une des raisons pourquoi j'ai commencé à parler avec ton patron.
Gabriel: Oui, avec Robbie Bachecourt. Avec Bachecourt, OK. De toute façon, c'est le même sang.
Christophe: C'est ça. En gros, c'est que le touriste Québec demande, si je te donne de l'argent, il faut que je chasse, que ça a été... Il y a eu un bon impact parce que le monde sont comme... Ils viennent de l'extérieur pour venir à ton festival. Alors là, il demandait de remplir des sondages. C'est le bonhomme dans les festivals qui viennent et qui vont poser des questions.
Gabriel: Ouais, ouais, ben, toi dans le fond.
Christophe: Il arrive avec un petit pad et il pose des questions. Je ne sais pas si tu faisais des festivals, mais il y en a beaucoup qui faisaient ça.
Gabriel: Ouais, ok, non, je veux dire, les derniers festivals que j'ai participé, je n'ai pas vu de monde qui posait des questions aux gens, mais j'imagine que ça arrive. Je parlais des populaires FEQ et compagnie.
Laurent: Moi, je les ai vus. Peut-être plus, c'est plus flexible. Ça dépend.
Gabriel: C'est ça, exactement. Ça dépend à l'ANAS, à ShrewdSmith. Non, c'est ça.
Christophe: Ces sondages-là sont requis pour En fait.
Gabriel: C'Est la même affaire, c'est Touristes Québec. Ils font la même affaire avec tous les centres de tourisme, les points d'information. À chaque fois que tu arrêtes là, ils te demandent ton code postal.
Christophe: Ben oui, mais là, maintenant, c'est comme si avec une transaction, je suis capable d'extraire cette information-là, j'arrête de gosser le monde.
Gabriel: C'est moins d'efforts, puis tu as l'info quand même. Ça fait que tu élimines quand même une étape importante de l'équation. Puis, à la limite, c'est une personne de moins que tu as besoin de payer, si c'est quelqu'un qui le faisait manuellement, tu sais.
Christophe: Oui. Alors, ben oui, c'est ça. Puis, c'est ça. Ma question, c'est dans le fond, Est-ce que c'est possible de le faire? Si c'est oui, est-ce que tu peux le faire avec toutes les POS? Si c'est oui, peux-tu me l'écrire?
Gabriel: C'est sûr qu'il faudrait regarder, mais en fait, l'enjeu que tu as de ça, par exemple, c'est la validité de l'information parce qu'une carte, moi, je peux emprunter la carte de crédit de quelqu'un et payer. En tout cas, je ne sais pas si ça peut avoir de l'impact.
Christophe: Rendu là, c'est un pourcentage minime, je pense.
Gabriel: Ok. Fait que toi, c'est quoi que tu étais à l'aise à gérer? Parce que tu sais, si on pense aux différents edge case, use case que les gens font, qu'on pense jamais, là, ça en est un de base de juste genre, c'est pas parce que j'ai payé avec une affaire que l'information qu'il y a sur mes informations de paiement me représente exactement et réellement. Puis, mais j'imagine que toi, c'est dans les stades d'utilisation, tous ceux-là qui ont payé, c'est ces personnes-là que tu as les informations.
Gabriel: Est-ce que, Parce que toi, dans le fond, de ce que je comprends, c'est que l'information que tu veux vendre après ça, c'est au festival, le fait qu'eux aient accès à les codes postales. Mais ça impliquerait que beaucoup de monde... En fait, tu peux juste aller extraire l'information de ceux qui ont vraiment payé. Est-ce que c'est suffisant pour les festivals? J'imagine que oui.
Christophe: OK, je vais reformuler ça.
Gabriel: Oui, vas-y.
Christophe: J'ai les statistiques de ceux qui ont gaspillé de l'argent dans ton événement. Je pense que c'est à plus de valeur que ceux qui sont venus.
Gabriel: Oui, effectivement. Par contre, tu n'as pas les codes postales de tout le monde, mais c'est ce qui l'insiste. Le code postal, c'est quand même une information pertinente finalement au niveau des gens qui consomment sur place.
Christophe: Je vais pouvoir le mettre dans tous les types de POS qui existent ou genre un système de paiement qui existe. C'est-à-dire, si dans un festival, il y a une épicerie qui est comme que la porte mène au festival et l'épicier est comme oui, c'est beau, tu peux rentrer. Parce que tu sais, il y a aussi des commerces qui seraient intéressés à peut-être séquencer leurs stats.
Gabriel: Ok.
Christophe: Alors, tu sais, une fois que le programme est fait, tu me dis combien ça me coûte par année, c'est quoi les API que je dois avoir avec ça, genre, ou comment je peux faire en sorte, parce qu'il y en a que ça serait comme, ben, ça devient illégal quand tu fais un payle mail de l'information comme ça, tu ne peux pas identifier personne.
Gabriel: Ouais.
Christophe: Ben, c'est ce que j'ai besoin de savoir.
Gabriel: Ok, ok, je comprends. Puis toi, quand tu parlais entre autres de l'utilisation pour multiples POS, c'est-tu parce que l'objectif de cette partie de logiciel-là soit utilisé ailleurs que sur la borne? C'est vraiment ça. Puis, je fais le reste dans ma tête. C'est là qu'effectivement, ça peut être rentable parce que si tu as différents commerçants qui utilisent différents POS, bien là, si tu es compatible avec eux, tu es capable de récupérer l'information.
Gabriel: Moi, je pense qu'un des défis que tu vas avoir, c'est que tu sais, Lui qui arrive avec son Foodtruck qui a déjà son service de paiement, il faut qu'il s'authentifie avec Tick, Read and Charge pour que ça passe par Tait Transactions pour qu'ensuite, tu peux s'extradier les informations.
Christophe: Non, non, non. C'est là que je veux qu'on mette ça au clair. Je veux qu'il garde ses affaires, que c'est ses programmes. Il pourrait avoir ses programmes, puis avoir une séquence de toutes ses affaires. On va dire qu'il est avec Moneris, Clover, ou toutes ces affaires-là, que ce soit dans ses affaires. Mais moi, je vais pouvoir lui donner un dashboard avec comme voici la séquence de tes transactions.
Gabriel: Mais ça, à ce moment-là, il faudrait que les gens mettent les clés d'API de chacun de ces services-là.
Christophe: Et encore là, c'est ça. Encore là, je veux m'arranger de ça. Je veux savoir si c'est possible, comment le faire et combien ça va coûter.
Gabriel: Je comprends. Très bien. Évidemment, en ce moment, je n'ai pas la réponse, mais il faudrait faire la recherche.
Christophe: C'est pour ça que je te demande de faire la recherche. Tu vois un peu comment je séquence toutes mes affaires. Après ça, on va revenir un peu en arrière. Le end user qui utilise mes batteries. Présentement, T'as deux façons de louer une batterie, c'est que t'arrives devant la machine, tu scans le code CRAQ qui t'amène vers mon portail, ma plateforme. Cette plateforme-là, elle te demande un numéro de téléphone pour la vérification. Une fois que c'est fait, elle demande une carte de crédit pour le dépôt.
Gabriel: Ça, c'est déjà fonctionnel en ce moment?
Christophe: C'est 100% fonctionnel, ça marche très bien. Ok.
Gabriel: Parfait. Ça demande le numéro de carte de crédit pour le dépôt. Ou je pose un pay with Apple Pay ou des affaires de même aussi, peut-être.
Christophe: Non, non, c'est vraiment JustTribe. JustTribe, c'est ça.
Gabriel: C'est ça.
Christophe: Donc, tu as JustTribe, il pointe juste la carte. Puis, c'est ça. Donc, je suis très limité là-dessus. Puis, tu vois, je recollecte juste un numéro de téléphone. Puis je collecte un dépôt. Là, il y a beaucoup de monde qui charge sur le dépôt. Alors moi, mon but ultime, c'est de pouvoir créer des profils complets. Que le monde puisse mettre là comme des cartes de crédit, mais qu'il soit chargé à l'utilisation. Comme, mais pour prendre le dépôt parce que genre. Tu comprends?
Gabriel: Ouais. Fait que c'est parti à l'utilisateur. Ouais, ouais, c'est ça. OK, exact. Ça, évidemment, aussi, je vais devoir m'informer pour cette partie-là parce que je ne vais pas développer là-dessus, mais je vais demander à l'interne s'il y a des gens qui ont fait des sites comme ça chez TNM.
Christophe: Mais une fois que la personne va pouvoir faire aussi son profil, je vais pouvoir qu'il associe son profil à, on va dire, Instagram, Facebook, TikTok, parce que s'il publie des affaires, je vais pouvoir le savoir, puis pouvoir lui offrir des petits bonus. pas payer quelqu'un pour faire du marketing quand les clients peuvent le faire pour moi.
Gabriel: Ouais, OK. Puis, est-ce que là, évidemment, c'est bon, c'est une autre partie aussi. Donc, publicité ciblée pour les personnes, puis après ça, prise d'informations de montage de profils par utilisateurs. Quand on parle de profil, profil complet, qu'est-ce que tu parles comme information?
Christophe: Comme la personne dit. Tu as créé un profil rapide, ça serait ton numéro de téléphone, puis genre tu mets un dépôt. Mais si la personne, il ne veut pas mettre de dépôt, je veux qu'il crée un profil comme à travers son Google ou Microsoft ou n'importe quoi. Comme ça, il peut créer un profil par rapport à ça. Puis je lui charge juste le temps qu'il va utiliser. À la place, je te charge à la fin de la journée toutes les transactions ou comme le temps que tu as pris.
Gabriel: C'est là que tu mentionnes un peu comme Bixi où est-ce que c'est basé sur une fois l'utilisation quand tu as ramené le vélo, la même affaire une fois que tu as ramené la batterie, c'est là que ça te charge.
Christophe: Comme ça, on n'a pas de dépôt de prix puis il y a bien du monde qui arrête de chialer.
Gabriel: OK. Parfait.
Christophe: Pour finir, quand on va créer des profils comme ça, j'aimerais ça aussi que quand une batterie est achetée par un client, qu'elle lui soit identifiée à cette personne-là. Ça veut dire que si la batterie revient dans mon système, elle l'a achetée, donc je sais qu'il va pouvoir la redébarrer.
Laurent: Pour la durée de l'allocation.
Christophe: Non, non, on va dire que toi, t'arrives, tu prends une batterie, tu t'en vas, blablabla, ça fait, d'habitude j'ai à peu près 2-3 jours, pis si tu me l'as pas ramené, ben, je considère que tu l'as acheté.
Gabriel: Ah, ok, ok.
Christophe: Là t'arrives dans 2 semaines, t'es comme, tu utilises la batterie comme si c'était la tienne, t'aimes ça, blablabla, on crie de la fidélisation. Là t'es comme, ma batterie est morte, pis j'ai une station devant moi. J'aimerais ça que le client puisse remettre la batterie, et toujours être associé à lui, puis qu'il puisse la débarrer de son téléphone, genre comme, OK, ta batterie est pleine. Ça t'a coûté cinq ans ou dessous pour la recharger pour deux heures.
Gabriel: Ok, ok, ok. Si, si, si, si, mettons, quelqu'un l'a pris puis ça a été considéré comme étant acheté, s'il la ramène, bien, ça lui appartient encore, mais il peut utiliser le service pour le chargement finalement. C'est ça l'idée.
Christophe: Ok. Bingo. Parce qu'il y a des entreprises qui veulent, qui vont vouloir avoir, parce que nous, on va aller dans plus tard, dans six mois, un an, on va aller vers du branding plus personnel. Où est-ce que le monde vont pouvoir brander leur propre batterie avec leur couleur, leur et offrir des caisses qui vont par dessus. Ils vont pouvoir remettre les batteries quand elles sont vides dans des événements pour pouvoir se recharger. Comme ça, ils n'ont pas besoin de penser, j'ai oublié de recharger ma batterie.
Christophe: J'ai une batterie vide avec moi, ça ne sert à rien. J'ai une station là, je dois en acheter une autre.
Gabriel: Ouais, ok, je comprends. Puis, est-ce que ces batteries-là, elles peuvent se charger par autre chose que tes bornes?
Christophe: Oui, ils ont une prise USB-C de côté, mais ça va plus vite parce que j'ai comme 6 ici, quand je les connais comme ça. Dans la station, ça va vraiment plus vite, mais je veux dire, à l'intérieur de 20 minutes, 30 minutes, c'est plein.
Gabriel: Ok, je comprends. Ok, ben ça, selon moi, c'est assez clair. Il faut regarder l'espèce de returning customer avec le use case de la batterie qui a potentiellement été achetée ou donnée ou, tu sais, je ne sais pas, ça peut être une entreprise qui est donnée en cadeau à tout le monde puis que la borne est disponible tout le temps, peu importe.
Christophe: Puis à côté... Comme toutes les autres bornes pourraient être disponibles, je comprends.
Gabriel: Ouais, c'est ça, pis c'est le côté, est-ce que tu charges pour le, est-ce que tu billes les gens pour charger des affaires?
Christophe: Tu sais, parce que t'as le système.
Gabriel: Pour là qu'il est. Ouais, c'est ça.
Christophe: Au début, non. Peut-être dans le futur, ça serait une option de comme, ok, ben, recharge ton, tu sais, je pense que tu peux le recharger un nombre de fois, pis on te donne un crédit par rapport à ça, là, mais je... En tout cas, je n'ai pas l'idée, mais peut-être qu'à un moment donné, on va commencer à charger.
Gabriel: Il y a un modèle à voir, parce que dans des cas d'utilisation, je veux dire, je sais que souvent dans les aéroports, tu as des bornes de genre, soit tu paies pour débloquer des couettes de fil et tu peux charger tes bidules, ou sinon, ils mettent souvent des prises partout où le monde ont leur fil.
Christophe: Ouais, ben justement, je suis en part aux aéroports, puis je commence à trouver ça très problématique, ces endroits-là, parce que ça réunit trop de monde au même endroit, puis les fils brisent tout le temps. Même les prises électriques, ils brisent.
Gabriel: Oui, c'est maluseux. Le monde y passe par là beaucoup.
Christophe: Ouais, ben c'est ça. Alors imagine que moi j'arrive, puis je suis comme « Ah, il y a une batterie qui est brisée », ben je la reprends, j'en mets une autre, pis ça finit là.
Gabriel: Effectivement.
Christophe: Sur le modèle qu'on travaille, ça va être des batteries au carbone et nos batteries au lithium. Donc, on enlève la matière dangereuse. Ça veut dire que tu embarques dans l'avion avec et ça ne va pas te péter en pleine face s'il y a quoi que ce soit.
Gabriel: Intéressant. Fait qu'écoute, c'est sûr que ça va être le défi de vendre aux aéroports et de rentrer dans ce marché-là. Mais j'imagine que tu en reparles déjà. Fait que ça peut être bien.
Christophe: C'est ça. Donc, tu connais à peu près tous les enjeux que j'ai.
Gabriel: Je comprends.
Christophe: Tu vois un peu dans quelle direction je m'enligne. Là, je ne suis pas un programmeur. Je vais vous donner un exemple. L'hôpital me demande de modifier des affaires et je patine pour être honnête. Est-ce que je ne suis pas capable de le faire?
Gabriel: Oui, oui, c'est correct. C'est normal. Sinon, tu dirais que je vais pelleter ça dans le cours de ma équipe de développement.
Christophe: Quand tu n'en as pas, tu fais pas mal d'heures.
Gabriel: Oui. Ils ne sont pas obligés de savoir que ce n'est pas.
Christophe: Je sais. Je n'ai pas dit que je n'en avais pas. J'ai dit que mes techniciens sont occupés à développer la plateforme.
Gabriel: Exactement. Ça, c'est bon.
Laurent: C'était la bonne réponse.
Gabriel: Ça, c'est la bonne réponse. OK. Écoute, là, j'ai pris quelques notes. Dans ces différents enjeux-là, en fait, évidemment, il y en a plusieurs sur lesquels on peut facilement intervenir, puis d'autres un peu moins. Il y en a qui ont des questions à répondre sur les trucs sur les bornes de paiement, surtout. Puis on est à la merci de qu'est-ce qu'eux exposent, puis de tous les supporter. Ça peut être un enjeu particulier. Est-ce que tu en as déjà, toi, en ordre de priorité? T'as mentionné Clover, t'as mentionné... T'as-tu déjà un peu... Monéris. Ouais, Monéris, Clover.
Christophe: Clover, c'est le plus agressif présentement. Il a été très agressif dans les dernières années, alors il va vraiment plus ressortir. Mais honnêtement, le plus possible, tout ce que je peux mettre la main dessus, le plus possible. Il y a bien du monde dans bien des régions qui pourraient être intéressés. Pour moi, c'est un, une fois qu'il est placé, je peux en faire de quoi qui est très beau. Comme je dis, je peux, je peux séquencer ça comme, ben voici de quoi le programme, voici, voici tes taxes, tes statistiques pour l'année.
Christophe: Ah ben tu veux aussi moi, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben, ben.
Gabriel: Ben, ben, OK, OK. Pis sinon, là, ça, habituellement, quand je suis préparé ben, sur les appels, j'ai peut-être l'info. Peut-être, Laurent, tu me l'avais peut-être partagé, mais j'ai pas vérifié. Mais tu sais, je sais pas si... Toi, t'as déjà parlé un peu avec BarchCard, fait que j'imagine que tu comprends le fonctionnement de TLM, tu comprends... T'as fait un peu une présentation du genre de services qu'on offre. tout ça.
Gabriel: Est-ce que tu avais déjà une idée de, je ne sais pas si tu as un budget disponible ou c'est quoi tes attentes, autant financière, mais tant échéancier aussi.
Christophe: Honnêtement, je n'ai pas rien de fixe. Si tu me dis que ça me prend deux ans de te faire ça, ça va te prendre deux ans de te faire ça. Si ça te prend deux mois, mais ça me coûte 100 000, je vais faire comme, bon, on va étaler ça sur plusieurs, plusieurs mois.
Gabriel: Je veux dire, tout ça peut, somehow, à un certain point, mais tu sais, en fait, toi, dans les, ce que j'aimerais savoir un peu, c'est quoi les éléments les plus critiques et lesquels on pourrait s'adresser en premier? Parce que tu sais, moi, ce que je peux te faire, c'est comme un truc un peu plus général de genre, En termes d'efforts représentatifs, essayez de répondre aussi aux questions que tu as mentionnées.
Gabriel: Je pense que c'est pertinent qu'on puisse arriver pour une prochaine conversation pour dire, écoute, j'ai trouvé telle et telle et telle information, voici les limitations, qu'est-ce qu'on peut faire? Parce que si toi, ce que tu dis, c'est que tu as de la place pour un marché vraiment intéressant, si tu débloques cette information-là, après ça, techniquement, l'argent n'est plus un problème. C'est peut-être la priorité qu'on devrait adresser.
Christophe: Celle-là, pour être honnête, ça serait une priorité. Le truc pour les POS. Tu viens de me garantir un financement de 115 000 $. Je passe le reste de ta programmation sur la subvention.
Gabriel: Oui, c'est ça, exactement. Puis justement, parlant de subvention, je ne sais pas si tu as eu des conversations à ce niveau-là avec Bashcore, style R&D, qui prend quoi et tout ça, parce que dans le fond, dans tout développement, quand il y a de l'incertitude, des choses comme ça, il y a moyen d'aller par exemple réclamer de la R&D, ça peut être une forme de financement que tu reçois. plus tard, mais est-ce que c'est toi qui la demande ou est-ce que c'est nous, etc.
Gabriel: Ça, je te laisserais avoir la discussion à la limite avec Bachar où Laurent pourrait intervenir avec des détails sur ça. Parce qu'on a toujours, je veux dire, on fait des ententes précises avec les gens. Oui, j'avais compris. C'est du cas par cas. Mais en fait, ce qui fait ton affaire risque de faire notre affaire. Autrement dit, on s'arrête pour que tout le monde soit soireux.
Christophe: Justement en parlant de Bachar, Ok, c'est bon. Parce qu'il avait oublié de signer quelque chose. J'étais comme, hé, c'est moi ça là.
Gabriel: Le NDA genre?
Christophe: Ouais, c'est ça. Oublie-moi pas là.
Gabriel: Non, non, non, c'est ça.
Christophe: On en parlait, puis là tu as dit quelque chose, j'ai fait comme, ah, une chance qui a signé, une minute qui n'a pas signé.
Gabriel: Oui, c'est ça. Mais en fait, anyway, moi, je considère qu'à chaque fois que j'ai une conversation, ce n'est pas la première fois que je parle avec des entrepreneurs, évidemment. Je considère que j'ai tout un lien avec les gens parce que, anyway, on en a généralement avec tous nos clients. Evidemment, ce qui se dit en ce moment dans l'appel, oui, dans l'enregistrement et tout, mais on utilise juste en interne et ce n'est pas un projet absolu. Fait que ça, c'est sympa, t'as pas à t'inquiéter. Pour ce qui est des... Fait que dans le fond...
Gabriel: Bref, au niveau de budget, j'ai entendu un peu comme ça peut tourner des trucs de 100 000 ou de... Moi, je pense que l'idée, c'est pas de brûler, évidemment, tout ça, mais ça me donne quand même un ordre d'idée de où est-ce qu'on peut se rendre pareil avec les premières étapes.
Laurent: Mais la question, est-ce qu'on parle plus de ressources? Est-ce que tu vas avoir plus une personne à temps plein avec toi, deux à temps partiel?
Christophe: Avec de mon bord?
Laurent: Mais d'abord, c'est la personne qu'on peut fournir pour toi. On trouve un meilleur profil qui peut t'accompagner là-dedans.
Christophe: C'est ça. Dans le fond, moi, j'ai besoin de quelqu'un pour toute la création, puisque c'est tellement vague que je ne peux pas avoir une personne qui fait tout. Il y en a que, oui, tu vas pouvoir me programmer. Il y en a beaucoup qui font du full stack, puis ils font plein d'affaires. Je veux dire là, tu me demandes d'aller programmer de quoi dans un POS. Puis après, je te demande d'aller programmer des utilisateurs qui peuvent associer des comptes Google, pas des comptes Google, mais des comptes de Facebook et Instagram.
Christophe: Puis après ça, je m'en vais me demander de gérer une plateforme complètement différente. Tu comprends? Je sais que je n'aurai pas besoin.
Gabriel: Ça risque d'être varié un peu. Tu vas avoir quelqu'un qui va être plus orienté back-end, plus front-end aussi, parce que t'as quand même des enjeux au niveau de la plateforme. Évidemment, moi, je vais être très curieux de voir le code base existant aussi pour voir qu'est-ce qu'on peut faire avec ça, parce que j'ai l'impression qu'on va désirer partir de cette base-là pour atteindre les objectifs. Si tu me dis que ça fonctionne déjà, puis comment on l'améliore. Ce truc-là, en ce moment, c'est hébergé quelque part aussi. Est-ce que c'est un compte AWS ou.
Christophe: Un compte... AWS, Route 55, puis c'est moi qui l'ai.
Gabriel: Ok. Parfait. Fait que tu acceptes toutes ces affaires-là.
Christophe: Tout est à moi là-dedans.
Gabriel: Excellent.
Christophe: Tout est dedans, mais je ne suis pas super technique, mais j'ai appris à c'est quoi un code.
Gabriel: Oui, c'est ça, tu as la base.
Christophe: J'ai appris, je n'ai pas eu le choix.
Gabriel: Excellent. Ben, pis t'sais, nous autres, ce qu'on peut faire aussi, évidemment, une fois qu'on a accès au Codebase, moi, je peux quand même, t'sais, je vais analyser qu'est-ce qu'il y a en place, qu'est-ce que ça fait, à quel point on est capable de le faire tourner en local. T'sais, j'ai vu qu'il y avait un crochet rouge tantôt, pis ça me disait qu'il y a quelque chose qui faille. Les déploiements, c'est-tu automatisé, etc.
Gabriel: T'sais, je peux te sortir un peu une espèce d'éventail aussi, là, en termes de, t'sais, un espèce de rapport technique de t'en est où, où est-ce que ça devrait aller avant qu'on commence à travailler dedans aussi, là, s'il y a des choses à faire.
Christophe: Il y a aussi des affaires un petit peu plus urgentes, comme l'hôpital veut que je fasse des modifications avant qu'on le déploie. Si tu as les détails des demandes de ça aussi. C'est tellement niaiseux. C'est juste la formulation de Mouron. Ah, cette ligne-là, c'est mal écrit. Peux-tu l'écrire comme ça? Là, t'es comme... T'as changé une virgule de place, une fucking niaise.
Gabriel: Oui, oui, je comprends. Mais en fait, ce genre de petites affaires-là aussi, à la limite, ça peut être de regarder si la plateforme, on peut faire en sorte que ce soit... Surtout si c'est des trucs qui sont récurrents. Si c'est un one-time, tu le fais, tu l'appliques, puis ça fonctionne pour tous tes clients. OK, fine.
Christophe: Généralement, c'est un one-time. Puis si je veux dire comme, la plateforme, comme le portail. J'appelle ça un portail parce que je ne veux pas mettre une application. Le mot application, je déteste ça. Le monde déteste ça. On est étonné d'en avoir. Je ne veux pas obliger le monde à télécharger une application. Je veux que ce soit une possibilité qu'un monde puisse le télécharger en application, mais je ne veux pas qu'on le force.
Christophe: Présentement, mon ancien, la personne qui a créé la plateforme, elle a programmé que si tu as un Android, on te suggère que tu peux la télécharger. en commençant. Je ne veux pas nécessairement que ce soit le cas, tu comprends? Je veux juste que ce soit une option.
Gabriel: Ça fait plus lean, c'est vraiment plus lean si c'est web aussi, tu sais, je veux dire, tu as une plateforme, tu te connectes, puis tu sais, ça fonctionne partout, tu n'as pas de gestion.
Christophe: Je ne veux pas aller dans le Google Store, je ne veux pas aller dans l'App Store puis le Google Store. Je veux pouvoir télécharger ça sur le téléphone sans passer par là.
Gabriel: Ouais, c'est ça, le côté web app, c'est vraiment, c'est quand même l'idéal, là, évidemment. Puis c'est les fameuses SPA, là, en tout cas. Mais oui, c'est ça. Ça, je regarderais pour être sûr que ça... Je ne vais pas me faire bouffer.
Christophe: Par le 30 %, genre...
Gabriel: C'est ça, exact. Je veux dire, c'est sage décision. Déjà, le chocolat. Ça va te coûter plus cher pour avoir moins. Fais pas ça, c'est ça. Ça, c'est bon.
Christophe: C'est ça. Donc, tu vois comment je m'enligne sur tous les aspects. Là, comme j'ai dit, j'ai des petites modifications à faire pour l'hôpital et c'est niaiseux, comme j'ai dit. Il y en a un que c'est comme, ah ben en français, je te niaise fucking pas. En français, le signe de dollars, il est supposé d'être devant l'argent. Puis en anglais, il est supposé d'être derrière. Là, j'étais comme... Il n'est pas été programmé comme ça, je m'excuse.
Gabriel: Ah, mais là, c'est terrible. OK, c'est le genre de petites affaires. C'est sûr que, évidemment, c'est des trucs qui ne sont pas spécifiques à eux. Mais en fait, je posais la question aussi à savoir si c'est des one time ou pas. Je pense que c'est des trucs d'amélioration de la plateforme classique ou normaux. Eux autres, ils veulent absolument que ça fitte dans les conventions, mettons. Mais est-ce que tu as des enjeux plus de, il y a des modifications qui peuvent être d'une certaine manière pour un client versus une autre pour un autre client.
Gabriel: Le fameux white label, tu n'es pas là-dedans du tout. Parce que ça, c'est de la complexité que tu ne veux pas avoir non plus. Si tu es capable d'avoir une plateforme qui fonctionne pour tout le monde, c'est vraiment l'idéal et c'est ce que je te recommande aussi.
Christophe: C'est ce que je veux faire exactement, mais c'est comme là où est-ce que j'ai beaucoup d'enjeux, c'est avec Display. Parce que là, en même temps, Display, lui, il offre un white label, mais je suis comme, je ne veux pas payer 25 000 piastres pour que je mette mon nom dessus. Je veux utiliser leurs API pour extraire qu'est-ce que j'ai besoin. Puis là, ce sont mes affaires.
Gabriel: Ça te coûte moins cher à mettre accès aux data quand même. Puis, est-ce que dans les termes d'utilisation, le data t'appartient ou as-tu jeté un oeil là-dedans côté plus...
Christophe: J'ai pas checké, j'ai pas regardé, mais à mes souvenirs, le data m'appartient.
Gabriel: Ok.
Christophe: En tout cas, ce prochain an, ça.
Gabriel: Vaut peut-être la peine. Parce que juste comme, autrement dit, j'imagine si on est capable d'exporter le data, une fois que tu l'as, j'imagine que tu peux faire ce que tu veux. Puis sinon, s'ils exposent des API, ça veut dire qu'ils sont assez open sur ça. Puis à un moment donné, tu payes pour le service. Mais ça peut être un enjeu, parce que surtout si tu bâtis ta business autour de cette solution-là, puis que for some reason, eux autres, ils décident de fermer ou changer des affaires.
Gabriel: dépendent des ressources externes, tu es toujours un peu à la merci d'eux aussi. Si ce n'est pas toi qui mesure la data, tu es un peu... On a Laurent qui devait avoir un autre appel, mais ce n'est pas grave, il aura le résumé. C'est ça en même temps que je.
Christophe: Regarde d'un coin de l'œil l'autre joueur qui... J'ai quelqu'un ici au Québec qui est en train de se développer. Il n'y a pas tout ça, c'est correct. développer, puis là, quand t'as développé, je lui coupe l'herbe en dessous du pied, je coupe ses clients, puis à partir de là, je le rachète.
Gabriel: Ça peut être une option, effectivement.
Christophe: Je sais que c'est une traite, mais.
Gabriel: Je veux dire que... Ah, business as usual, I guess. C'est la meilleure stratégie, I mean, t'as.
Christophe: Pas besoin de le développer,
Gabriel: ça te. Coûte moins cher et ça peut aller plus vite. Exact. C'est sûr que si tu peux faire ça, tant mieux. Après, il te reste quand même que t'auras les étapes de compliance à passer techniquement si toi c'est un rookie aussi. En même temps, est-ce que ça t'empêche de vendre pour, mettons, les festivals? J'imagine que non, mais sinon, c'est le produit que tu vendrais pour les hôpitaux, par exemple. Lui, tu sais, ça passe même pas par la communication.
Christophe: Ah bon, il est revenu.
Gabriel: Il est revenu. Il a achoumé trop fort. Il a peut-être perdu le courant. Ça va leur arrêter.
Laurent: Ouais, c'est sûr, la tempête s'en vient, pis j'ai perdu le courant un couple de secondes.
Gabriel: C'est ça que je me disais avant. Je pensais que t'avais peut-être un autre appel.
Christophe: T'es dans quel coin? C'est pas moins discret.
Laurent: Je suis dans le coin de Saint-Jean-Port-Joli.
Christophe: Saint-Jean... Port-Joli. OK, j'ai Saint-Jean-sur-Richelieu, moi, à la Côte-de-Chine.
Laurent: Ouais, non, un peu plus vers l'est du Québec, après Québec.
Christophe: OK, OK, OK. Ben, j'ai monté en Gaspé il y a pas... il y a comme deux jours, alors. Je redescends de la Gaspé, en fait, parce que j'étais en festival là-bas, alors j'ai dû croiser la région.
Laurent: C'est passé dans chez nous, ouais.
Christophe: C'est ce que je me disais. Ironiquement, j'ai croisé un ami à Trois-Pistoles, parce que c'est un trucker, pis il a dit... J'étais comme, tabarnak! Les estis de trucker, vous vous faites chier, genre. Pis il est comme... Je descends de la Gaspé. « Hey, t'es rendu où? » Ben, j'étais à Trois-Pistoles. Moi aussi. Deux heures, je n'ai pas vu. Je suis comme, t'es où à Trois-Pistoles? Je suis comme là, je tournais dans le stationnement quasiment. Alors, c'est très drôle. En tout cas, petite parenthèse. Ouais, c'est ça. Donc, c'est ça. Reviens-moi avec quelque chose.
Christophe: Qu'est-ce qui est possible? Puis, de toute façon, un autre taker qui va te séquencer toutes tes caractéristiques.
Gabriel: Ça, ça va me sortir le résumé un petit peu aussi. Mais évidemment, c'est moi, en fait, de mon côté, les premières étapes. Déjà, si tu peux m'inviter, par exemple, au compte GitHub, au projet GitHub, pardon, le compte, c'est ton compte. Je n'ai pas besoin de tout mon compte au complet. Juste le ou les projets, s'il y en a plusieurs. C'est tout ce que tu peux me partager de contexte que je peux aller explorer pour te sortir plus de détails. Ça va m'aider dans mon travail.
Christophe: Est-ce que tu pourrais m'envoyer ton courriel?
Gabriel: Oui. Ouais ben même mon courriel TLM ça va fonctionner, je te l'écris dans le chat qui est ici aussi mais essentiellement si tu m'invites sur Github avec ce courriel là ça va te sortir mon username Github c'est Gab Leroux. Je te l'écris aussi dans le chat, tu vas le voir passer comme ça mais en fait si t'écris mon courriel mon username va popper aussi.
Christophe: Je ne sais même pas comment ajouter quelqu'un.
Gabriel: Je peux le guider si tu veux. Écoute, on peut faire ça rapidement vu que c'est une des tâches. Je ne vois pas ton écran, mais je peux aller me connecter sur GitHub pour te pointer les bonnes places. Je vais ouvrir un projet quelconque et t'aider. Normalement, tu t'en vas dans Settings en haut à droite. Ensuite, ça va aller dans Collaborators and Teams.
Christophe: Attends une minute.
Gabriel: Ouais, excuse-moi, dans le milieu de l'écran.
Christophe: Merci.
Gabriel: Mon écran est petit, fait que c'était en haut à droite.
Christophe: Collaborateur, c'est bon.
Gabriel: Collaborateur, exact. Là, tu vas te connecter là, puis c'est à partir de là que tu pourras t'inviter.
Christophe: Je me connecte sur mon téléphone.
Gabriel: Ouais, c'est bien protégé, tu peux pas inviter par accident quelqu'un.
Christophe: Je savais pas.
Gabriel: C'est très bien, Guy. Ça appartient à Microsoft, en fait.
Christophe: Ah, OK, ouais. Bon, on va l'enlever.
Gabriel: Exact. Si tu veux faire l'aménage, c'est le moment.
Christophe: Ouais, ben en même temps, Colin, c'est celui qui l'a parti. Alors, j'ai comme l'impression que si je l'enlève, il y a des affaires qui risquent de disparaître.
Gabriel: Ah mais peut-être pas en fait parce que considérant que ton projet, ton user c'est Chris Capps dans le fond.
Christophe: Ouais.
Gabriel: Ben tu sais si c'est sous ton projet et que t'es owner à ce moment là, tu peux retirer l'autre.
Christophe: Ouais ben comme je dis on regardera ça en temps et lieu parce que si jamais je suis puni par Je ne veux pas briser rien.
Gabriel: Exact, c'est moi. Ce n'est pas trop indiscret, dans le fond, le développement qui a été fait par le passé, la raison pour laquelle tu préférais passer par TLM plutôt que de continuer avec l'ancien. Il est parti. Il est juste parti.
Christophe: Je n'ai pas personne. Pour l'instant, j'ai mon ami Max Martineau, qui est un CTO d'un FinTech. Deux heures à perdre, il est comme « Hey, je m'en vais régler tes problèmes ». Je suis comme ça fait huit semaines que je pleure là, tout est arrivé en deux heures, tout est réglé. Puis tu m'as dit que deux heures après ça, tu me rajoutes des features.
Gabriel: Oui, c'est ça. Évidemment, je ne te cacherais pas qu'avec les outils d'intelligence artificielle en développement, surtout pour des gens qui sont expérimentés, arrivés dans des projets, puis adapter le code, tout ça s'est rendu vraiment plus accessible, facile et rapide. On a comme gagné en vélocité en termes de développement aussi. C'est sûr que ça ne règle pas tous les problèmes, mais je peux quand même te confirmer qu'on va être sûrement très à l'aise selon comment c'est monté.
Gabriel: Moi, je vais le faire tourner localement, mais c'est ça, si c'est assez simple et qu'on est capable de le faire tourner, on va regarder ce qu'il serait à faire. Si jamais tu as un genre de... Je ne sais pas si tu as déjà peut-être dressé une liste parce que là, on a discuté, bon, verbalement. Oui, je vais avoir le note-taker, mais tu sais, est-ce que tu as comme un peu une liste de demandes, une liste de tâches, des choses qui sont à faire, déjà établies?
Christophe: Honnêtement, non, je n'ai pas une liste. Mais ce que je sais, c'est que j'ai donné, regarde là, c'est comme l'hôpital m'a envoyé ces modifications-là à faire. C'est comme la première chose à faire, ça prendra pas de temps, on va s'entendre.
Gabriel: Non, c'est ça, exact.
Christophe: Ah ben une nouvelle modification, c'est juste s'assurer que ça soit bien écrit comme qu'ils veulent. Là, j'étais comme... Oh, regarde, tu vois ici. Là, ici, c'est parce que le signe de dollars est de l'autre bord.
Gabriel: Là, je suis comme... OK, c'est bon.
Christophe: L'icône ici, il voulait un petit icône, tu sais, l'engrenage à place de celui-là, celui qui est avec des flèches.
Gabriel: Oui, c'est ça. Évidemment, c'est des petites modifications cosmétiques. C'est sûr que je peux l'évaluer, mais je peux tout de suite te dire que si on est capable de faire tourner l'application, puis que toutes ces informations-là sont montées d'une manière qui n'est pas n'importe quoi, que ce n'est pas mal fait en tant que tel, Ah ben.
Christophe: Non, c'est super facile. Même moi, j'ai commencé à les modifier. C'est juste que j'ai fait de quoi que je n'aurais pas dû faire.
Gabriel: Je vois qu'il y a un petit crochet rouge. En fait, maintenant que j'ai accès, je pourrais aller voir aussi le résultat des crochets rouges.
Christophe: C'est juste que j'ai mis de quoi qui n'a pas passé, c'est tout.
Gabriel: OK, correct. Pourtant, la modification, tu as juste retiré la majuscule de R, mais c'est ça. De toute façon, j'irai voir comment c'est fait lors de l'analyse. Vraiment, moi, ce qui m'intéresse, c'est beaucoup les modifications qui sont critiques ou nécessaires à faire dans ce logiciel-là. Puis, est-ce que tu as peut-être soit de la documentation pour la mise en ligne côté AWS?
Christophe: Non. Ah, attends. Les programmes que j'utilise aussi, c'est Cloudflare.
Gabriel: OK, ouais.
Christophe: C'est pour gérer le flow d'informations. On a des travailleurs, je pense.
Gabriel: Ah, des workers. OK, t'as des Cloudflare workers pour faire du traitement. Parfait.
Christophe: Ouais, pour regarder qu'est-ce qui se passe.
Gabriel: OK.
Christophe: Après ça, j'ai Twilio.
Gabriel: Est-ce que le code des Cloudflare Workers, tu sais où est-ce qu'il est? Il est seulement dans le code Cloudflare peut-être, dans le projet Cloudflare?
Gabriel: Ça, ce serait quelque chose que, en fait, en ce moment, moi, ce que je te propose déjà, en ce moment, le code est sous ton username, mais ce que je recommande en réalité, ça va être de créer une organisation au nom de l'entreprise dans laquelle on va mettre différents projets et ces différents projets-là vont contenir à la fois le projet qu'on regardait tout à l'heure et le code des workers de Cloudflare aussi.
Gabriel: Comme ça, si pour une raison ou pour une autre, tu perds l'accès à ton compte Cloudflare ou quoi que ce soit, ben t'as quand même encore accès au code qui a été développé pour les workers. Ce que présentement, c'est peut-être pas le cas parce qu'il serait juste sur ton compte Cloudflare.
Christophe: Ben, j'en ai plus qu'un compte parce que j'ai celui de mon ancien tech qui était là. Alors.
Gabriel: Il y a peut-être un mini-déménage à faire dans ça ou en tout cas à voir. C'est sûr qu'en ce moment, si ça fonctionne, c'est correct, mais il faudra adresser comment c'est géré. C'est Workers and Platform. Ou sinon, tu peux aller voir dans Compute Workers. Workers and Pages, ok. C'est peut-être dans l'autre compte, sinon. T'as un drop-down en haut pour changer le compte. En haut à gauche je crois sinon. Ça c'est le profil. Si tu retournes sur la page précédente, c'est peut-être sur un des deux là. Ouais.
Christophe: OK.
Gabriel: OK. Fait que là, je vois. Bon, parfait. Là, tu vois, c'est connecté. Déjà, en ce moment, Chris Capps versus l'autre, c'est Sunwise. Fait que tu vois, tu as le Ricoh Admin.
Christophe: Ça, c'était l'ancien, c'est pour ça.
Gabriel: Ouais, c'est ça, mais qui est quand même comme encore actif. Fait que là, en tout cas, il y aura peut-être à vérifier que les bons sont utilisés aux bonnes places. Parce que dans le fond, de ce que je vois, il y a peut-être un enjeu là. Bref, je sais pas si tu serais peut-être en mesure de pouvoir m'inviter là.
Gabriel: dans ce truc-là, on pourra regarder parce que la chose à éviter, parce que là, effectivement, il est connecté avec GitHub, mais tu sais, je vois que tu as IcoAdmin puis tu as IcoApp, il faut s'assurer que ce soit connecté sur ton compte à toi, puis si on le déplace vers une organisation, ça voudrait dire peut-être de recréer les workers, donc là, je ne sais pas si c'est quelque chose de possible.
Gabriel: En tout cas, bref, il y a comme, essentiellement, ce qu'on veut s'assurer, c'est que tu sois vraiment à 100% en contrôle de ton environnement, de ton stack, puis que les choses soient clairement documentées, de où ça va, puis que si... Une des raisons pour lesquelles tu veux que ce soit vraiment ordonné, c'est s'il y a quelque chose qui plante, bien de savoir c'est où qu'il faut aller pour le déplanter finalement.
Christophe: C'est ça. C'est exactement ça. Puis ça, c'est mon super base sur lequel que... OK.
Gabriel: Ça, c'est pour l'accès aux datas puis les... la base de données.
Christophe: Oui, c'est ça. C'est la base de données. J'imagine que tu voudrais avoir accès à ça aussi.
Gabriel: Oui, ça pourrait être très pratique. C'est sûr que techniquement, ça me donne accès aux données de production. Évidemment, je n'irais pas lire le data des users. De toute façon, encore une fois, on est « behind in the air ». Mais c'est sûr que le plus d'informations que j'ai pour déterminer comment les choses sont faites, pour être sûr qu'on est en mesure de développer dedans, puis de déterrer, puis de mettre à jour et de déployer.
Gabriel: Tu sais, ça, c'est le premier point parce que tu sais, même si on fait techniquement rien, je veux dire, il y a un temps de juste comme set-uper nos environnements. Je vois en ce moment que c'est quand même bien ta développement et ta production. Les deux semblent être configurés. Ça, c'est une bonne nouvelle. On a eu des projets où est-ce qu'il y a juste de la prod, puis tu sais, tu peux rien faire, tu peux rien essayer.
Christophe: Non, c'est ça. Au moins, mon dev, il a quand même très bien travaillé. C'est comme mon ami qui est CTO, il a fait une très belle job. Je l'engagerais, c'est ce qu'il m'a dit.
Gabriel: Excellent. Bon ben ça, c'est vraiment de bonnes nouvelles aussi. Parce qu'on est habitué de travailler dans des projets que c'est pas bien fait. Fait que t'sais, j'ai comme tout le temps un peu le bon, on va aller voir là, puis on va découvrir ensemble si ça a été correct ou pas. Mais si tu me dis que c'est ça, c'était quelqu'un d'expérimenté qui est passé déjà avant, puis qui me dit c'est beau, ben écoute, le stress est déjà moins élevé. Nous autres, ça réduit le risque aussi.
Gabriel: Lors de mon analyse, je vais peut-être trouver des surprises, mais t'sais, ça risque d'être pas si pire.
Christophe: Comment je t'ajoute, je t'envoie une invitation?
Gabriel: Oui, dans le cas du SuperBay spécifiquement, moi j'aimerais travailler avec, mais ça ne m'empêchera pas de faire des analyses. Tu vas voir dans les réglages, normalement tu vas avoir, c'est à gauche complètement, la petite gear, t'es dans Project Settings, parfait. Puis après ça, Generate Compute, Dug, API, Vault, Database Storage sont où? Je pense que c'est dans le, ça serait-tu, c'est peut-être en haut à droite en réalité dans ton user. Ouais, pis là t'as Ah non même pas, je me suis arrêté pour aller chercher l'info. D'accord, on va ouvrir tout ça.
Christophe: Alors en attendant, je vais t'inviter sur Cloudflare. Ouais, ça C'est quoi ça, c'est Non... Ah! Account members, c'est ça, ouais.
Gabriel: Access control... Ouais, ça ici, invite members. pis là, non, ouais, utilise mon TLM Go, c'est parfait. En ce moment-là, tu peux inclure les domaines. Normalement, je n'aurai pas besoin d'énormément de règles.
Christophe: Je vais tout te laisser comme tu veux.
Gabriel: Super admin, c'est sûr que je vais pouvoir faire ce que je veux. Évidemment, je n'irai rien briser, mais s'il y a des trucs à adresser, au moins, je vais être en mesure de te donner l'information. J'ai l'impression qu'il va falloir que j'aille me créer un compte avant que tu puisses m'inviter. l'administration. En ce moment, derrière ton user, comme je dis, l'idéal, ce serait plutôt qu'il y ait une organisation pour pas que tu aies besoin.
Gabriel: En fait, l'avantage avec les organisations dans GitHub, c'est que si tu veux, après ça, inviter d'autres personnes, tu n'es pas obligé de le faire projet par projet. Tu peux juste dire, OK, j'invite le groupe de TLM, de telle équipe, tu peux gérer par équipe. Moi, j'ai reçu des courriels, j'ai accepté ça tout à l'heure aussi, si jamais il y a quoi, je pourrais passer par courriel aussi pour la suite, s'il me manque des affaires ou pendant que je suis en train de faire l'analyse.
Gabriel: D'ailleurs, évidemment, j'ai un peu posé la question tantôt, mais en termes de timeline et d'attente pour qu'on soit aligné aussi, tu sais toi, mettons, en termes de réponse, ça ressemblerait à quoi?
Christophe: Moi, je réponds vite, mais ça dépend, c'est quoi?
Gabriel: Oui, c'est ça. Moi, c'est plus à quel moment, toi, tu désires obtenir l'analyse? Parce que dans le fond, évidemment, j'ai comme d'autres projets en cours, des choses comme ça. À quel moment t'aimerais avoir une réponse de nous? Puis moi, je vais regarder pour m'assurer que ça rentre à la main.
Christophe: C'est sûr que j'aimerais ça que tu puisses me dire un peu genre, qu'est-ce qui va me coûter quoi? Puis combien?
Gabriel: Oui, ça va être le but.
Christophe: Ça va être le but. Puis ça, ça doit être fait assez rapidement parce que je veux l'intégrer dans.
Gabriel: Des des demandes de sub sûrement.
Christophe: Merci.
Gabriel: Ok parfait, ben considérant ça, toi t'as-tu justement, ben concernant les demandes de sub, t'as-tu des dates butoirs pour avoir des réponses de ça ou une analyse? Est-ce que je peux m'arranger pour te faire une analyse partielle puis après ça je vais plus en
Christophe: détail. Le 20 août il faut que je soumets genre des affaires pour une subvention. Ok, j'ajoute à mon calendrier. Ouais, alors tu sais, si tu pourrais me le mettre bien avant, comme rapidement, ça m'aiderait à pouvoir. J'ai dit à peu près genre combien ça va coûter, mais s'ils me demandent plus de détails.
Gabriel: Je peux te sortir vraiment comme une analyse détaillée des choses qu'on va faire en fonction de la discussion qu'on vient d'avoir aussi. On utilisera les synchs aussi. Je risque de te poser quelques questions probablement par courriel. Comme tu me dis, si tu me réponds rapidement, c'est parfait.
Gabriel: Si tu as une liste de tâches, comme le document que tu m'as montré tantôt des changements, Moi, je peux en faire l'analyse rapidement, je vais l'intégrer aussi, je vais dire, OK, les changements front-end, ça ne sera pas une grosse affaire, mais on s'attend peut-être quelques heures de quelqu'un, puis ça va être réglé, mais ça va en faire partie.
Christophe: Honnêtement, le truc de l'hôpital que je vais t'envoyer dans pas long, là, si ça pourrait être réglé, genre, rapidement.
Gabriel: Je serais sûrement, en fait, pendant mon analyse, il n'y a rien qui m'empêche après ça d'envoyer une demande de changement, puis de voir si on est capable de le déployer en déf pour te confirmer. Ça peut être un bon test pour confirmer qu'on est en contrôle de la patente. Parce que, tu sais, pour moi aussi, dans le cadre de mon travail, quand je vais travailler, quand je vais sortir l'analyse, moi, ça va être basé beaucoup sur l'expérience.
Gabriel: Mais autrement dit, dans les attentes, évidemment, il y a la réponse aux questions qu'on a mentionnées tantôt, mais sinon, une analyse technique de genre. Combien de temps pour faire chacune des tâches séparées? Je vais te sortir un délai, puis un genre de recommandation aussi de quelle personne devrait travailler sur le projet. Comme on a mentionné, tout le côté POS, ça me prend quelqu'un qui a ces compétences-là.
Gabriel: Mais sinon, pour le front-end puis le back-end, l'admin, tout ça, je vais aller voir comment c'est organisé, puis je vais être en mesure de de recommander les bonnes personnes au sein de TLM pour ça. Ça ne veut pas dire que c'est moi qui vais travailler dessus nécessairement, mais je vais taper une trail. Puis sinon, est-ce que... J'avais une autre question, vite comme ça aussi. Comme là, j'ai toutes les infos pour ça. En ce moment, tu as juste des demandes principalement par l'hôpital. Tu n'as pas d'autres tâches ou des bugs ou des choses à régler.
Gabriel: Il n'y a pas des affaires. C'est quoi les grandes pain points que tu as?
Christophe: Oui, il y a une affaire. On vient d'accréditer les POS. Puis, là, présentement, peu importe ce que j'affiche sur le POS, le montant qu'il affiche dessus, il ne prend pas en compte et il charge toujours deux piastres de l'heure.
Gabriel: OK, OK.
Christophe: Donc, il affiche, on va dire, zéro piastre. Moi, si on va dire que je le mets gratuit à un moment donné. il va charger 2 piastres de l'heure. Mais si je mets 4 piastres de l'heure, il va charger 2 piastres de l'heure.
Gabriel: Ok, ok.
Christophe: Alors, déjà de base, dans le code c'est écrit, ben on met ce montant-là si on va dire il n'y a rien d'écrit ou le monde ne choisisse pas. Alors c'est 2 piastres de l'heure. Mais il y a d'autres, il y a d'autres places qu'il fallait que, il y a une association qui manque quelque part là.
Gabriel: Ok, ok.
Laurent: Moi, ce que je me demande, c'est est-ce que tu aurais besoin de quelqu'un dès maintenant ou rapidement?
Gabriel: Ouais.
Laurent: Tu sais, à temps plein, à temps partiel. On va faire une évaluation complète de tout ce qu'il y a à faire. Il y a des choses à faire, il faut qu'elles soient faites. On se tape pour un 6 mois à temps partiel, 20 heures par semaine.
Christophe: Comme j'ai dit, je ne sais pas si ça va me prendre 20 heures par semaine. Est-ce que j'ai besoin d'en avoir 20 heures par semaine pour faire tout le projet et après 6 mois c'est fini? Ou peut-être que je vais en avoir besoin d'un 5 heures par semaine et en un an ça va être fini tout le projet d'AOAZ et on va y aller par bloc. Ou demain matin, il y a 100 000 qui me tombent dans les mains et je suis comme, finis-moi le don dans 3 jours.
Christophe: Je sais qu'il n'est pas possible, mais tu comprends ce que je veux dire.
Gabriel: Autrement dit, c'est assez flexible. Si on te propose quelque chose qui fait du sens, tu vas vouloir aller de l'avant. Puis en ce moment, tu as quand même quelques petits besoins sporadiques. Moi, c'est sûr que pendant mon analyse, je peux adresser les points que tu me mentionnes. Je vais aller m'intéresser, entre autres, à l'histoire de l'intégration de POS et du 2$ hard-coded.
Gabriel: Il y a comme une place sûrement à quelque part que même si tu changes l'affichage ou la propriété ou l'option, ça ne se rend pas jusqu'au POS et donc c'est tout le temps chargé à 2$. Anyway, il faut que j'aille lire le code, il faut que j'aille voir une couple d'affaires, je vais te sortir des infos pour ça. C'est sûr que si je peux te corriger des choses en même temps avec ça, tant mieux, mais je ne peux rien te garantir. Mais idéalement, je vais essayer de trouver ce genre d'affaires-là puis de sortir l'info.
Gabriel: Essentiellement, c'est pas mal tous les points qu'on a mentionnés précédemment, mais tu n'as pas des enjeux spécifiques. Il faut absolument que tu adaptes le back-end pour des nouvelles fonctionnalités. J'en avais un, mais mon gars l'a.
Christophe: Programmé en un heure. C'est dans le fond, en passant ça c'est de quoi que mon back-end il a l'air. Quand on va dire je fais des modifications pour les stations. Puis tu sais, je veux dire là, on va dire je vais modifier ça, je m'en vais dans mes settings, puis il m'a acheté cette feature-là.
Gabriel: Oui, screen reuse, ok, parfait. Puis ça, à ce moment-là, c'est celle-là qui ne fonctionne pas, si je comprends bien.
Christophe: Non, il marche.
Gabriel: Ah, ok, ok.
Christophe: Il marche. Je veux dire, en fait, le système, tu vas pèser dessus, il va dire, je n'ai pas de batterie disponible, mais il y en a une qui va débarrer.
Gabriel: Ah ok, ok, je comprends. Donc c'est l'affichage qui n'est pas correct. Mais dans le fond, tu n'as pas dit que ça allait tout le temps te charger deux dollars.
Christophe: Oui, oui, non, mais ok. Ça, c'est un feature de... Ok.
Gabriel: Allons-y.
Christophe: Le POS est arrivé. Le POS est arrivé. On le testait, ça ne marchait pas. Ça me chargeait toujours deux piastres. C'est comme, ok, tu as acheté la batterie pour deux piastres. J'étais comme, ben là, tu ne peux pas faire ça. puis on a été regarder dans le code. Puis là, on a tout ajusté le code pour justement qu'il charge par rapport à ce que j'écris en arrière. Alors, si je modifie à deux pièces, ça va apparaître deux pièces. Ça va prendre les dépôts puis tout. Alors là, ça, ça marche.
Christophe: Cependant, c'est comme là, quand on arrive pour la facturation, quand la personne remet la batterie, il va toujours avoir un montant de base. Quand tu vas voir le code ici. C'est ici les routes, les API. Callback, Stripe. Ça commence à être pas pire. Ah non, il faudrait que je regarde ça. OK, attends, il n'y a pas les prix dedans. Mais tu le vois comme, il envoie les payments et tout, blablabla. Là, il faut que j'aille aux payments. Euler, Rent.
Christophe: Puis à un moment donné, je ne me rappelle plus c'est laquelle, il dit de base c'est écrit deux piastres de l'heure. Puis plus bas tu vois, on doit aller voir l'information pour vraiment avoir la bonne information. Je ne sais pas que je trouve c'est où par exemple, parce que là je m'excuse.
Gabriel: Non, c'est correct, mais si jamais tu as des détails que tu veux me partager, tu peux m'envoyer plus de détails par courriel plus tard, par après l'appel aussi, on n'est pas obligé de faire ça.
Christophe: Non, mais comme j'ai dit, c'est vraiment...
Gabriel: Parce que dans le fond, je suis allé voir aussi le fichier serveur.ts que tu m'as mentionné, je viens d'accepter l'invitation, puis je regarde, bon, en ce moment, ça check une valide cable type, ça check aussi, est-ce que tu as déjà un ongoing order, puis tu as des... Mais essentiellement, d'ailleurs, j'imagine que tu es à l'aise à ce qu'on utilise des outils d'intelligence artificielle dans le code base. Je pourrais déjà faire une recherche pour expliquer les différentes étapes du fonctionnement.
Christophe: Ici, tu vois, c'est comme, il y a les images, mais je n'arrive pas à trouver l'image pour remplacer l'engrenage par une nouvelle image.
Gabriel: Ok, moi ça je pourrais à la limite donner les instructions. Ça, ça m'inquiète moins pour la partie affichage. Mais tu sais, le truc que je suis curieux en ce moment, c'est si ton bas que tu mentionnais du 2$, si en ce moment ça affiche pas les bonnes informations ou ça charge puis ça devrait pas charger dans le fond?
Christophe: Ça affiche pas les bonnes informations.
Gabriel: Ok.
Christophe: Parce que, tu sais, je veux dire, si on va dire l'information est de zéro, il va charger 2$, mais si l'information est de 4$, il va quand même charger 2$. Alors, pour faire ça correct, je mets deux piastres.
Gabriel: Donc, il y a vraiment un problème en ce moment quand même, que quelque part, ça bille tout le temps deux piastres, mais si tu le mets gratuit, ça fonctionne.
Christophe: C'est ça. En fait, tu paies sur gratuit, tu fais l'occasion, puis il va dire, il y a un message, il dit, il n'y a aucune batterie disponible, mais il y a une batterie qui va débarrer.
Gabriel: Ok. En tout cas, ça, clairement, on peut l'adresser pour l'améliorer.
Christophe: Pour être honnête, l'instant, ce n'est pas.
Gabriel: Un si gros problème.
Christophe: Mais si je pèse dessus, ça, ça ne requiert pas de dépôt. Ok. Donc, c'est ça, c'est un features demandé par un client. Il va payer très cher pour avoir ces features. Alors, je suis très content.
Gabriel: Bon, ben tant mieux. C'est parfait.
Christophe: Ok, excellent. Mais là, c'est ça, je n'arrive pas à te faire rentrer là-dessus, je ne comprends pas.
Gabriel: Ouais, ça fait que ça, c'est dans SupaBase. Dans le fond, dans SupaBase, j'ai fait une petite recherche. Dans le fond, il y a un truc de Identity Linking puis de Access Control. Donc, peut-être que sinon... Aller voir. Sharing access to SuperBase project for collaboration, inviting, post-project score, sharing access to application. You can invite other users to collaborate on your SuperBase project by adding them to your organization's team settings within the SuperBase dashboard. C'est vraiment dans les team settings.
Christophe: Ok, donc.
Gabriel: Ce n'est pas dans un projet, il faut que tu retournes, clique sur l'icône en haut à gauche complètement. Ça va être à partir de là. Puis là, ici, normalement, t'es dans une organisation. Puis là, c'est là que t'as Team. Et voilà, c'est là.
Christophe: Good.
Gabriel: C'est tout à fait différent d'une plateforme à l'autre. Exact. Puis ouais, ça c'est parfait. Je vais configurer mon SSO. Good. Si tu veux faire le ménage dans tes accès, c'est parfait.
Christophe: C'est parce qu'il m'a dit qu'il allait m'aider. Il a pris deux semaines pour évaluer, mais il faisait ça à temps perdu. Il ne trouvait pas c'est quoi les problèmes. Puis, je suis dans mon événement. Mon ami m'a aidé il y a trois jours de ça. Comme t'es en retard.
Gabriel: Ouais, c'est ça. Timing is everything.
Christophe: Puis, il était comme, si t'as pas besoin de moi, c'est correct.
Gabriel: Ah oui, ça va. De toute façon, tu pourras réinviter au besoin. OK, ça, c'est bon. Sinon, tu mentionnais Twilio, effectivement.
Christophe: C'est ça, Twilio fait les vérifications de mes messages textes. OK, parfait. Puis, j'ai rajouté quelque chose d'un petit peu plus cool aussi. Parce que j'ai un service client téléphonique. d'IA. J'ai connecté ça et à date, ça marche. J'ai tous les liens pour les messages textes de confirmation, puis le même numéro de confirmation. Le même numéro pour confirmer. Excuse-moi.
Gabriel: Je te laisse te connecter.
Christophe: Le même numéro de confirmation, c'est le numéro d'appel pour l'agent vocal.
Gabriel: Ah, ok, ok. Fait que t'as les deux connectés dans le fond avec la même... Ah, c'est cool ça.
Christophe: Je peux voir c'est quoi les discussions qu'il y a eu avec mon agent vocal.
Gabriel: Ça fonctionne bien en plus avec les...
Christophe: À date, ça marche bien.
Gabriel: Ok, ok. Puis tu t'en sers de quelle manière?
Christophe: Service client.
Gabriel: Vraiment, service client, GPT-1. Ah oui, GPT-4.1, service client, puis t'as un résultat qui est bon.
Christophe: Oui, je pourrais le modifier. C'est nice.
Gabriel: J'ai expérimenté un peu avec, mais je ne l'ai pas plugé en production pour du support, j'ai juste fait des trucs plus advertising.
Christophe: Le support, c'est juste qu'il explique au monde comment ça marche.
Gabriel: Ah, ok, ok, je comprends.
Christophe: Je pourrais mettre un API pour aller régler les problèmes, mais non, ce n'est pas vrai.
Gabriel: Ok, ouais, c'est ça, là, ça commence.
Christophe: C'est comme, je pourrais le faire, là, tu sais, il y a des... Ouais, il y a des setups. Ouais, il y a des setups pour faire des intégrations. Les intégrations sont là, mais tu sais, c'est customisé, ça. Puis mon agent, dans mon agent, c'est comme, il y a des, je pense que c'est les tools. Les tools, tu peux ajouter les tools, que c'est genre, justement, tu peux faire ça, tu peux faire ça. transfert un numéro, tu sais. Là, lui, moi, je peux virer de français à anglais quand je veux avec celui-là.
Gabriel: Ok, parfait.
Christophe: Je peux fermer l'appel, mais c'est comme, c'est des tools de base, mais je peux en rajouter comme, va chercher le numéro de téléphone.
Gabriel: Ah oui, tu peux même y connecter sur un MCP serveur, fait que techniquement... Ah ouais, ok, ok, ok, super. Je l'ai utilisé, mais le logiciel a clairement évolué depuis que je l'ai utilisé. Nous autres, on l'avait juste utilisé sur le projet sur lequel je travaillais pour finalement parler à un utilisateur sur une page web.
Christophe: Ouais, non, c'est ça, ben tu peux, ça marche encore ça, ça existe.
Gabriel: Ah, c'est cool.
Christophe: Mais là, je l'ai eu numéro, là, puis j'ai eu de la misère avec Twilio. Regarde, j'ai comme, ça, c'est mon numéro de téléphone que j'ai avec eux autres. qui m'envoie les numéros de vérification. Mais là, j'en ai un deuxième et j'ai essayé de l'intégrer, mais il n'a jamais marché. Quand j'ai mis celui-là, j'appelle et ça tombe direct. Il y a quelque chose que je ne dois pas avoir fait correctement. Mais en tout cas.
Gabriel: Mais on voit en fait, au niveau messaging, tu vois, dans les deux actifs configuration, il y en a un qui a messaging service one time password qui est seté versus l'autre pas. Il y a peut être une différence.
Christophe: Oui, mais c'est celui là que j'utilise pour le vocal. Le vocal s'en va directement sur Eleven Labs. C'est lui que j'arrive pas à connecter. Je l'avais connecté, je l'essayais et ça ne sonnait même pas. C'était comme si le numéro n'existait pas.
Gabriel: En ce moment, il n'est pas utilisé du tout.
Christophe: J'imagine que tu dois l'exprimer parce que.
Gabriel: Tu dois payer pour.
Christophe: Je l'achète. Tu l'achètes et c'est comme 1,50$.
Gabriel: Une fois que tu l'as, il reste dans ton compte.
Christophe: Oui, c'est ça. Regarde, buy a number. Tu les as ici. Regarde, tu as 1,50$. Il y a des places où c'est un petit peu plus cher. Tu peux checker ce que tu veux. En plus de ça, je l'ai montré à quelqu'un. Un de mes amis, il recevait des fax tout le temps. Il me dit « cherche un numéro pour recevoir des fax ». Merci.
Gabriel: Ben oui c'est ça exact, tu peux le transférer vers du PDF en plus avec les services.
Christophe: Donc c'est ça, donc ça c'est mon Twilio, j'ai mon Cloudflare, j'ai mon...
Gabriel: J'imagine que t'as différents accès à services externes aussi avec les API pour le truc de statistiques là, ça je risque de le voir passer, comme je te dis si j'ai des accès supplémentaires...
Christophe: J'ai pas encore bien programmé, là j'utilise la plateforme de l'autre, à un moment donné je vais vouloir l'intégrer dans la mienne, tu comprends?
Gabriel: Ouais, c'est ça.
Christophe: Est-ce que quand il va y avoir des clients qui vont vouloir les statistiques, ça, ça va arriver dans comme peut-être décembre, genre. Comme décembre, mars, je dois sortir genre les premières preuves de concept. Alors là, il y a des clients qui vont vouloir embarquer sur mes affaires, puis c'est ça.
Gabriel: Ouais. Puis est-ce que, comme là, dans le fond, DisplayForce, en ce moment, je vais aller voir DisplayForce.ai, mais j'imagine que moi, je peux me créer un compte gratuit sur ça pour expérimenter ou... Non. Non, ils n'ont pas de public démo?
Christophe: Non, c'est très... Tu peux regarder les démos, mais je veux dire, c'est très... Faut que tu.
Gabriel: Rencontres quelqu'un, puis... Ah ouais, ok, ils l'ont comme barré. Ça, j'imagine que tu ne peux pas inviter nécessairement de gens là-dessus?
Christophe: Ah oui, je peux inviter.
Gabriel: Ah ok, peut-être que si tu peux m'inviter pour que je puisse, parce que c'est sûr que ça fait partie de, je ne sais pas si le rappel est disponible. Ça va peut-être m'aider entre autres à pouvoir te sortir les infos. Display Force, je vais aller voir Display Force, c'est clair, qu'est-ce qu'ils exposent et qu'est-ce qu'ils ont. Ils n'ont pas de dock.
Christophe: Non, il faut que tu sois dans.
Gabriel: Leur... Ouais, c'est une fois qu'on est connecté.
Christophe: Ouais, faut que t'aies ça là. Puis, j'ai jamais vraiment compris comment ça marche leurs affaires.
Gabriel: Alors, je suis comme, bon... T'es vite à user, direct dedans, puis fin là, c'est comme... Ouais, c'est ça.
Christophe: Il faut que tu choisisses... Bon, je te mets partout comme ça.
Gabriel: On va avoir accès. De toute façon, évidemment, mon objectif, ce n'est pas de foutre le bordel nulle part. C'est bon, je vais aller voir.
Christophe: Je vais pouvoir regarder. Comme je dis, c'est juste comme aller chercher les infos. Mais si je voudrais les extraire, je peux le faire. On va dire Visitor Insights puis SIN Report. Je peux les extraire.
Gabriel: OK. OK, en ce moment, c'est ça, tu peux le faire comme manuellement comme ça. Je vais juste pouvoir te confirmer avant qu'on se quitte, comme ça. Yeah, Cloudflare a dit que j'étais un humain. Tout va bien encore. Login.
Christophe: T'as pas besoin de AWI.
Gabriel: Effectivement, peut-être tôt comme ça, peut-être pas, mais ça risque d'arriver plus tard.
Christophe: Avant que je n'oublie, j'ai aussi un site web. C'est WordPress. Toutes mes affaires sont dans des sous-domaines de mon domaine.
Gabriel: Les services sont hébergés sous le domaine. Tu avais mentionné Route 53, j'imagine, pour les domaines, c'était ça?
Christophe: Oui, c'est ça, Route 53.
Gabriel: C'est parfait.
Christophe: Je vais avoir besoin, si c'est possible, d'un développeur web, puis d'un développeur UX.
Gabriel: Ouais, on a déjà.
Christophe: Pour la page, le site web. On veut, parce que là, il est correct. Et donc, il faudrait, j'ai quelqu'un de Brand qui va s'occuper de ça.
Gabriel: OK, OK. C'est sûr que nous autres, l'expertise qu'on a chez TLM, c'est beaucoup, évidemment, applicatif, tu sais, puis gestion de données, puis ces affaires-là.
Christophe: Je suis prêt, je suis avec quelqu'un d'autre au cas où, sinon.
Gabriel: Ouais, mais par contre, on a un expert UX, mais est-ce que c'est la bonne personne pour le... En fait, on a même deux personnes qui ont développé de l'expertise là-dedans. Mais est-ce que c'est les bonnes personnes pour, justement, le Publicly Facing Website, avoir, parce que je veux dire, il y a vraiment des firmes qui font des trucs vraiment hot avec des app web, pas des app web, mais genre les pages web publiques, les publicly facing pages. Puis WordPress est une technologie que certains aiment, d'autres aiment moins. En tout cas, bref. C'est sûr.
Christophe: Tu peux voir que.
Gabriel: Ben l'essentiel il est là après ça, tu sais, si t'as du contenu, t'as une présence web, puis, mais tu sais peut-être que ce que ça prend en réalité c'est une stratégie de conversion, si tu désires plus de clients, puis à qui tu veux parler.
Christophe: Bon non, ben ça sinon, comme je dis, t'as raison là, mais c'est juste comme que ça a l'air plus beau là, parce que tu vois comment c'est.
Gabriel: Comme... Ouais, ben c'est, au moins t'as quand même le data qui est là, fait que pour le référencement t'as une base, mais c'est sûr que ça peut t'améliorer.
Christophe: Bon non, c'est ça. Alors c'est pour ça que Et tant qu'à ça, je vais aller voir jusqu'à où on peut aller en extension.
Gabriel: OK. Mais oui, effectivement, si c'est des... C'est sûr que si t'avais rien eu, on aurait pu faire un truc de base, mais c'est sûr que moi, je vais te recommander personnellement d'aller vers une firme qui font des trucs hot, puis que tu peux déjà voir le bel job qu'ils ont fait, puis t'affier là-dessus. Nous autres, on a fait quelques petits trucs, mais notre core business, c'est vraiment dans le développement.
Christophe: Oui, c'est pour ça que je te demandais.
Gabriel: Par contre, si c'est tout le côté UX de comment les utilisateurs utilisent, c'est comme l'app visuelle, le front-end app, ça par exemple, c'est là que l'expertise d'un gars UX, en fait de notre équipe UX maintenant, c'est vraiment là que cette personne-là peut intervenir davantage. C'est vraiment le côté genre le placement des boutons pour que les gens comprennent facilement c'est quoi la prochaine étape quand ils ont emprunté la batterie ou pas, tu sais, puis de s'assurer que les étapes soient vraiment simples, c'est ça. S'il y a un besoin là.
Christophe: Ça serait pas pire de regarder ça.
Gabriel: OK.
Christophe: Puis à un moment donné, si on va dire que quelqu'un publie de quoi sur ses réseaux sociaux et que l'application est comme connectée aux réseaux sociaux, comme à partir de là, on peut donner des rabais, genre une heure gratuite, des trucs comme ça.
Gabriel: OK, ouais, on a peut-être des features d'intégration. Ça, ça serait, ça serait à voir. Effectivement, il y a des petits projets techniques par rapport à ça, mais c'est intéressant.
Christophe: Je t'en parle parce que, tu sais, à un moment donné, si je te dis pas comment je veux que ma maison soit haute, tu vas peut-être bâtir une maison à un étage, puis c'est comme, ben, ils sont où les quatre autres? Ah ok, ben minute là, il va falloir que je débolisse une partie de cette section-là. Non, c'est ça.
Gabriel: Mais ça me pointe bien. Je vois un peu vers où est-ce que tu veux te diriger. Autrement dit, c'est fournir une belle expérience aux utilisateurs aussi pour que ce soit facile pour eux de prendre les batteries et que tu veux le plus d'incitatifs possible à ce que ça se propage un peu, que ce soit un peu plus organique. Tu as ce côté-là, clairement, si tu vas vers les analytics, tu as ce côté-là, vente, qui est clairement assez fort en toi.
Christophe: Et le pire là-dedans, c'est que moi, ça fait juste un an et demi que je suis là-dedans.
Gabriel: Oui. C'est bon, écoute, tu t'es rendu loin, je trouve.
Christophe: Ça fait trois ans que l'entreprise est ouverte, mais ça fait un an et demi que je suis plus à temps plein là-dedans. Je travaille pour justement que ça sort correctement. Je suis pas mal sûr que j'ai fait mon tour.
Gabriel: Oui, oui, écoute, on a couvert très, très large. Comme je te dis, si j'ai des besoins supplémentaires, comme pour le côté AWS, on verra quand je serai rendu là, je pourrai te redemander des accès si c'est pertinent. Mais en fonction de ce que je vais voir là, si c'est impossible de faire tourner les logiciels en local, puis que j'ai absolument besoin du serveur sur AWS, je te ferai signe en ce moment là aussi. Mais ça m'empêchera pas de lire le code et de comprendre.
Christophe: Moi, on me dit qu'en local, ça a déjà marché.
Gabriel: Ah bon ben regarde excellent, tu vois ça m'enlève déjà un risque.
Christophe: C'est l'autre le fait, pendant qu'il faisait le nouveau features, il l'a mis en local, puis il m'a dit je vais mettre ça en local. Je suis comme ok, j'enregistre ce qu'il dit. Il est comme ok, je teste là, comme pouf, ça déborde. Il est comme ouais, ok, ça marche.
Gabriel: Parfait, excellent. Puis moi, je vais jeter un oeil aussi. Dans le fond, j'ai vu qu'il y avait des petits pipelines automatisés. Il y avait des crochets rouges versus verts en haut des pages. Je vais aller jeter un oeil là-dedans.
Christophe: C'est des modifications aussi que j'ai faites.
Gabriel: Que je n'étais pas... Ouais, c'est ça, mais exact. Est-ce que c'est vraiment les modifications que t'as faites qui a brisé quelque chose ou non? Puis jusqu'à quel point ça... Je pense qu'il y a comme une.
Christophe: Ligne que j'ai effacée, que je voulais laisser ça blanc, genre.
Gabriel: Mais tu sais, j'ai l'historique, moi je suis capable de revenir dans le temps et de voir. En fait, ce que je veux dire, c'est que je vais aller vérifier l'état de la situation par rapport à est-ce que tu es couvert avec des tests? C'est quoi le niveau de qualité et d'assurance que tu as dans le projet? Puis je vais te mettre des recommandations par rapport à ça. C'est ma job. C'est mon expertise.
Christophe: Oui, je suis sûr que tu la fais bien aussi.
Gabriel: Yes, ah oui, j'aime ça.
Christophe: C'est ça. Donc, vas-y là-dessus. Écoute, moi, j'ai plus rien d'autre à te dire. À part que l'expérience admin est à chier, on parie... Ah, écoute, je vais te donner un exemple. Regarde, là.
Gabriel: Oui, vas-y. T'as mon intérêt tout d'un coup. Je suis juste curieux.
Christophe: OK, regarde. Le user. Comment tu veux que je les identifie? OK, tous les numéros sont là, mais ils ne sont pas là.
Gabriel: Je ne vois plus ton écran à ce moment-là, Vas-y.
Christophe: Ah, excuse-moi.
Gabriel: Ouais, vas-y.
Christophe: Excuse-moi.
Gabriel: Ouais, non, pas de problème. On lâche fin, on est sur la fin pas mal.
Christophe: Tu regardes, mes users là, ils me donnent tous les numéros comme ça, mais c'est les numéros de téléphone que j'ai besoin. Ok, je décide d'y aller comme ça, mais je n'ai pas toutes les dates. Si j'arrive à côté, c'est comme on dirait qu'ils classifient genre dans ce qu'il y a sur la page et pas genre comme je m'en vais à côté. Regarde, tu vois, ils les ont replacés, mais c'est comme juste les dix, pas toutes les données.
Gabriel: Ok, ok. Dans le fond, autrement dit, ça, c'est quelque chose que tu aimerais faire en sorte, tu sais, est-ce que tu veux qu'on améliore un peu cette page de users là? Parce que là, tu sais, maintenant, t'as 123 users.
Christophe: Ça, c'est moi, là, comme en arrière, là, comme qui gère ça.
Gabriel: Ok, ouais.
Christophe: C'est pas une priorité. Je peux vivre avec ça.
Gabriel: Ok, je comprends.
Christophe: Mais tu sais, je veux dire, comme on va dire, j'ai un problème avec une station. Je m'en vais sur la transaction problématique. Puis là, il me sort ça. Comment tu veux que je identifie la personne?
Gabriel: Ouais, ouais, ouais. Potentiellement pas assez d'informations exposées nécessairement dans ta gestion d'users ou d'orders.
Christophe: C'est ça, juste me dire c'est quoi le numéro de téléphone. C'est comme, OK, partie de là. Mais là, à la place de ça, il faut que j'aille dans l'user, puis que je fasse ça, puis que j'essaie de le trouver. Puis ah, c'est le dernier utilisateur. Puis là, je clique dessus. Ah, ben oui, c'est la personne. Ah non, c'est pas la bonne. On revient au début. Ah, c'est tout remis à zéro.
Gabriel: Ça, potentiellement, c'est les gens qui ont loué des batteries, mais que tu n'es pas capable d'aider. Parce que tu n'as pas nécessairement tout le temps le courriel, tu n'as pas d'informations, tu as juste le phone number.
Christophe: Le seul temps qu'ils vont mettre un courriel, c'est quand on va dire qu'ils ont des problèmes. Parce que dans le FAQ, Il a signalé un problème, mais pour signaler le problème, on demande un courriel.
Gabriel: C'est ça le moment où... OK, c'est là qu'il est capturé, je comprends. OK, OK, excellent. Puis sinon, au niveau de la capture d'erreur en soi, qu'est-ce qu'il se passe de quoi de pas correct en termes d'utilisation, si tu as identifié ou non l'utilisateur, tu n'as pas de gestion de ça présentement. C'est comment tu fais pour savoir s'il y a eu un problème entre autres présentement, c'est tu, ils t'appellent? Comment ça marche?
Christophe: Ben, généralement, j'ai un. Tu vois, mon numéro n'est même pas enregistré, je ne comprends même pas pourquoi. J'ai un courriel. Qui peuvent m'envoyer des affaires, puis c'est service point, c'est mon Microsoft service point client.
Gabriel: Ok, fait que c'est vraiment juste comme eux autres qui peuvent passer par le site, ils disent j'ai un problème, puis c'est tout. T'as pas d'informations plus techniques qu'il faut ou de traces d'utilisateurs de qu'est-ce qu'il a fait ou de... C'est-tu un besoin que t'as aussi dans les choses évaluées ou genre ça peut attendre? C'est à quel point en ce moment?
Christophe: Honnêtement, si on va dire que je suis comme ok, on a tout fini, puis genre... En passant, je t'envoie le courriel de display avec les informations comprimées.
Gabriel: Ok, parfait.
Christophe: C'est ça, ce n'est pas une urgence, mais je veux dire que c'est gossant.
Gabriel: Ouais, ouais, ouais.
Christophe: OK.
Gabriel: Ben écoute, c'est sûr que si on peut adresser plein de petits irritants comme ça pour simplifier ta job aussi, ça, ça peut être très intéressant. Nous autres, si je veux dire, si on te met quelqu'un qui est à quelques heures par semaine et qu'il passe au travers des différentes tâches et qu'on implémente les choses, si il peut adresser des irritants, c'est toujours mieux.
Christophe: Puis avant que je... Aussi, puisque j'y pense, une fois que je vais avoir un nouveau programme, puis tout ça, tu sais, quand je vais m'être débarrassé des Chinois, je vais pouvoir avoir un remote access à tous les écrans et toutes les stations aussi. C'est comme toutes mes stations, il faut qu'elles soient basées sur Linux. On trouve justement des motherboards et des CPU, des GPU pour pouvoir analyser les vidéos plus rapidement pour le programme. Mais je vais pouvoir, si j'en ai à des endroits fixes, je vais pouvoir régler les problèmes à distance. Ok.
Christophe: Puis, je veux comme, je veux qu'il y ait des affaires qui me flaguent, comme il y a quelqu'un qui a décidé de débrancher une machine. Il faut que par hasard, je passe par là et que je regarde, hey, la machine est débranchée, pourquoi?
Gabriel: Ah, ok, ouais. Fait que t'as pas du reporting nécessairement de tes machines actives, ces choses-là. Fait que ça, c'est quand même un besoin finalement de monitoring de différentes stations déployées. En ce moment, ça, c'est pas en place.
Christophe: Zéro.
Gabriel: Ok, parfait.
Christophe: Je figure it out.
Gabriel: Excellent. Ça, je comprends très bien. Écoute, on a des projets, disons similaires de gestion de hardware qu'on a eu à faire des trucs similaires. C'est quand même un classique de genre, j'ai une flotte de machines puis je veux savoir ce qui se passe avec mes machines. Ça fait que ça, c'est un besoin.
Christophe: C'est ça. Je veux juste savoir comme tu sais, des fois, le POS, il est en ligne puis l'écran, il peut en ligne. Ou vice versa. L'écran, il est en ligne, mais le POS, il a surchauffé puis il est en stand-by.
Gabriel: Oui, c'est ça. Autrement dit, je veux dire, tu as un status report de toutes tes machines au complet, qui est connecté, de tous les appareils. Tu arrives sur le dashboard, tu as accès à toutes tes infos. Ça, je comprends bien. Juste avant qu'on skippe, la dernière petite chose qui m'intéresserait, parce qu'on a parlé quand même de pas mal d'affaires. puis je vais me refier au recap, effectivement, de la bille.
Gabriel: Mais tu sais, toi, mettons en priorité les choses qu'on a discutées, celles qu'on devrait adresser comme en premier, parce que quand même, ça va aussi orienter, moi, les efforts que je vais aller mettre. C'est sûr que, tu sais, première chose, je vais tenter de répondre à la question concernant les captures d'informations de code postal sur les transactions POS.
Christophe: C'est mon gars de IT qui gère tout ce qui est problèmes, juste IT général. Il vient de me taguer sur le quoi que je lui ai demandé tout à l'heure. Oui, désolé, répète-moi ça. Mes priorités. Première priorité, je te dirais, quand tu fais l'analyse, si tu pourrais régler l'espèce de word que je t'ai envoyé, tu vas me rendre très heureux.
Gabriel: Oui, ça, il n'y a pas de problème. Je le prends. Je vais regarder ça.
Christophe: Après ça, ça serait comme commencer à merger, genre comme aller chercher le dashboard de l'autre, puis de faire des API, puis de merger ça dans une plateforme où est-ce que je peux avoir un meilleur contrôle. Après ça, ce serait.
Gabriel: C'est tout.
Christophe: Comme ça, c'est vraiment les deux grosses. L'API, l'API pour le POS. Si t'es capable de m'avoir ça.
Gabriel: L'API de POS, ah oui, c'est la capture d'informations de code postal.
Christophe: Ouais, c'est les trois affaires urgentes, je te dirais. Plus l'API, je te dirais, si t'es capable de m'avoir ça en premier, premier, premier ou en même temps. Et si tu me dis, ah oui, c'est possible. ben, vas-y, commence à le développer tout de suite. C'est carrément ça.
Gabriel: OK, c'est bon. Ben écoute, moi, c'est sûr qu'avec Laurent, on va regarder aussi qu'est-ce qui est possible de faire en lien avec ça. Puis, c'est de revenir avec qu'est-ce qu'on te propose. Parce que c'est sûr que tu sais, même travailler avec des API de POS, je veux dire, on va regarder qui est disponible déjà chez TLA, puis qui est à l'aise avec ces affaires-là. Je ne sais pas à quel point il y en a du monde. Souvent, c'est du monde occupé.
Christophe: Je ne sais pas à quel point on peut aller loin, mais ce n'est pas nécessairement obligé d'être un API. Ça peut être un système d'extraction de données. Je ne sais pas. Moi, si tu me dis que j'ai besoin de télécharger de quoi dans une machine, puis tu vas avoir les codes postales après ça. Moi, ça me convient.
Gabriel: OK.
Christophe: C'est surtout aussi si tu es capable d'avoir comme, on va dire une transaction est associée à une carte de crédit qui est associée à un recueil postal. Si tu as le montant de la transaction et le recueil postal ensemble. Moi, je peux me revirer de bord puis de donner des stats en disant genre comme bien 73% de tes clientèles, c'est du local, mais 45% de tes revenus viennent du touristique.
Gabriel: OK, c'est sûr que c'est ça, c'est plus facile quand c'est toi qui contrôle qu'est ce qui est vendu. Fait que là, si c'est pour utiliser des POS d'autres commerçants qui doivent partager des accès, c'est là que c'est tricky un peu.
Christophe: C'est plus rendu là, ça va être des commerçants de d'autres. Mais moi, je veux comme. Et je veux juste leur donner accès à une plateforme. Il y aurait accès à la plateforme, mais il y aurait juste accès au dashboard qui dit genre, ben voici qu'est-ce qui s'est passé dans ton, dans ton POS pour l'année.
Gabriel: C'est ça, c'est ça, exact. Mais ouais, ouais, ok, je comprends.
Christophe: Puis, à partir de là, ben moi, j'ai des ententes avec eux autres pour voir si je peux utiliser ces données-là ailleurs. OK. Et si ils m'autorisent, ben, je veux pas que ça soit extractable, celle-là, par exemple. OK. C'est comme, je veux pas qu'il puisse, il peut juste faire comme imprimer la page genre, puis ça finit là. Mais je veux juste pas qu'il ait les données parce que moi, je me reverrai de bord puis je peux le vendre à d'autres organismes, d'autres situations genre.
Gabriel: Ok, parfait. Ben, ça marche. Écoute, moi, ça, je vais partir pour aller au moins à la prise d'informations. Comme je te dis, je ne peux pas rien garantir sur celui-là parce que j'ai aucune idée.
Christophe: Ouais, non, je sais. C'est touché, mais le fait que, comme tu vois, je suis très créatif parce que plus tu me donnes d'informations, plus je les absorbe. Et là, je suis comme, hey, j'ai besoin de ça. Je suis comme, hey, tu pourrais faire ça comme ça, comme ça, comme ça ou comme ça. Et t'es comme, what the fuck? Comment tu sais ça? Parce que j'ai gossé pendant trois mois avec Stripe et ça me faisait chier. Alors, j'ai appris qu'il y avait des codes postales associés aux cartes de crédit.
Gabriel: Ouais, pis là tu peux t'en resserver de ça comme information, mais c'est bon, ok. Fait que ça, je vais explorer ça. Bref, en termes de priorité, on dit capture de transaction POS, et puis identifier, ben en fait, localisation si on veut, somehow, plus les montants, fait que pire, c'est la sortie du reporting. Par rapport à ça, pour entre autres, pis ça ton target, c'est principalement un festival ou tu vises autre chose, c'est-tu...
Christophe: Pour eux autres? Ouais, pour ça, c'est-tu... Non, ça va être tout, là.
Gabriel: Ok, target plus large, c'est tout.
Christophe: Plus haut et plus large. Si on va dire qu'une épicerie veut me dire j'achète ton programme parce qu'il veut avoir ses stats par rapport à qui il consomme ses produits localement ou plus en visite, ça va avoir un impact. Ou s'il veut dire, les 25% des visiteurs que j'ai eu, 80% des visiteurs sont européens, alors je vais peut-être importer certains produits européens pour eux autres.
Gabriel: Ok, ça c'est bon. Je vais regarder à quel point on est capable de faire un truc qui se place devant les POS de manière générique pour baser sur tes infos-là. Après ça, puis ça, cette affaire-là, je veux dire, tu veux développer une app vraiment comme 100% à part de ça ou c'est moins relié directement avec la plateforme de charge en tant que telle?
Christophe: Ben en fait, je le mettrais dessus juste pour comme avoir un dashboard là, pis que c'est facile à gérer.
Gabriel: T'as quand même, fait que le but, c'est que justement, ça peut être un truc qui vit de part entière, mais que ça peut être utile quand même, même directement toi avec les plateformes, justement.
Christophe: C'est ça. Pis t'sais, on pourrait aussi avoir des options pour désactiver ça. On va dire que je suis dans un des événements spécifiques, puis il y a des commerçants qui viennent. Les commerçants, s'ils viennent à l'événement, ils n'ont pas le choix de mettre ces applications-là. Mais après l'application, ils peuvent juste l'effacer, genre. Pas l'effacer, mais comme barrer les communications.
Gabriel: Excellent. Donc, bref, ça, c'est le point que tu veux avoir les réponses pour ça et que c'est parfait. Quand on parlait tantôt de merge d'informations, donc deuxième priorité, mettons, dans le fond, en fait, tu as déjà, c'est sortir les informations de la plateforme Display Force vers ton dashboard. Puis après ça, avoir une place pour.
Christophe: Avoir un dashboard et tout.
Gabriel: Oui, c'est ça, c'est ça que ça vient avec.
Christophe: Et trouver de quoi, puis là, c'est là que ton gars du UX pourrait être pas pire, là, parce que, tu sais, je dois avoir un truc média, genre, parce que c'est de la gestion soit médiatique ou analytique. Alors, il faut que ça soit comme, que ça soit facile à utiliser pour le gars qui décide qu'il embarque dans mon affaire. Puis là, il est comme, comment ça marche?
Gabriel: OK, parfait. Puis là, on avait parlé un peu aussi même, en fait, on a découvert plein d'affaires. Il y avait le côté self-service aussi, configuration de l'événement, puis le côté ad, le côté, pardon, ajout de système publicitaire pour tes écrans, que ça, c'est un autre jeu.
Christophe: Ça, c'est fait à travers le display. C'est comme, parce que le display, c'est les écrans et l'analytique. Mais des fois, parce que ça coûte cher. Alors, des fois, je n'embarque pas avec eux. Des fois, j'embarque avec les Chinois. Alors, c'est juste un des trucs récurrents, facile à faire. Je vais embarquer avec les Chinois et je n'ai pas besoin d'analytique parce que c'est inclus dans mes affaires.
Christophe: Mais au pire, avoir de quoi pour faire la gestion et juste pouvoir mettre des trucs sur mes écrans sans devoir nécessairement passer par Display ou l'autre, puis d'avoir un meilleur contrôle genre sur qu'est-ce que je peux afficher genre.
Gabriel: OK, OK. Ça c'est bon. Puis après ça, en termes de... bref, c'est comme un peu troisième priorité si on veut, mais ça vient avec l'idée de... Est-ce que dans le fond, ton objectif, c'est un peu de sortir de Display Force ou première étape, on peut rester dedans puis juste extraire le data?
Christophe: On va extraire un bout, je te dirais, au moins deux ans. Peut-être qu'à la longue, parce que comme ça m'ouvre les portes de la vulnérabilité, alors j'aimerais ça m'en venir.
Gabriel: Je peux dépendre des autres, mais c'est bon. Je vais quand même regarder.
Christophe: Je t'en MVP.
Gabriel: C'est ça, je le garde en tête. C'est sûr que le plus simple en ce moment, c'est si tu restes là, si ça répond à ton besoin, si on est capable d'extraire la data et que tu la réexposes à tes clients et qu'on vient d'avoir un win.
Christophe: Mon gros problème, par exemple, c'est les Chinois. Je vais me débarrasser des Chinois, ils font chier.
Gabriel: Puis ça, justement, quand tu parles de technologie chinoise, là, as-tu quelques infos que tu peux me partager en lien avec ça? Il est déjà tard, mais dans le fond, c'est un API qui est appelé, puis c'est via le code que ça s'utilise. Toi, tu le configures, puis après ça, ça l'utilise, genre, ou c'est tes machines? Puis ça, c'est plus tes plus petites machines qui utilisent ça ou c'est aussi ta borne?
Christophe: Toutes.
Gabriel: Toutes utilisent ça, OK. Fait que c'est quand même talent, dans le fond, si je comprends bien.
Christophe: Ouais, c'est parce que si j'ai un problème avec, parce que la façon que c'est fait, c'est comme eux, ils contrôlent la borne et après ça, ils me l'envoient dans mon système.
Gabriel: OK, OK.
Christophe: OK, ben si je programme quelque chose, ben ça fuck tout.
Gabriel: OK, OK.
Christophe: Si on va dire qu'il y a quelque chose qui se reset, ils retombent dans leur système à eux. Mais là, si je veux faire ci, je veux faire ça, c'est toujours comme il faut que je gère une partie sur mon système, puis après j'aille sur l'heure des fois.
Gabriel: Oui, je comprends, c'est quand même problématique. Est-ce qu'en ce moment, ces bornes-là, parce que c'est ça, j'imagine aussi peut-être qu'elles pourraient être plus facilement malléables. En fait, je ne sais pas dans le cadre de mon travail, puis pour l'analyse que je vais te produire, je vais avoir besoin nécessairement de cette machine-là, mais est-ce que toi, tu en as différentes, tu en as plusieurs, c'est-tu quelque chose que tu n'as pas accès?
Christophe: J'en ai trois. J'ai trois modèles différents, mais je veux dire, présentement, c'est pas mal tout le même système. Moi, je le regarde en arrière, je les démonte carrément. Alors, parce que, tu sais, je vais te donner un exemple. La carte SIM, elle ne marche pas là-dessus. Pourquoi? Il y a une connexion qui ne marche pas sur le motherboard. Wow!
Gabriel: Ok, ouais, c'est quand même, c'est l'ouvrage par rapport à ça. Puis toi, dans le fond, est-ce que tu planifies de continuer quand même avec ce hardware-là ou j'imagine que ça, c'est quelque chose qui peut évoluer dans le temps avec la gang de garage et console?
Christophe: Oui, c'est ça, on voulait, il y a certains hardware qui vont être modifiés. Comme les écrans, pas mal de choses. On essaie de modifier le plus de choses possibles.
Gabriel: Ça répond déjà quand même à beaucoup de mes questions. C'est sûr que pour le côté, en fait, de l'API, moi, j'imagine que ça se passe aussi dans le cas, dans ce moment, c'est vous autres qui appelez les écoles. Puis après ça, ça fait des autres qui s'occupent de faire les modifications.
Christophe: C'est la même affaire pour le POS aussi avec eux. C'est ça qui me gâche un peu.
Gabriel: Ça, j'imagine que je vais le rencontrer dans le cadre.
Christophe: Ouais. Oh, et tu vas le rencontrer partout.
Gabriel: C'est bon.
Christophe: C'est parfait. C'est pour ça que je suis comme, tu vas le voir là, tu vas le rencontrer partout. Il y a beaucoup d'appels avec eux autres. Puis, tu sais, comme ça me gosse le fait que je dois appeler genre pour le POS. C'est comme, c'est techniquement eux qui gèrent ça, alors je suis comme, fuck.
Gabriel: OK, OK. Ah, dans le fond, ça serait... OK, c'est bon, OK. Moi, c'est ça. En tout cas, si je ne l'ai pas dans le code, je vais voir comment c'est fait. Je vais regarder à quel point on peut changer ça. Mais tu sais, l'idée, c'est qu'il faut recréer un peu ce qu'eux autres font comme plateforme de gestion, mais avec des appareils Linux. Moi, je vais évaluer un peu cette partie-là aussi. Puis ça, en termes de priorité... C'est-tu la chose que tu veux faire le plus possible? Imagine qu'on ne fait rien du reste.
Gabriel: Si on réussit à te sortir de ça et que tu es capable de fonctionner sur une machine Linux de base où tu es 100% en contrôle, ça te redonne ces fonctionnalités-là.
Christophe: Je serais vraiment fucking heureux.
Gabriel: Fait que c'est quand même un gros projet. Fait que tu sais, par rapport à capture de transactions POS, puis mettons extraire le data de Display Force, ça ici, le fait de sortir de la techno, de cette techno là, puis être plus en contrôle, si tu avais à choisir.
Christophe: Non, non, je préfère, je préfère aller chercher le display parce que le, parce que c'est fonctionnel. Mais l'autre, si j'expose mon third party à du monde, on risque de l'utiliser contre moi.
Gabriel: Oui, c'est ça. Mais c'est bon, en même temps, tu sais, tu vois, ça me donne quand même un peu une idée de où est-ce que tu te suis dans tout ça. Puis, tu sais, en réalité, nous autres, à long terme, on peut finir par réussir à faire l'autre étape.
Gabriel: Ça peut être, tu sais, ça peut être itératif aussi, là, où est-ce que ta première version de ta borne, tu restes avec justement ce système-là, puis on pense un coup d'avance à, OK, bien, une prochaine, peut-être une autre génération où est-ce que là, ça rend ça possible, puis de manière évolutive. Ça peut être dans plusieurs étapes.
Christophe: C'est ça que, présentement, on travaille là-dessus pour, comme tu l'as vu, Linux pour tout avoir des systèmes qui... Sur lesquels.
Gabriel: Tu as du contrôle.
Christophe: Display dit aussi, genre, qu'il opère plus vite les analyses sur Linux que sur Android ou Microsoft. Je t'ai envoyé, c'est un gros avantage. On parle de, je parle de 1.5 secondes jusqu'à 3 secondes pour analyser quelqu'un à 0.3.
Gabriel: OK, OK. Bon, super. Puis ça, tu lui laisserais encore la job, j'imagine. Tu n'as pas l'intention nécessairement de partir de là, c'est sûr. Non, c'est ça.
Christophe: Puis surtout, genre, ça coûte quand même cher. Tu as vu les prix.
Gabriel: Oui, c'est ça.
Christophe: C'est par caméra.
Gabriel: OK, quand même.
Christophe: Ouais, mais c'est ce que je veux dire.
Gabriel: Mais tu sais, après ça, si tu reprends ça, puis tu réussisses à le revendre dans ton service, puis qu'il y a réellement de la valeur, je veux dire, tu sais, c'est ça.
Christophe: C'est ça, mais c'est pas juste ça, c'est comme, je suis capable de le revendre, puis de le revendre, puis de le revendre, puis de le revendre, puis de le revendre.
Gabriel: Ouais, effectivement, parce que tu réutilises tes bornes.
Christophe: Ouais, c'est ça, tu sais, il coûte une borne, là, l'avoir pour une fin de semaine, ça coûte cher en tabarnak, là.
Gabriel: Ouais.
Christophe: Et je charge 2000 piastres pour une semaine, pas par écran. Plus si on va dire que tu mets en location les batteries, c'est un autre 1000$. Alors tu payes le service.
Gabriel: OK.
Christophe: Puis si tu veux du data, pour quatre bornes, c'est 5000$.
Gabriel: Ok. Puis en ce moment, as-tu des prévisions de scaling pas mal par rapport à ça? C'est la gestion de demandes? As-tu des enjeux de ventes de ton côté?
Christophe: Pour l'instant, mon seul enjeu, ce n'était pas au moment de l'année où est-ce qu'on pouvait me donner des enveloppes, c'est en septembre, septembre-octobre. À partir de là, je commence à faire des appels. Il y a bien du monde qui m'a dit, rappelle-moi à ce moment-là, parce que c'est à ce moment-là qu'on écrit nos enveloppes. J'ai besoin de tes stats, j'ai besoin de ci, j'ai besoin de ça.
Gabriel: Parfait. Ça m'éclaire énormément. Déjà, pour ta prochaine demande en ce moment, d'avoir de l'information, je vais pouvoir te sortir du data par rapport à ça. Puis, après ça, c'est le côté développement, nous autres, qu'est-ce qu'on peut faire, puis qui est-ce qu'on a disponible, à partir de quand, qui va avoir un impact aussi. Je vais surtout me concentrer sur exactement l'ordre qu'on vient de définir.
Gabriel: La partie, mettons, reverse engineering de l'API, de la technologie, ou si on développe un truc, mettons, from scratch, Ça, je vais moins l'évaluer, mais c'est sûr que ça fait partie du plan qu'il faudrait qu'il soit développé, puis on pourra regarder qu'est-ce qui est possible de faire. Mais ça reste un peu plus flou parce que ça me prendrait plus d'infos, ça me prendrait la borne. Il faudrait qu'on sache exactement avec quel hardware on travaillerait, avec quelle machine. C'est une machine Linux, qu'est-ce qu'on branche dessus.
Gabriel: Je veux dire, écoute, nous autres, on a travaillé sur des systèmes qui automatisaient une partie, puis qui faisaient de la vente à des consommateurs directement. Puis tu sais, après ça, c'est le contrôle de ces machines-là. Puis tous nos dashboards étaient 100% custom. Ça a été long à développer, tu sais. Ça fait que c'est comme, c'est, tu sais, il y a une espèce de range. Puis là, toi, après ça, c'est comme tu injectes full d'argent là-dedans, mettons, mais que finalement, T'en as pas tant que ça ou ça reporte pas assez, ça vaut pas la peine.
Gabriel: Avant, je pense de commencer cet espèce de projet-là, de sortir de ça. Faut être sûr que le modèle, il marche pour que si t'injectes de là-dedans, ça va être la peine. Effectivement, je le laisserais plus loin aussi. Je ne l'analyserai pas tant là, mais c'est bon que je l'aille en tête, que c'est là que ça se dirige. Je vais vraiment me concentrer sur les trois points qu'on a dit. Capture de transaction POS, Merge des informations, Display Force. Après ça, c'est tous les petits correctifs qu'il y avait.
Gabriel: À la limite, même monitoring de flotte, mais ça, ça serait plus facile encore si c'était des machines Linux plutôt que ce que tu as en ce moment qui est utilisé. Si jamais tu as des spécifications par rapport à ce que tu utilises, si tu as des numéros de modèles, ces choses-là, c'est autrement dit. Oui, parfait. Je vais repasser au travers des courriels, je vais me filer toutes ces informations-là et je vais essayer de monter un beau plan. Ça court pas mal.
Christophe: C'est quoi la documentation que je t'ai donnée? Je t'ai donné beaucoup de documentation par rapport à le hardware que j'ai. Ouais. C'est ça. Donc, je t'ai donné l'information sur le hardware, les caméras, puis Il marche sur Unreal aussi. OK.
Gabriel: Parfait. Ben écoute, moi je vais partir avec ça. Si j'ai des questions là, de toute façon, je te refais signe là, il s'en vient déjà un peu tard. Je vois Laurent qui a la main levée.
Christophe: Oui, c'est ça.
Gabriel: T'es-tu appelé pour aller aider à souper?
Laurent: Oui, c'est sûr, j'ai des garçons à côté de moi qui graffignent un petit peu là.
Christophe: Ah oui, c'est moi qui vais le.
Gabriel: Voir s'il veut sortir en plus.
Christophe: Moi je suis, puis moi il faut que j'aille, j'ai tellement d'affaires à faire là. J'ai une, je dois écrire mon prévision financière là, tu sais.
Gabriel: C'est le fun, le fun stuff. All right, bien écoute, on se redonne des nouvelles très bientôt, mais merci pour ce temps-là. Écoute, t'as répondu vraiment à beaucoup de mes questions. Si j'ai d'autres affaires, je te réécrirai. Puis Laurent, il va sûrement communiquer avec toi prochainement pour la suite des choses aussi. On sort beaucoup une petite rencontre.
Christophe: C'est bon.
Laurent: Good, merci beaucoup. C'est vraiment intéressant.
Christophe: Merci à toi. Ouais, j'essaie d'en faire le plus possible, mais à un moment donné, j'ai des limites à ce que je peux faire.
Gabriel: Ouais, mais c'est...
Christophe: Je dirais créatif, mais ça va.
Gabriel: Oui, je vois ça.
Christophe: Bon, mais sur ce, excellente journée.
Gabriel: Merci.
Christophe: Salut.
Gabriel: Salut.
