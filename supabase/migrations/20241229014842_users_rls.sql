GRANT ALL ON TABLE "auth"."users" TO "anon";
GRANT ALL ON TABLE "auth"."users" TO "authenticated";
GRANT ALL ON TABLE "auth"."users" TO "service_role";

DROP POLICY IF EXISTS "Enable read access for superusers" ON "auth"."users";

CREATE POLICY "Enable read access for superusers"
ON "auth"."users"
AS PERMISSIVE
FOR SELECT
TO authenticated
USING (
    -- Check if the user belongs to one of the superuser's organizations
    EXISTS (
        SELECT 1
        FROM public.user_roles AS ur_user
        INNER JOIN public.user_roles AS ur_superuser
        ON ur_user.organization_id = ur_superuser.organization_id
        WHERE ur_user.user_id = users.id
        AND ur_superuser.user_id = auth.uid()
        AND ur_superuser.role = 'superuser'
    )
);

DROP POLICY IF EXISTS "Enable read access for organization members" ON "auth"."users";

CREATE POLICY "Enable read access for organization members"
ON "auth"."users"
AS PERMISSIVE
FOR SELECT
TO authenticated
USING (
    -- Check if the user has placed an order in this organization
    EXISTS (
        SELECT 1
        FROM "public"."orders" AS o
        JOIN "public"."stations" AS s
        ON o.station_id = s.id
        JOIN jsonb_array_elements_text(auth.jwt() -> 'organization_ids') AS org_id
        ON s.organization_id = org_id::uuid
        WHERE o.user_id = "users".id
    )
);