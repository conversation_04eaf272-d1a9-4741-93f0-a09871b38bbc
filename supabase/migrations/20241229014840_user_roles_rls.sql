DROP POLICY IF EXISTS "Enable read access for superusers" ON "public"."user_roles";

CREATE POLICY "Enable read access for superusers"
ON "public"."user_roles"
TO authenticated
USING (
    -- Check if the user_role entry belongs to the same organization as the superuser
    EXISTS (
        SELECT 1
        FROM jsonb_array_elements_text(auth.jwt() -> 'organization_ids') AS org_id
        WHERE org_id::uuid = "user_roles"."organization_id"
    )
    AND
    -- Check if the requesting user is a superuser
    EXISTS (
        SELECT 1
        FROM "public"."user_roles" AS ur
        WHERE
            ur.user_id = auth.uid() AND
            ur.role = 'superuser'
    )
);