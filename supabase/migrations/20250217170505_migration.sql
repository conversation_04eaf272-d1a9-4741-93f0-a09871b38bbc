create or replace view orders_with_unresolved_end with (security_invoker=on) AS
with orders_with_battery_recently_synced as (
  select o.*
  from orders o
  inner join station_slots ss on ss.battery_id = o.battery_id
  where o.status = 'ongoing'
  and type = 'rental'
  and o.ended_at is null
  and ss.synced_at > o.started_at
),
orders_with_battery_in_new_order as (
  select o.*
  from orders o
  where o.status = 'ongoing'
  and type = 'rental'
  and o.ended_at is null
  and exists (
    select 1
    from orders o2
    where o2.battery_id = o.battery_id
    and o2.id != o.id
    and o2.created_at > o.created_at
  )
)
select *
from orders_with_battery_recently_synced
union
select *
from orders_with_battery_in_new_order;