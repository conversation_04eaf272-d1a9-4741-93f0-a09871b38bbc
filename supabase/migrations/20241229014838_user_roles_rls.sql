DROP POLICY IF EXISTS "Enable read access for superusers" ON "public"."user_roles";

CREATE POLICY "Enable read access for superusers"
ON "public"."user_roles"
TO authenticated
USING (
    EXISTS (
        SELECT 1
        FROM "public"."user_roles" AS ur_superuser
        WHERE ur_superuser.user_id = auth.uid()
          AND ur_superuser.role = 'superuser'
          AND ur_superuser.organization_id = "user_roles".organization_id
    )
);