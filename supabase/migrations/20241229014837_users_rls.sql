DROP POLICY IF EXISTS "Enable read access for superusers" ON "auth"."users";

CREATE POLICY "Enable read access for superusers"
ON "auth"."users"
TO authenticated
USING (
    -- Check if the user belongs to one of the admin's organizations
    EXISTS (
        SELECT 1
        FROM "public"."user_roles" AS ur
        JOIN jsonb_array_elements_text(auth.jwt() -> 'organization_ids') AS org_id
        ON ur.organization_id = org_id::uuid
        WHERE ur.user_id = "users".id
    )
    AND
    -- Check if the requesting user is a superuser
    EXISTS (
        SELECT 1
        FROM "public"."user_roles"
        WHERE
            "user_roles"."user_id" = auth.uid() AND
            "user_roles"."role" = 'superuser'
    )
);