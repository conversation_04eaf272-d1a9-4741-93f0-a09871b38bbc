create table "public"."coupons" (
    "id" uuid not null default gen_random_uuid(),
    "organization_id" uuid not null,
    "stripe_coupon_id" text not null,
    "code" text not null,
    "discount_amount" smallint not null,
    "discount_type" text not null,
    "usage_limit_per_user" smallint,
    "stripe_customer_id" text,
    "expires_at" timestamp with time zone,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."coupons" enable row level security;

CREATE UNIQUE INDEX coupons_pkey ON public.coupons USING btree (id);

alter table "public"."coupons" add constraint "coupons_pkey" PRIMARY KEY using index "coupons_pkey";

alter table "public"."coupons" add constraint "public_coupons_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."coupons" validate constraint "public_coupons_organization_id_fkey";

grant delete on table "public"."coupons" to "anon";

grant insert on table "public"."coupons" to "anon";

grant references on table "public"."coupons" to "anon";

grant select on table "public"."coupons" to "anon";

grant trigger on table "public"."coupons" to "anon";

grant truncate on table "public"."coupons" to "anon";

grant update on table "public"."coupons" to "anon";

grant delete on table "public"."coupons" to "authenticated";

grant insert on table "public"."coupons" to "authenticated";

grant references on table "public"."coupons" to "authenticated";

grant select on table "public"."coupons" to "authenticated";

grant trigger on table "public"."coupons" to "authenticated";

grant truncate on table "public"."coupons" to "authenticated";

grant update on table "public"."coupons" to "authenticated";

grant delete on table "public"."coupons" to "service_role";

grant insert on table "public"."coupons" to "service_role";

grant references on table "public"."coupons" to "service_role";

grant select on table "public"."coupons" to "service_role";

grant trigger on table "public"."coupons" to "service_role";

grant truncate on table "public"."coupons" to "service_role";

grant update on table "public"."coupons" to "service_role";


