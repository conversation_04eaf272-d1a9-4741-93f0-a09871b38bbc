DROP POLICY IF EXISTS "Enable update for superusers" ON "public"."user_roles";

CREATE POLICY "Enable update for superusers"
ON "public"."user_roles"
AS PERMISSIVE
FOR UPDATE
TO authenticated
USING (true)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.user_roles as ur
    WHERE ur.organization_id = user_roles.organization_id
    AND ur.user_id = auth.uid()
    AND ur.role = 'superuser'
    AND ur.id != user_roles.id
  )
)