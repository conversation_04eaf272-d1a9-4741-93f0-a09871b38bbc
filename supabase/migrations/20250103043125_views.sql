create or replace view "public"."income_per_day" as  SELECT (orders.created_at)::date AS date,
    sum(orders.amount_charged_cents) AS total_income_cents,
    stations.organization_id
   FROM (orders
     JOIN stations ON (((orders.station_id)::text = (stations.id)::text)))
  GROUP BY stations.organization_id, ((orders.created_at)::date)
  ORDER BY ((orders.created_at)::date) DESC;


create or replace view "public"."orders_per_station" as  SELECT orders.station_id,
    count(*) AS order_count,
    stations.organization_id
   FROM (orders
     JOIN stations ON (((stations.id)::text = (orders.station_id)::text)))
  GROUP BY orders.station_id, stations.organization_id
  ORDER BY (count(*)) DESC;



