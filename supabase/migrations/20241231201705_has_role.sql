set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _organization_id uuid, _role text)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$DECLARE
  valid boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles AS ur
    WHERE ur.user_id = _user_id
    AND ur.organization_id = _organization_id
    AND ur.role = _role
  ) INTO valid;

  RETURN valid;
END;$function$
;