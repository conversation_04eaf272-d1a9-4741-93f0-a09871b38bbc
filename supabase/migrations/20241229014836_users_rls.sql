
SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgsodium" WITH SCHEMA "pgsodium";

COMMENT ON SCHEMA "public" IS 'standard public schema';

CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "postgis" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";

DROP TYPE IF EXISTS "public"."user_role";

CREATE TYPE "public"."user_role" AS ENUM (
    'superuser',
    'admin',
    'employee'
);

ALTER TYPE "public"."user_role" OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."custom_access_token_hook"("event" "jsonb") RETURNS "jsonb"
    LANGUAGE "plpgsql" STABLE
    AS $$declare
    claims jsonb;
    organization_ids jsonb;
begin
    select jsonb_agg(organization_id) into organization_ids
    from public.user_roles
    where user_id = (event->>'user_id')::uuid;

    claims := event->'claims';

    if organization_ids is not null then
        claims := jsonb_set(claims, '{organization_ids}', organization_ids);
    else
        claims := jsonb_set(claims, '{organization_ids}', '[]');
    end if;

    event := jsonb_set(event, '{claims}', claims);

    return event;
end;$$;

ALTER FUNCTION "public"."custom_access_token_hook"("event" "jsonb") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."find_stations_within_radius"("lng" double precision, "lat" double precision, "radius" double precision) RETURNS TABLE("station_id" character varying, "longitude" double precision, "latitude" double precision, "distance_meters" double precision)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    WITH params AS (
        SELECT
            ST_SetSRID(ST_MakePoint(lng, lat), 4326) AS center_point
    )
    SELECT
        s.id as station_id,
        s.longitude,
        s.latitude,
        ST_Distance(
            ST_SetSRID(ST_MakePoint(s.longitude, s.latitude), 4326)::geography,
            p.center_point::geography
        ) AS distance_meters
    FROM
        public.stations s,
        params p
    WHERE
        ST_DWithin(
            ST_SetSRID(ST_MakePoint(s.longitude, s.latitude), 4326)::geography,
            p.center_point::geography,
            radius
        )
    ORDER BY distance_meters ASC;
END;
$$;

ALTER FUNCTION "public"."find_stations_within_radius"("lng" double precision, "lat" double precision, "radius" double precision) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."generate_discount_code"() RETURNS "text"
    LANGUAGE "plpgsql"
    AS $$DECLARE
    code TEXT;
    bad_words TEXT[] := ARRAY['NIGGER', 'NIGGA', 'NAZI', 'FUCK', 'DICK', 'PORN', 'BITCH', 'SUCK', 'CUNT', 'SLUT', 'HOE']; -- Add your bad words here
BEGIN
    LOOP
        -- Generate a random UUID, convert it to a hexadecimal string, take the first 6 characters
        code := substring(md5(gen_random_uuid()::text) from 1 for 6);
        
        -- Replace any non-alphanumeric characters to ensure code is alphanumeric (although UUID is already hex-based)
        code := regexp_replace(code, '[^a-zA-Z0-9]', '', 'g');

        -- Convert the code to uppercase
        code := upper(code);

        -- Check if the generated code contains any bad words
        IF NOT EXISTS (SELECT 1 FROM unnest(bad_words) AS bw WHERE position(bw IN code) > 0) THEN
            -- If no bad words are found, exit the loop
            EXIT;
        END IF;
    END LOOP;

    RETURN code;
END;$$;

ALTER FUNCTION "public"."generate_discount_code"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";

CREATE TABLE IF NOT EXISTS "public"."favorite_stations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "station_id" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."favorite_stations" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."orders" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "stripe_customer_id" character varying,
    "stripe_payment_intent_id" character varying NOT NULL,
    "type" character varying NOT NULL,
    "status" character varying DEFAULT '"pending"'::character varying NOT NULL,
    "amount_charged_cents" integer DEFAULT 0 NOT NULL,
    "started_at" timestamp with time zone,
    "ended_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "station_id" character varying NOT NULL,
    "cable_type" smallint NOT NULL,
    "battery_id" character varying,
    "carku_order_id" character varying NOT NULL,
    "station_slot_id" smallint,
    "promo_code" character varying,
    "hourly_price_cents" integer
);

ALTER TABLE "public"."orders" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."income_per_day" WITH ("security_invoker"='on') AS
 SELECT ("orders"."created_at")::"date" AS "date",
    "sum"("orders"."amount_charged_cents") AS "total_income_cents"
   FROM "public"."orders"
  GROUP BY (("orders"."created_at")::"date")
  ORDER BY (("orders"."created_at")::"date") DESC;

ALTER TABLE "public"."income_per_day" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."issues" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "type" character varying NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "order_id" "uuid"
);

ALTER TABLE "public"."issues" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."orders_by_source" WITH ("security_invoker"='on') AS
 SELECT ("orders"."created_at")::"date" AS "date",
    "count"(*) FILTER (WHERE ("orders"."user_id" IS NULL)) AS "via_terminal",
    "count"(*) FILTER (WHERE ("orders"."user_id" IS NOT NULL)) AS "via_application"
   FROM "public"."orders"
  GROUP BY (("orders"."created_at")::"date")
  ORDER BY (("orders"."created_at")::"date");

ALTER TABLE "public"."orders_by_source" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."orders_by_status" WITH ("security_invoker"='on') AS
 SELECT ("orders"."created_at")::"date" AS "date",
    "count"(*) FILTER (WHERE (("orders"."status")::"text" = ANY (ARRAY[('completed'::character varying)::"text", ('late_completed'::character varying)::"text"]))) AS "completed_orders",
    "count"(*) FILTER (WHERE (("orders"."status")::"text" = ANY (ARRAY[('ongoing'::character varying)::"text", ('pending'::character varying)::"text"]))) AS "ongoing_orders",
    "count"(*) FILTER (WHERE (("orders"."status")::"text" = ANY (ARRAY[('failed'::character varying)::"text", ('late_failed'::character varying)::"text", ('cancelled'::character varying)::"text"]))) AS "failed_orders"
   FROM "public"."orders"
  GROUP BY (("orders"."created_at")::"date")
  ORDER BY (("orders"."created_at")::"date");

ALTER TABLE "public"."orders_by_status" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."orders_per_day" WITH ("security_invoker"='on') AS
 SELECT ("orders"."created_at")::"date" AS "date",
    "count"("orders"."id") AS "count"
   FROM "public"."orders"
  GROUP BY (("orders"."created_at")::"date")
  ORDER BY (("orders"."created_at")::"date") DESC;

ALTER TABLE "public"."orders_per_day" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."orders_per_station" WITH ("security_invoker"='on') AS
 SELECT "orders"."station_id",
    "count"(*) AS "order_count"
   FROM "public"."orders"
  GROUP BY "orders"."station_id"
  ORDER BY ("count"(*)) DESC;

ALTER TABLE "public"."orders_per_station" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."organizations" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."station_events" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "station_id" character varying NOT NULL,
    "data" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."station_events" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."station_locations" (
    "station_id" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "latitude" double precision NOT NULL,
    "longitude" double precision NOT NULL,
    "address" character varying,
    "postal_code" character varying,
    "city" character varying,
    "state" character varying,
    "country" character varying,
    "google_place_id" character varying
);

ALTER TABLE "public"."station_locations" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."station_slots" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "station_id" character varying NOT NULL,
    "slot_id" smallint NOT NULL,
    "voltage" smallint NOT NULL,
    "amperage" smallint NOT NULL,
    "power" smallint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "is_charging" boolean NOT NULL,
    "battery_id" character varying,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "synced_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."station_slots" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."stations" (
    "id" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "status" smallint NOT NULL,
    "apn" character varying,
    "usable_batteries" smallint NOT NULL,
    "empty_slots" smallint NOT NULL,
    "total_batteries" smallint NOT NULL,
    "signal_strength" smallint,
    "update_status" boolean NOT NULL,
    "lock_mode" smallint NOT NULL,
    "last_heartbeat_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "latitude" double precision,
    "longitude" double precision,
    "hourly_price_cents" integer DEFAULT 400,
    "organization_id" "uuid"
);

ALTER TABLE "public"."stations" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."total_income" WITH ("security_invoker"='on') AS
 SELECT "sum"("orders"."amount_charged_cents") AS "total_income_cents"
   FROM "public"."orders";

ALTER TABLE "public"."total_income" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."total_orders" WITH ("security_invoker"='on') AS
 SELECT "count"("orders"."id") AS "total_orders"
   FROM "public"."orders"
  WHERE (("orders"."status")::"text" = 'completed'::"text");

ALTER TABLE "public"."total_orders" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."total_users" WITH ("security_invoker"='on') AS
 SELECT "count"("users"."id") AS "total_users"
   FROM "auth"."users";

ALTER TABLE "public"."total_users" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "public"."user_role" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "organization_id" "uuid"
);

ALTER TABLE "public"."user_roles" OWNER TO "postgres";

CREATE OR REPLACE VIEW "public"."users" WITH ("security_invoker"='on') AS
 SELECT "users"."instance_id",
    "users"."id",
    "users"."aud",
    "users"."role",
    "users"."email",
    "users"."encrypted_password",
    "users"."email_confirmed_at",
    "users"."invited_at",
    "users"."confirmation_token",
    "users"."confirmation_sent_at",
    "users"."recovery_token",
    "users"."recovery_sent_at",
    "users"."email_change_token_new",
    "users"."email_change",
    "users"."email_change_sent_at",
    "users"."last_sign_in_at",
    "users"."raw_app_meta_data",
    "users"."raw_user_meta_data",
    "users"."is_super_admin",
    "users"."created_at",
    "users"."updated_at",
    "users"."phone",
    "users"."phone_confirmed_at",
    "users"."phone_change",
    "users"."phone_change_token",
    "users"."phone_change_sent_at",
    "users"."confirmed_at",
    "users"."email_change_token_current",
    "users"."email_change_confirm_status",
    "users"."banned_until",
    "users"."reauthentication_token",
    "users"."reauthentication_sent_at",
    "users"."is_sso_user",
    "users"."deleted_at",
    "users"."is_anonymous"
   FROM "auth"."users";

ALTER TABLE "public"."users" OWNER TO "postgres";

ALTER TABLE ONLY "public"."favorite_stations"
    ADD CONSTRAINT "favorite_stations_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."issues"
    ADD CONSTRAINT "issues_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_carku_order_id_key" UNIQUE ("carku_order_id");

ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."station_events"
    ADD CONSTRAINT "station_events_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."station_slots"
    ADD CONSTRAINT "station_slots_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."stations"
    ADD CONSTRAINT "stations_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."station_locations"
    ADD CONSTRAINT "temp_station_locations_pkey" PRIMARY KEY ("station_id");

ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_key" UNIQUE ("user_id");

ALTER TABLE ONLY "public"."favorite_stations"
    ADD CONSTRAINT "favorite_stations_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."favorite_stations"
    ADD CONSTRAINT "favorite_stations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."issues"
    ADD CONSTRAINT "issues_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."issues"
    ADD CONSTRAINT "public_issues_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "public_orders_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("id");

ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "public_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."stations"
    ADD CONSTRAINT "public_stations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "public_user_roles_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."station_events"
    ADD CONSTRAINT "station_events_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."station_slots"
    ADD CONSTRAINT "station_slots_station_id_fkey" FOREIGN KEY ("station_id") REFERENCES "public"."stations"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;

CREATE POLICY "Allow auth admin to read user roles" ON "public"."user_roles" AS PERMISSIVE FOR SELECT TO "supabase_auth_admin" USING (true);

CREATE POLICY "Enable delete for users based on user_id" ON "public"."favorite_stations" AS PERMISSIVE FOR DELETE USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));

CREATE POLICY "Enable insert for users based on user_id" ON "public"."favorite_stations" AS PERMISSIVE FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));

CREATE POLICY "Enable read access based on user_id" ON "public"."orders" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));

CREATE POLICY "Enable read access based on user_id" ON "public"."organizations" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."organization_id" = "organizations"."id") AND ("user_roles"."user_id" = "auth"."uid"())))));

CREATE POLICY "Enable read access based on user_id" ON "public"."user_roles" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));

CREATE POLICY "Enable read access for all users" ON "public"."station_locations" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);

CREATE POLICY "Enable read access for all users" ON "public"."station_slots" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);

CREATE POLICY "Enable read access for all users" ON "public"."stations" AS PERMISSIVE FOR SELECT TO "authenticated" USING (true);

CREATE POLICY "Enable read access for organization members" ON "public"."orders" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."stations"
     JOIN "jsonb_array_elements_text"(("auth"."jwt"() -> 'organization_ids'::"text")) "org_id"("value") ON ((("org_id"."value")::"uuid" = "stations"."organization_id")))
  WHERE (("stations"."id")::"text" = ("orders"."station_id")::"text"))));

CREATE POLICY "Enable read access for users based on user_id" ON "public"."favorite_stations" AS PERMISSIVE FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));

CREATE POLICY "Enable update for superusers" ON "public"."stations" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "jsonb_array_elements_text"(("auth"."jwt"() -> 'organization_ids'::"text")) "org_id"("value")
  WHERE (("org_id"."value")::"uuid" = "stations"."organization_id"))) AND (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'superuser'::"public"."user_role"))))));

ALTER TABLE "public"."favorite_stations" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."issues" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."orders" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."station_events" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."station_locations" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."station_slots" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."stations" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."user_roles" ENABLE ROW LEVEL SECURITY;

ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";

ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."orders";

ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."station_slots";

ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."stations";

GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT USAGE ON SCHEMA "public" TO "supabase_auth_admin";

REVOKE ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "service_role";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "supabase_auth_admin";

GRANT ALL ON FUNCTION "public"."find_stations_within_radius"("lng" double precision, "lat" double precision, "radius" double precision) TO "anon";
GRANT ALL ON FUNCTION "public"."find_stations_within_radius"("lng" double precision, "lat" double precision, "radius" double precision) TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_stations_within_radius"("lng" double precision, "lat" double precision, "radius" double precision) TO "service_role";

GRANT ALL ON FUNCTION "public"."generate_discount_code"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_discount_code"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_discount_code"() TO "service_role";

GRANT ALL ON TABLE "public"."favorite_stations" TO "anon";
GRANT ALL ON TABLE "public"."favorite_stations" TO "authenticated";
GRANT ALL ON TABLE "public"."favorite_stations" TO "service_role";

GRANT ALL ON TABLE "public"."orders" TO "anon";
GRANT ALL ON TABLE "public"."orders" TO "authenticated";
GRANT ALL ON TABLE "public"."orders" TO "service_role";

GRANT ALL ON TABLE "public"."income_per_day" TO "anon";
GRANT ALL ON TABLE "public"."income_per_day" TO "authenticated";
GRANT ALL ON TABLE "public"."income_per_day" TO "service_role";

GRANT ALL ON TABLE "public"."issues" TO "anon";
GRANT ALL ON TABLE "public"."issues" TO "authenticated";
GRANT ALL ON TABLE "public"."issues" TO "service_role";

GRANT ALL ON TABLE "public"."orders_by_source" TO "anon";
GRANT ALL ON TABLE "public"."orders_by_source" TO "authenticated";
GRANT ALL ON TABLE "public"."orders_by_source" TO "service_role";

GRANT ALL ON TABLE "public"."orders_by_status" TO "anon";
GRANT ALL ON TABLE "public"."orders_by_status" TO "authenticated";
GRANT ALL ON TABLE "public"."orders_by_status" TO "service_role";

GRANT ALL ON TABLE "public"."orders_per_day" TO "anon";
GRANT ALL ON TABLE "public"."orders_per_day" TO "authenticated";
GRANT ALL ON TABLE "public"."orders_per_day" TO "service_role";

GRANT ALL ON TABLE "public"."orders_per_station" TO "anon";
GRANT ALL ON TABLE "public"."orders_per_station" TO "authenticated";
GRANT ALL ON TABLE "public"."orders_per_station" TO "service_role";

GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";

GRANT ALL ON TABLE "public"."station_events" TO "anon";
GRANT ALL ON TABLE "public"."station_events" TO "authenticated";
GRANT ALL ON TABLE "public"."station_events" TO "service_role";

GRANT ALL ON TABLE "public"."station_locations" TO "anon";
GRANT ALL ON TABLE "public"."station_locations" TO "authenticated";
GRANT ALL ON TABLE "public"."station_locations" TO "service_role";

GRANT ALL ON TABLE "public"."station_slots" TO "anon";
GRANT ALL ON TABLE "public"."station_slots" TO "authenticated";
GRANT ALL ON TABLE "public"."station_slots" TO "service_role";

GRANT ALL ON TABLE "public"."stations" TO "anon";
GRANT ALL ON TABLE "public"."stations" TO "authenticated";
GRANT ALL ON TABLE "public"."stations" TO "service_role";

GRANT ALL ON TABLE "public"."total_income" TO "anon";
GRANT ALL ON TABLE "public"."total_income" TO "authenticated";
GRANT ALL ON TABLE "public"."total_income" TO "service_role";

GRANT ALL ON TABLE "public"."total_orders" TO "anon";
GRANT ALL ON TABLE "public"."total_orders" TO "authenticated";
GRANT ALL ON TABLE "public"."total_orders" TO "service_role";

GRANT ALL ON TABLE "public"."total_users" TO "anon";
GRANT ALL ON TABLE "public"."total_users" TO "authenticated";
GRANT ALL ON TABLE "public"."total_users" TO "service_role";

GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";
GRANT ALL ON TABLE "public"."user_roles" TO "supabase_auth_admin";
GRANT ALL ON TABLE "public"."user_roles" TO PUBLIC;

GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";

RESET ALL;

--
-- Dumped schema changes for auth and storage
--

CREATE POLICY "Enable read access for organization members" ON "auth"."users" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM (("public"."orders" "o"
     JOIN "public"."stations" "s" ON ((("o"."station_id")::"text" = ("s"."id")::"text")))
     JOIN "jsonb_array_elements_text"(("auth"."jwt"() -> 'organization_ids'::"text")) "org_id"("value") ON (("s"."organization_id" = ("org_id"."value")::"uuid")))
  WHERE ("o"."user_id" = "users"."id"))));

CREATE POLICY "Enable read access for superusers" ON "auth"."users" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'superuser'::"public"."user_role")))));

