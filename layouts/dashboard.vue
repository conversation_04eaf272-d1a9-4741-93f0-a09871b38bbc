<script setup lang="ts">
useSeoMeta({
  title: "HIKO Dashboard",
});

const isSidebarVisible = ref(false);
</script>

<template>
  <div
    class="md:grid items-start justify-center flex-1 max-h-dvh md:grid-cols-[250px_1fr]"
  >
    <Sidebar v-model="isSidebarVisible" />
    <main class="h-full max-h-dvh flex flex-col overflow-hidden">
      <header
        v-if="$slots.header"
        class="h-[64px] flex items-center gap-1.5 border-b border-gray-200 dark:border-gray-800 px-4"
      >
        <UButton
          icon="ph:list"
          color="gray"
          variant="ghost"
          class="md:hidden"
          @click="isSidebarVisible = !isSidebarVisible"
        />
        <slot name="header" />
      </header>
      <div
        v-if="$slots.subheader"
        class="border-b border-gray-200 dark:border-gray-800 px-4 py-2"
      >
        <slot name="subheader" />
      </div>
      <div class="flex-1 flex flex-col overflow-auto">
        <slot />
      </div>
    </main>

    <UNotifications />
    <UModals />
  </div>
</template>
