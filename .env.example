# SvelteKit Environment Variables
# Copy this file to .env and fill in your values

# Supabase Configuration
PUBLIC_SUPABASE_URL=http://localhost:54321
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Stripe Configuration
# https://dashboard.stripe.com/test/apikeys
PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_stripe_key
STRIPE_SECRET_KEY=sk_stripe_key

# Google Maps API
PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Sentry Configuration
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# Twilio Configuration
# https://console.twilio.com/
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# Carku API Configuration (Battery Stations)
CARKU_API_URL=your_carku_api_url_here
CARKU_API_KEY=your_carku_api_key_here

# TODO: currently not used, but this was found in src/routes/api/callback/station/+server.ts
PUBLIC_CARKU_SERVER_IP = "************"

# PostHog Analytics
PUBLIC_POSTHOG_KEY=your_posthog_key_here
PUBLIC_POSTHOG_HOST=your_posthog_host_here
