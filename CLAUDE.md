# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Development
- `npm run dev` - Start development server on port 5174 (http://localhost:5174)
- `npm run build` - Build for production (Cloudflare Pages preset)
- `npm run preview` - Preview production build locally
- `npm run generate` - Generate static site

### Code Quality
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run types:generate` - Generate TypeScript types from Supabase schema

## Architecture Overview

### Tech Stack
- **Framework**: Nuxt 3 with SSR disabled (SPA mode)
- **UI Library**: Nuxt UI (built on Tailwind CSS and Headless UI)
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **State Management**: Tanstack Vue Query for server state, Nuxt composables for client state
- **Styling**: Tailwind CSS with custom "hiko" color palette
- **Authentication**: Supabase Auth
- **Deployment**: Cloudflare Pages

### Project Structure

#### Pages & Routing
The app uses file-based routing with nested organization structure:
- `/login` - Authentication with forgot password modal
- `/organizations/[organization_id]` - Organization-scoped routes:
  - `/dashboard` - Analytics with revenue charts and top stations
  - `/stations` - Battery station management with detailed views
  - `/users` - User management with order history
  - `/coupons` - Coupon creation and management
  - `/settings` - Organization and profile settings with member management

#### Key Directories
- `components/` - Reusable Vue components (modals, tables, charts)
- `composables/` - Vue composables for shared logic (organizations, user roles, dialogs)
- `server/api/` - Nuxt server API routes for backend operations
- `server/services/` - External service integrations (Carku API, Stripe, Google Maps)
- `types/` - TypeScript type definitions including auto-generated Supabase types
- `queries/` - Tanstack Query hooks for data fetching

#### External Integrations
- **Carku API**: Battery station hardware communication via `server/services/carku.ts`
- **Stripe**: Payment processing via `server/services/stripe.ts`
- **Google Maps**: Address autocomplete and geocoding
- **Cloudflare Turnstile**: CAPTCHA protection

### Data Flow Patterns
- Use Tanstack Vue Query for server state management with appropriate stale times
- Organization context is managed through route parameters and composables
- Authentication state handled by Nuxt Supabase module
- Real-time updates via Supabase subscriptions where needed

### Development Notes
- The app runs in SPA mode (`ssr: false`) for Cloudflare Pages compatibility
- Custom dev server port is 5174 to avoid conflicts
- ESLint configured with Prettier, import sorting, and unused import removal
- Database types are generated from Supabase and should be regenerated when schema changes