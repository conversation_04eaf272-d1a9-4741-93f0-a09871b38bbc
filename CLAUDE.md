# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HIKO is a SvelteKit application for battery rental and purchase from stations located throughout the city. The app provides an interactive map interface, payment management, favorites, order history, and flexible return options.

## Development Commands

- `npm run dev` - Start development server with host binding
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Run Svelte type checking and sync
- `npm run check:watch` - Run type checking in watch mode
- `npm run lint` - Run ESLint on the codebase

## Database Commands (Supabase)

- `npm run types:generate` - Generate TypeScript types from Supabase schema
- `npm run db:generate-seed` - Dump database data to seed file
- `npm run db:generate-migration` - Generate new database migration
- `npm run db:push-migration` - Push migrations to linked Supabase project

## Architecture & Key Patterns

### Technology Stack
- **Frontend**: SvelteKit with TypeScript
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Payments**: Stripe integration
- **Maps**: Google Maps API and Leaflet
- **Styling**: TailwindCSS with custom color scheme
- **Monitoring**: Sentry for error tracking
- **SMS**: Twilio integration
- **Deployment**: Cloudflare (configured via wrangler.json)

### Project Structure
- `src/routes/` - SvelteKit file-based routing with API endpoints
- `src/components/` - Reusable Svelte components (UI components in `ui/` subfolder)
- `src/lib/services/` - External service integrations (Supabase, Stripe, Maps, etc.)
- `src/lib/stores/` - Svelte stores for global state management
- `src/lib/composables/` - Reusable reactive utilities
- `src/lib/utils/` - Pure utility functions
- `src/lib/i18n/` - Internationalization setup (French/English)
- `src/types/` - TypeScript type definitions
- `supabase/` - Database migrations and configuration

### Authentication & Authorization
- User authentication handled via Supabase Auth
- Cookie-based session management in `hooks.server.ts`
- Route protection implemented in `+layout.server.ts`
- Authenticated routes defined in `src/lib/utils/auth.ts`

### Internationalization
- Supports French (default) and English
- Uses `sveltekit-i18n` with route-specific translations
- Translation files organized by feature in `src/lib/i18n/{lang}/`
- Language preference stored in cookies

### State Management
- Svelte stores for user state, location (lat/lng), orders, and stations
- Local storage utilities for persistence
- Reactive composables for common patterns (geolocation, pagination, etc.)

### API Integration
- Axios client with automatic 401 handling and logout
- API routes follow REST conventions under `/api/`
- External integrations: Stripe, Supabase, Google Maps, Carku (battery stations)

### Code Style
- ESLint with strict rules for formatting and imports
- 4-space indentation for Svelte files
- Simple import sorting enabled
- Double quotes, trailing commas, semicolons required

### Important Configuration
- Path aliases: `$types` → `src/types`, `$components` → `src/components`
- CSRF protection disabled for cross-origin requests
- Sentry configured for error monitoring
- Icons via unplugin-icons with Svelte compiler