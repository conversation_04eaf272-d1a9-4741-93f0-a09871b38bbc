<script setup lang="ts">
import { Route } from "~/types/route";

const router = useRouter();
const supabase = useSupabaseClient();
const user = useSupabaseUser();
const colorMode = useColorMode();

const profileName = computed(
  () => user.value?.email?.split("@")[0] || "Unknown",
);
const profileAvatar = computed(() => getProfileAvatarUrl(profileName.value));

const isDark = computed({
  get() {
    return colorMode.value === "dark";
  },
  set() {
    colorMode.preference = colorMode.value === "dark" ? "light" : "dark";
  },
});

const signOut = async () => {
  await supabase.auth.signOut();
  await router.push(Route.LOGIN);
};

const profileItems = computed(() => [
  [
    {
      label: user.value?.email || "Unknown",
      slot: "profile",
      avatar: {
        src: profileAvatar.value,
      },
      disabled: true,
    },
  ],
  [
    {
      label: isDark.value ? "Light mode" : "Dark mode",
      icon: isDark.value ? "heroicons:sun" : "heroicons:moon",
      click: () => (isDark.value = !isDark.value),
    },
  ],
  [
    {
      label: "Sign out",
      icon: "i-heroicons-arrow-left-on-rectangle",
      click: signOut,
    },
  ],
]);
</script>

<template>
  <UDropdown
    :items="profileItems"
    :popper="{ placement: 'top-start' }"
    class="w-full"
    :ui="{
      width: 'w-full',
      popper: {
        strategy: 'absolute',
      },
      item: {
        disabled: 'cursor-text select-text',
      },
    }"
  >
    <template #profile="{ item }">
      <div class="text-sm text-left text-gray-900 dark:text-white">
        <p>Signed in as</p>
        <p class="truncate font-medium">
          {{ item.label }}
        </p>
      </div>
    </template>

    <UButton color="gray" variant="ghost" size="md" block class="justify-start">
      <div class="flex items-center gap-2 w-full">
        <UAvatar :src="profileAvatar" size="xs" />
        <span class="text-base font-semibold truncate">{{ profileName }}</span>
        <UIcon name="ph:dots-three-vertical" class="size-5 ml-auto" />
      </div>
    </UButton>
  </UDropdown>
</template>
