<script setup lang="ts">
import { useMutation } from "@tanstack/vue-query";
import QRCodeStyling from "qr-code-styling";

import HikoText from "~/assets/images/hiko_text.png";

type Props = {
  data: string;
  filename: string;
};

const props = defineProps<Props>();

const qrCode = new QRCodeStyling({
  width: 500,
  height: 500,
  data: props.data,
  image: HikoText,
  dotsOptions: {
    color: "#000",
    type: "rounded",
  },
  backgroundOptions: {
    color: "#fff",
  },
});

const { mutateAsync: downloadQrCode, isPending: isDownloading } = useMutation({
  mutationFn: async () => {
    await qrCode.download({ name: props.filename, extension: "png" });
  },
});
</script>

<template>
  <UButton
    icon="ph:qr-code"
    color="gray"
    size="md"
    :loading="isDownloading"
    @click="downloadQrCode"
  >
    Download QR Code
  </UButton>
</template>
