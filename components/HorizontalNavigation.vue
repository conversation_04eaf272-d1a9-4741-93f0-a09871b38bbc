<script setup lang="ts">
import { customTabsUI } from "~/utils/ui";

type Props = {
  tabs: Array<{
    label: string;
    icon: string;
    to: string;
  }>;
};

const { tabs } = defineProps<Props>();

const route = useRoute();
const router = useRouter();

const getLandingTab = () => {
  const tab = tabs.findIndex((tab) => route.path.endsWith(tab.to));
  return tab === -1 ? 0 : tab;
};

const selectedTabIndex = ref(getLandingTab());

watch(
  selectedTabIndex,
  (tabIndex) => {
    const index = tabIndex === -1 ? 0 : tabIndex;
    const to = tabs[index].to;
    router.replace(to);
  },
  { immediate: true },
);

watch(route, () => {
  selectedTabIndex.value = getLandingTab();
});
</script>

<template>
  <div class="flex -mx-4 -my-1.5">
    <UTabs v-model="selectedTabIndex" :items="tabs" :ui="customTabsUI" />
  </div>
</template>
