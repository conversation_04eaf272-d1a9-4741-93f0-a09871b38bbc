<script setup lang="ts">
import { type Duration, format, isSameDay, sub } from "date-fns";

import DatePicker from "./_DatePicker.vue";

type Props = {
  buttonProps?: Record<string, unknown>;
};

defineProps<Props>();

const ranges = [
  { label: "Last 7 days", duration: { days: 7 } },
  { label: "Last 14 days", duration: { days: 14 } },
  { label: "Last 30 days", duration: { days: 30 } },
  { label: "Last 3 months", duration: { months: 3 } },
  { label: "Last 6 months", duration: { months: 6 } },
  { label: "Last year", duration: { years: 1 } },
];
const selected = defineModel<{ start: Date; end: Date }>({
  required: true,
  default: { start: sub(new Date(), { days: 14 }), end: new Date() },
});

const isRangeSelected = (duration: Duration) => {
  return (
    isSameDay(selected.value.start, sub(new Date(), duration)) &&
    isSameDay(selected.value.end, new Date())
  );
};

const selectRange = (duration: Duration) => {
  selected.value = { start: sub(new Date(), duration), end: new Date() };
};
</script>

<template>
  <UPopover :popper="{ placement: 'bottom-start' }">
    <UButton
      icon="i-heroicons-calendar-days-20-solid"
      v-bind="{ ...buttonProps }"
    >
      {{ format(selected.start, "d MMM, yyy") }} -
      {{ format(selected.end, "d MMM, yyy") }}
    </UButton>

    <template #panel="{ close }">
      <div
        class="flex items-center sm:divide-x divide-gray-200 dark:divide-gray-800"
      >
        <div class="hidden sm:flex flex-col py-4">
          <UButton
            v-for="(range, index) in ranges"
            :key="index"
            :label="range.label"
            color="gray"
            variant="ghost"
            class="rounded-none px-6"
            :class="[
              isRangeSelected(range.duration)
                ? 'bg-gray-100 dark:bg-gray-800'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800/50',
            ]"
            truncate
            @click="selectRange(range.duration)"
          />
        </div>

        <DatePicker v-model="selected" v-bind="{ ...$attrs }" @close="close" />
      </div>
    </template>
  </UPopover>
</template>
