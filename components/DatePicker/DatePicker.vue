<script setup lang="ts">
import { format } from "date-fns";

import DatePicker from "./_DatePicker.vue";

const date = defineModel<Date | null | undefined>({ required: true });
</script>

<template>
  <UPopover :popper="{ placement: 'bottom-start' }">
    <UButton
      :color="date ? 'primary' : 'white'"
      icon="i-heroicons-calendar-days-20-solid"
      :label="date ? format(date, 'd MMM, yyy') : 'Pick a date'"
      block
    />

    <template #panel="{ close }">
      <DatePicker
        v-model="date"
        is-required
        v-bind="{ ...$attrs }"
        @close="close"
      />
    </template>
  </UPopover>
</template>
