<script setup lang="ts">
type Props = {
  title?: string;
  description?: string;
  onConfirm: () => void | Promise<void>;
};

const props = withDefaults(defineProps<Props>(), {
  title: "Are you sure?",
  description: "This action cannot be undone.",
});
const isVisible = defineModel<boolean>();

const isLoading = ref(false);

const handleConfirm = async () => {
  try {
    isLoading.value = true;
    await props.onConfirm();
  } finally {
    isLoading.value = false;
    isVisible.value = false;
  }
};
</script>

<template>
  <UModal v-model="isVisible">
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            {{ props.title }}
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="ph:x"
            class="-my-1"
            @click="isVisible = false"
          />
        </div>
      </template>

      <p>{{ props.description }}</p>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton color="red" :loading="isLoading" @click="handleConfirm">
            Confirm
          </UButton>
          <UButton color="gray" @click="isVisible = false"> Cancel </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
