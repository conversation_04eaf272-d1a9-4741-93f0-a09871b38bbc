<script setup lang="ts">
import { ALL_ORGANIZATIONS } from "~/composables/useOrganizations";
import { OrganizationRoute } from "~/types/route";

const { organizations } = useOrganizations();
const organizationId = useOrganizationId();

const organizationItems = computed(() => {
  if (!organizations.value) return [];

  return [
    [
      {
        label: "Overview",
        icon: "heroicons:presentation-chart-bar",
        to: OrganizationRoute.HOME(ALL_ORGANIZATIONS),
      },
    ],
    organizations.value.map((organization) => ({
      label: organization.name,
      avatar: {
        src: getOrganizationAvatarUrl(organization.name),
      },
      to: OrganizationRoute.HOME(organization.id),
    })),
    [
      {
        label: "Manage teams",
        icon: "ph:gear",
        to: OrganizationRoute.SETTINGS(organizationId.value) + "/members",
      },
    ],
  ];
});

const selectedOrganization = computed(() => {
  return (
    organizations.value?.find(
      (organization) => organization.id === organizationId.value,
    ) ?? null
  );
});
</script>

<template>
  <UDropdown
    :items="organizationItems"
    :popper="{ placement: 'bottom-start' }"
    class="w-full"
    :ui="{
      width: 'w-full',
      popper: {
        strategy: 'absolute',
      },
    }"
  >
    <UButton
      color="gray"
      variant="ghost"
      size="md"
      block
      class="justify-between"
      trailing-icon="ph:caret-down"
      truncate
    >
      <div v-if="selectedOrganization" class="flex items-center gap-2">
        <UAvatar
          :src="getOrganizationAvatarUrl(selectedOrganization.name)"
          size="xs"
        />
        <span class="text-base font-semibold truncate">{{
          selectedOrganization.name
        }}</span>
      </div>
      <div
        v-else-if="organizationId === ALL_ORGANIZATIONS"
        class="flex items-center gap-2"
      >
        <UIcon name="heroicons:presentation-chart-bar" class="size-6" />
        <span class="text-base font-semibold truncate">Overview</span>
      </div>
    </UButton>
  </UDropdown>
</template>
