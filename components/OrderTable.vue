<script setup lang="ts">
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

import type { BadgeColor } from "#ui/types";
import type { Tables } from "~/types/database.types";
import { OrderStatus } from "~/types/orders";
import { OrganizationRoute } from "~/types/route";

dayjs.extend(duration);

type Props = {
  orders?: Tables<"orders">[];
  count?: number;
  isLoading: boolean;
  excludeColumns?: string[];
  organizationId: string;
};

const props = defineProps<Props>();

const DEFAULT_PER_PAGE = 10;

const sort = defineModel<{ column: string; direction: "desc" | "asc" }>("sort");
const page = defineModel<number>("page", { default: 1 });
const perPage = defineModel<number>("perPage", { default: DEFAULT_PER_PAGE });

const defaultColumns = [
  {
    key: "id",
    label: "ID",
  },
  {
    key: "user_id",
    label: "User",
  },
  {
    key: "amount_charged_cents",
    label: "Amount Charged",
    sortable: true,
  },
  {
    key: "status",
    label: "Status",
    sortable: true,
  },
  {
    key: "started_at",
    label: "Started At",
    sortable: true,
  },
  {
    key: "ended_at",
    label: "Ended At",
    sortable: true,
  },
  {
    key: "duration",
    label: "Duration",
    sortable: false,
  },
];

const columns = computed(() =>
  defaultColumns.filter(
    (column) => !props.excludeColumns?.includes(column.key),
  ),
);

const getOrderStatusColor = (status: string): BadgeColor => {
  switch (status) {
    case OrderStatus.Completed:
      return "green";
    case OrderStatus.LateCompleted:
      return "green";
    case OrderStatus.Pending:
      return "indigo";
    case OrderStatus.Ongoing:
      return "sky";
    case OrderStatus.Cancelled:
      return "orange";
    case OrderStatus.Failed:
      return "red";
    case OrderStatus.LateFailed:
      return "red";
    default:
      return "gray";
  }
};

const getOrderStatusText = (status: string) => {
  if (status === OrderStatus.LateCompleted) return "completed";
  if (status === OrderStatus.LateFailed) return "failed";

  return status;
};

const getOrderDuration = (order: Tables<"orders">) => {
  if (!order.started_at) return "N/A";

  const startedAt = dayjs(order.started_at);
  const endedAt = order.ended_at ? dayjs(order.ended_at) : dayjs();

  const diffMinutes = endedAt.diff(startedAt, "minute");

  if (diffMinutes < 1)
    return dayjs
      .duration(endedAt.diff(startedAt, "seconds"), "seconds")
      .format("s[s]");

  const formatMinutes = [
    diffMinutes >= 1440 ? "D[d]" : false,
    diffMinutes >= 60 ? "H[hr]" : false,
    diffMinutes >= 1 ? "m[min]" : false,
  ]
    .filter(Boolean)
    .join(" ");

  return dayjs.duration(diffMinutes, "minutes").format(formatMinutes);
};
</script>

<template>
  <UTable
    v-model:sort="sort"
    sort-mode="manual"
    :rows="orders"
    :columns="columns"
    :loading="isLoading"
    class="border-b border-gray-300 dark:border-gray-700"
  >
    <template #user_id-data="{ row }: { row: Tables<'orders'> }">
      <UButton
        v-if="row.user_id"
        icon="ph:link"
        color="gray"
        variant="link"
        :padded="false"
        :to="`${OrganizationRoute.USERS(organizationId)}/${row.user_id}`"
      />
      <span v-else>N/A</span>
    </template>
    <template #amount_charged_cents-data="{ row }: { row: Tables<'orders'> }">
      <p>
        {{
          formatCurrency(row.amount_charged_cents / 100, {
            currencyDisplay: "narrowSymbol",
          })
        }}
      </p>
    </template>
    <template #status-data="{ row }: { row: Tables<'orders'> }">
      <UBadge
        :color="getOrderStatusColor(row.status)"
        variant="subtle"
        :title="row.status"
      >
        {{ getOrderStatusText(row.status) }}
      </UBadge>
    </template>
    <template #started_at-data="{ row }: { row: Tables<'orders'> }">
      <p>
        {{
          row.started_at ? dayjs(row.started_at).format("D MMM h:mm a") : "N/A"
        }}
      </p>
    </template>
    <template #ended_at-data="{ row }: { row: Tables<'orders'> }">
      <p>
        {{ row.ended_at ? dayjs(row.ended_at).format("D MMM h:mm a") : "N/A" }}
      </p>
    </template>
    <template #duration-data="{ row }: { row: Tables<'orders'> }">
      <p>{{ getOrderDuration(row) }}</p>
    </template>
  </UTable>
  <div v-if="orders" class="flex justify-end px-3 py-3.5">
    <Pagination v-model="page" v-model:per-page="perPage" :total="count ?? 0" />
  </div>
</template>
