<script setup lang="ts">
type Props = {
  total: number;
};

defineProps<Props>();

const page = defineModel<number>({ required: true });
const perPage = defineModel<number>("perPage", { default: 10 });
</script>

<template>
  <div
    class="flex flex-col-reverse md:flex-row items-end md:items-center gap-4"
  >
    <div class="flex items-center gap-4">
      <p class="text-sm text-gray-700 dark:text-gray-200">Rows per page:</p>
      <USelect v-model.number="perPage" :options="[10, 20, 50, 100]" />
    </div>
    <div class="flex items-center gap-4">
      <p class="text-sm text-gray-700 dark:text-gray-200">
        {{ (page - 1) * perPage + 1 }}-{{ Math.min(page * perPage, total) }} of
        {{ total }}
      </p>
      <UPagination
        v-model.number="page"
        :page-count="perPage"
        :total="total"
        :active-button="{ color: 'black' }"
      />
    </div>
  </div>
</template>
