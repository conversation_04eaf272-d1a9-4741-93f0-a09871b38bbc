<script setup lang="ts">
import OrganizationDropdown from "~/components/OrganizationDropdown.vue";
import UserDropdown from "~/components/UserDropdown.vue";
import { OrganizationRoute } from "~/types/route";

const route = useRoute();
const organizationId = useOrganizationId();
const { isSuperuser } = useUserRole();

const isVisible = defineModel<boolean>({ required: true });

const isLinkActive = (link: string) => {
  return route.fullPath.startsWith(link);
};

const navLinks = computed(() =>
  [
    {
      label: "Dashboard",
      icon: "heroicons:home",
      to: OrganizationRoute.DASHBOARD(organizationId.value),
    },
    {
      label: "Stations",
      icon: "heroicons:battery-100-20-solid",
      to: OrganizationRoute.STATIONS(organizationId.value),
    },
    {
      label: "Coupons",
      icon: "heroicons:percent-badge",
      to: OrganizationRoute.COUPONS(organizationId.value),
    },
    {
      label: "Users",
      icon: "heroicons:user-group",
      to: OrganizationRoute.USERS(organizationId.value),
      hidden: !isSuperuser.value && organizationId.value !== ALL_ORGANIZATIONS,
    },
    {
      label: "Settings",
      icon: "heroicons:cog-8-tooth",
      to: OrganizationRoute.SETTINGS(organizationId.value),
    },
  ].filter((link) => !link.hidden),
);
</script>

<template>
  <div
    :class="
      cn(
        'fixed inset-0 md:hidden transition-opacity bg-gray-200/75 dark:bg-gray-800/75 z-[9]',
        {
          hidden: !isVisible,
        },
      )
    "
    @click="isVisible = false"
  />
  <aside
    :class="
      cn(
        'w-full max-w-[448px] h-dvh transition-transform md:transition-none flex flex-col fixed md:sticky top-0 border-r border-gray-200 dark:border-gray-800 bg-background shadow-xl md:shadow-none z-10',
        {
          '-translate-x-full md:translate-x-0': !isVisible,
        },
      )
    "
  >
    <div class="h-[64px] flex items-center gap-1.5 px-4">
      <UButton
        icon="ph:list"
        color="gray"
        variant="ghost"
        class="md:hidden"
        @click="isVisible = !isVisible"
      />
      <OrganizationDropdown />
    </div>

    <ul class="flex-1 flex flex-col gap-1 px-2 md:px-4 py-2">
      <li v-for="(link, index) in navLinks" :key="index">
        <ULink
          :to="link.to"
          :active="isLinkActive(link.to)"
          class="w-full flex items-center gap-2 text-md font-medium rounded-md px-2.5 py-1.5"
          active-class="text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-800"
          inactive-class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100/50 dark:hover:bg-gray-800/50"
        >
          <UIcon :name="link.icon" class="size-6" />
          <span>{{ link.label }}</span>
        </ULink>
      </li>

      <UDivider class="mt-auto" />

      <li class="flex justify-end mt-1">
        <UserDropdown />
      </li>
    </ul>
  </aside>
</template>
