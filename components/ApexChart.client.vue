<script setup lang="ts">
import ApexCharts from "apexcharts";
import merge from "lodash-es/merge";

type Props = {
  options: ApexCharts.ApexOptions;
};

const props = defineProps<Props>();

const colorMode = useColorMode();

const chartElement = ref<HTMLDivElement>();
let chart: ApexCharts | null = null;
const defaultOptions = computed(() => ({
  chart: {
    toolbar: {
      show: false,
    },
    zoom: {
      enabled: false,
    },
    width: "100%",
  },
  theme: {
    mode: colorMode.value,
  },
  dataLabels: {
    enabled: false,
  },
  grid: {
    borderColor:
      colorMode.value === "dark"
        ? "rgba(255, 255, 255, 0.1)"
        : "rgba(0, 0, 0, 0.1)",
    yaxis: {
      lines: {
        show: false,
      },
    },
    padding: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
  },
  tooltip: {
    followCursor: true,
  },
}));
const options = computed(() => merge(defaultOptions.value, props.options));

watch(
  [() => props.options, colorMode],
  () => {
    chart?.updateOptions(options.value);
  },
  { deep: true },
);

onMounted(async () => {
  await nextTick();

  chart = new ApexCharts(chartElement.value, options.value);

  await chart.render();
});

onUnmounted(() => {
  chart?.destroy();
});
</script>

<template>
  <div ref="chartElement" />
</template>

<style>
.apexcharts-canvas .apexcharts-svg {
  background: transparent !important;
}

.apexcharts-text.apexcharts-xaxis-label {
  @apply fill-gray-400;
}

.apexcharts-tooltip {
  box-shadow: none !important;
  overflow: visible !important;
  background: transparent !important;
  border: none !important;
}
</style>
