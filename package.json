{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxi build --preset=cloudflare_pages", "dev": "npx rimraf .output && nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "types:generate": "npx supabase gen types --lang=typescript --project-id \"nffidlnciimxqfxmbjjg\" > types/database.types.ts", "lint:fix": "eslint . --fix", "db:seed": "NODE_ENV=development node scripts/seed/development.seeder.js", "db:seed:dev": "npm run db:seed"}, "dependencies": {"@nuxt/eslint": "^1.1.0", "@nuxt/fonts": "^0.10.3", "@nuxt/scripts": "^0.9.5", "@nuxt/ui": "^2.20.0", "@nuxtjs/supabase": "^1.4.3", "@nuxtjs/turnstile": "^0.9.11", "@tanstack/vue-query": "^5.62.3", "@unovis/vue": "^1.5.0", "@vueuse/core": "^12.0.0", "apexcharts": "^4.2.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "nuxt": "^3.14.1592", "ofetch": "^1.4.1", "qr-code-styling": "^1.8.4", "stripe": "^17.4.0", "tailwind-merge": "^2.5.5", "v-calendar": "^3.1.2", "vue": "latest", "vue-router": "latest", "zod": "^3.23.8"}, "devDependencies": {"@faker-js/faker": "^9.0.0", "@iconify/json": "^2.2.281", "@types/lodash-es": "^4.17.12", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.5.1", "typescript": "^5.7.3"}}