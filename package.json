{"name": "hiko-app", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint .", "types:generate": "npx supabase gen types typescript --project-id \"nffidlnciimxqfxmbjjg\" --schema public > src/types/supabase.ts", "db:generate-seed": "npx supabase db dump --data-only -f supabase/seed.sql --linked", "db:generate-migration": "npx supabase db diff -f migration", "db:push-migration": "npx supabase db push --linked", "db:seed": "NODE_ENV=development npx tsx scripts/seed/seeders/development.seeder.ts", "db:seed:dev": "npm run db:seed", "db:test": "NODE_ENV=development node scripts/simple-seed-test.js"}, "devDependencies": {"@faker-js/faker": "^9.0.0", "@iconify/json": "^2.2.180", "@sentry/vite-plugin": "^2.20.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/eslint": "8.56.0", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-plugin-simple-import-sort": "^12.1.0", "eslint-plugin-svelte": "^2.35.1", "postcss": "^8.4.34", "supabase": "^1.161.0", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "tailwindcss": "^3.4.1", "tslib": "^2.4.1", "tsx": "^4.7.0", "typescript": "^5.0.0", "unplugin-icons": "^0.18.5", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@jill64/sentry-sveltekit-cloudflare": "^1.7.15", "@sentry/browser": "^8.27.0", "@stripe/stripe-js": "^3.0.7", "@supabase/supabase-js": "^2.39.3", "axios": "^1.6.7", "clsx": "^2.1.0", "compressorjs": "^1.2.1", "dayjs": "^1.11.10", "jimp": "^0.22.12", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "libphonenumber-js": "^1.10.55", "ofetch": "^1.3.4", "posthog-js": "^1.219.4", "qr-scanner": "^1.4.2", "stripe": "^14.19.0", "sveltekit-i18n": "^2.4.2", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1"}}