import { useQuery } from "@tanstack/vue-query";

import type { Enums } from "~/types/database.types";
import { Query } from "~/types/query";
import { Time } from "~/types/time";

export const useUserRole = () => {
  const supabase = useSupabaseClient();
  const user = useSupabaseUser();
  const organizationId = useOrganizationId();

  const { data: userRole } = useQuery({
    queryKey: [Query.USER_ROLES, organizationId, user],
    queryFn: async () => {
      if (
        !user.value ||
        !organizationId.value ||
        organizationId.value === ALL_ORGANIZATIONS
      )
        return null;

      const { data, error } = await supabase
        .from("user_roles")
        .select("role")
        .eq("user_id", user.value.id)
        .eq("organization_id", organizationId.value)
        .maybeSingle();

      if (error) {
        throw new Error(error.message);
      }

      return data?.role;
    },
    staleTime: Time.ONE_HOUR,
  });

  const isEmployee = computed(
    () =>
      userRole.value &&
      ["employee", "admin", "superuser"].includes(userRole.value),
  );
  const isAdmin = computed(
    () => userRole.value && ["admin", "superuser"].includes(userRole.value),
  );
  const isSuperuser = computed(() => userRole.value === "superuser");

  return {
    isEmployee,
    isSuperuser,
    isAdmin,
  };
};

export const useUserRoles = () => {
  const supabase = useSupabaseClient();
  const user = useSupabaseUser();

  const { data: userRoles } = useQuery({
    queryKey: [Query.USER_ROLES, user],
    queryFn: async () => {
      if (!user.value) return null;

      const { data, error } = await supabase
        .from("user_roles")
        .select("organization_id, role")
        .eq("user_id", user.value.id);

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    staleTime: Time.ONE_HOUR,
  });

  return computed<Record<string, Enums<"user_role">>>(() => {
    if (!userRoles.value) return {};

    return userRoles.value.reduce(
      (acc, { organization_id, role }) => {
        acc[organization_id] = role;
        return acc;
      },
      {} as Record<string, Enums<"user_role">>,
    );
  });
};
