import { useQuery } from "@tanstack/vue-query";

import { Query } from "~/types/query";
import { Time } from "~/types/time";

export const ALL_ORGANIZATIONS = "all";

export const useOrganizations = () => {
  const supabase = useSupabaseClient();

  const { data: organizations, isLoading } = useQuery({
    queryKey: [Query.ORGANIZATIONS],
    queryFn: async () => {
      const { data, error } = await supabase.from("organizations").select();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    staleTime: Time.ONE_HOUR,
  });

  return {
    organizations,
    isLoading,
  };
};

export const useOrganizationId = () => {
  const route = useRoute();
  return computed(() => route.params.organization_id as string);
};

export const useOrganization = () => {
  const organizationId = useOrganizationId();
  const { organizations } = useOrganizations();

  return computed(() =>
    organizations.value?.find(
      (organization) => organization.id === organizationId.value,
    ),
  );
};
