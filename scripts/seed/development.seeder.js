#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { faker } from '@faker-js/faker';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables from .env file
function loadEnv() {
  try {
    const envPath = join(process.cwd(), '.env');
    const envFile = readFileSync(envPath, 'utf8');
    
    envFile.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0 && !key.startsWith('#')) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.warn('⚠️  Could not load .env file:', error.message);
  }
}

/**
 * Development Database Seeder for Admin Dashboard
 * 
 * Creates admin users and organizational data for local development
 * NEVER runs in production environment
 */
class AdminDevelopmentSeeder {
  constructor() {
    // Load environment variables
    loadEnv();
    
    // Environment protection
    if (process.env.NODE_ENV === 'production') {
      throw new Error('🚫 Database seeding is not allowed in production environment!');
    }

    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration. Please check your .env file.');
    }

    // Additional protection: check if URL looks like production
    if (supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('localhost')) {
      const isProduction = !supabaseUrl.includes('dev') && !supabaseUrl.includes('test');
      if (isProduction) {
        console.warn('⚠️  Warning: This appears to be a production Supabase URL');
        console.warn('   For safety, please use a local development database.');
        throw new Error('Production database detected. Use local Supabase for development.');
      }
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Run the admin seeding process
   */
  async seed() {
    console.log('🌱 Starting admin development database seeding...\n');

    try {
      // Check if data already exists
      const existingData = await this.checkExistingData();
      
      if (existingData.hasData) {
        console.log('📊 Existing data found:');
        console.log(`   • ${existingData.organizations} organizations`);
        console.log(`   • ${existingData.users} users`);
        console.log(`   • ${existingData.stations} stations`);
        console.log('\n✅ Admin dashboard can use existing data!');
        console.log('\n🔑 Admin login: <EMAIL> (create password in Supabase Auth)');
        return;
      }

      // Create admin-specific data
      await this.seedAdminUsers();
      
      console.log('\n✅ Admin development seeding completed!');
      console.log('\n🔑 Admin login credentials:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: Create in Supabase Auth Users panel');
      console.log('\n💡 Next steps:');
      console.log('   1. Go to your Supabase dashboard');
      console.log('   2. Navigate to Authentication > Users');
      console.log('   3. Create/update <NAME_EMAIL>');
      console.log('   4. Login to admin dashboard at http://localhost:5174\n');

    } catch (error) {
      console.error('❌ Admin seeding failed:', error);
      process.exit(1);
    }
  }

  /**
   * Check if data already exists in database
   */
  async checkExistingData() {
    const [orgResult, userResult, stationResult] = await Promise.all([
      this.supabase.from('organizations').select('id', { count: 'exact', head: true }),
      this.supabase.from('users').select('id', { count: 'exact', head: true }),
      this.supabase.from('stations').select('id', { count: 'exact', head: true }),
    ]);

    return {
      hasData: (orgResult.count || 0) > 0 || (userResult.count || 0) > 0 || (stationResult.count || 0) > 0,
      organizations: orgResult.count || 0,
      users: userResult.count || 0,
      stations: stationResult.count || 0,
    };
  }

  /**
   * Create admin users for testing
   */
  async seedAdminUsers() {
    console.log('👑 Creating admin users...');

    // Check if admin users already exist
    const { data: existingUsers } = await this.supabase.auth.admin.listUsers();
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const existingEmails = existingUsers?.users?.map(u => u.email) || [];
    
    if (adminEmails.some(email => existingEmails.includes(email))) {
      console.log('   Admin users already exist');
      return existingUsers.users.filter(u => adminEmails.includes(u.email));
    }

    // Create admin users through auth
    const adminUsers = [
      {
        email: '<EMAIL>',
        password: 'password123',
        phone: '+15141234567',
        user_metadata: {
          first_name: 'Admin',
          last_name: 'User',
          stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        phone: '+15141234568',
        user_metadata: {
          first_name: 'Manager',
          last_name: 'User',
          stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
        },
      },
    ];

    const createdUsers = [];
    for (const userData of adminUsers) {
      const { data, error } = await this.supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        phone: userData.phone,
        user_metadata: userData.user_metadata,
        email_confirm: true,
        phone_confirm: true,
      });

      if (error) {
        throw new Error(`Failed to create admin user ${userData.email}: ${error.message}`);
      }

      createdUsers.push(data.user);
    }

    console.log(`   Created ${createdUsers.length} admin users`);

    // Create main organization if it doesn't exist
    const { data: existingOrg } = await this.supabase
      .from('organizations')
      .select('id')
      .eq('name', 'HIKO Development')
      .single();

    let organizationId = existingOrg?.id;

    if (!organizationId) {
      console.log('🏢 Creating main organization...');
      const { data: newOrg, error: orgError } = await this.supabase
        .from('organizations')
        .insert({
          id: faker.string.uuid(),
          name: 'HIKO Development',
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (orgError) {
        throw new Error(`Failed to create organization: ${orgError.message}`);
      }

      organizationId = newOrg.id;
      console.log('   Created main organization');
    }

    // Create user roles
    console.log('👔 Assigning admin roles...');
    const userRoles = createdUsers.map(user => ({
      id: faker.string.uuid(),
      user_id: user.id,
      organization_id: organizationId,
      role: 'admin',
      created_at: new Date().toISOString(),
    }));

    const { error: roleError } = await this.supabase
      .from('user_roles')
      .insert(userRoles);

    if (roleError) {
      throw new Error(`Failed to create user roles: ${roleError.message}`);
    }

    console.log(`   Assigned admin roles to ${userRoles.length} users`);
    
    return createdUsers;
  }
}

// Run seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const seeder = new AdminDevelopmentSeeder();
  await seeder.seed();
}