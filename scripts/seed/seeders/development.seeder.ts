#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import type { Database } from '../../../src/types/supabase';
import { OrganizationFactory } from '../factories/organization.factory';
import { StationFactory, StationLocationFactory, StationSlotFactory } from '../factories/station.factory';
import { UserFactory, UserRoleFactory } from '../factories/user.factory';
import { OrderFactory, CouponFactory, FavoriteStationFactory } from '../factories/order.factory';

// Load environment variables from .env file
function loadEnv() {
  try {
    const envPath = join(process.cwd(), '.env');
    const envFile = readFileSync(envPath, 'utf8');
    
    envFile.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0 && !key.startsWith('#')) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.warn('⚠️  Could not load .env file:', (error as Error).message);
  }
}

/**
 * Development Database Seeder
 * 
 * Creates realistic test data for local development
 * NEVER runs in production environment
 */
export class DevelopmentSeeder {
  private supabase;
  
  constructor() {
    // Load environment variables
    loadEnv();
    
    // Environment protection
    if (process.env.NODE_ENV === 'production') {
      throw new Error('🚫 Database seeding is not allowed in production environment!');
    }

    const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration. Please check your .env file.');
    }

    // Additional protection: check if URL looks like production
    if (supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('localhost')) {
      const isProduction = !supabaseUrl.includes('dev') && !supabaseUrl.includes('test');
      if (isProduction) {
        console.warn('⚠️  Warning: This appears to be a production Supabase URL');
        console.warn('   For safety, please use a local development database.');
        throw new Error('Production database detected. Use local Supabase for development.');
      }
    }

    this.supabase = createClient<Database>(supabaseUrl, supabaseKey);
  }

  /**
   * Run the complete development seeding process
   */
  async seed() {
    console.log('🌱 Starting development database seeding...\n');

    try {
      // Clear existing data (optional - comment out to preserve)
      await this.clearExistingData();

      // Create base data
      const organizations = await this.seedOrganizations();
      const stations = await this.seedStations(organizations);
      await this.seedStationLocations(stations);
      await this.seedStationSlots(stations);
      
      // Create users and relationships
      const users = await this.seedUsers();
      await this.seedUserRoles(users, organizations[0]); // Associate with first org
      
      // Create transactional data
      await this.seedOrders(users, stations);
      await this.seedCoupons(organizations);
      await this.seedFavoriteStations(users, stations);
      
      console.log('\n✅ Development database seeding completed successfully!');
      console.log('\n📊 Summary:');
      console.log(`   • ${organizations.length} organizations created`);
      console.log(`   • ${stations.length} stations created`);
      console.log(`   • ${users.length} users created`);
      console.log(`   • Test users: <EMAIL>, <EMAIL>, <EMAIL>`);
      console.log('\n🚀 You can now start developing with realistic test data!\n');

    } catch (error) {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  /**
   * Clear existing data (use with caution)
   */
  private async clearExistingData() {
    console.log('🧹 Clearing existing data...');
    
    // Clear auth users first
    try {
      const { data: users } = await this.supabase.auth.admin.listUsers();
      if (users?.users?.length > 0) {
        for (const user of users.users) {
          await this.supabase.auth.admin.deleteUser(user.id);
        }
        console.log(`   Cleared ${users.users.length} auth users`);
      }
    } catch (error) {
      console.warn('⚠️  Warning: Could not clear auth users:', (error as Error).message);
    }
    
    const tables = [
      'favorite_stations',
      'issues', 
      'orders',
      'coupons',
      'station_events',
      'station_slots',
      'station_locations',
      'stations',
      'user_roles',
      'organizations',
    ];

    for (const table of tables) {
      if (table === 'station_locations') {
        // station_locations uses station_id as primary key
        const { error } = await this.supabase
          .from(table)
          .delete()
          .neq('station_id', '00000000-0000-0000-0000-000000000000'); // Delete all

        if (error) {
          console.warn(`⚠️  Warning: Could not clear table ${table}:`, error.message);
        }
      } else {
        const { error } = await this.supabase
          .from(table)
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

        if (error) {
          console.warn(`⚠️  Warning: Could not clear table ${table}:`, error.message);
        }
      }
    }

    console.log('✅ Data cleared\n');
  }

  /**
   * Seed organizations
   */
  private async seedOrganizations() {
    console.log('🏢 Creating organizations...');
    const factory = new OrganizationFactory(this.supabase);
    
    // Create main development organization
    const mainOrg = await factory.createTestOrganization('HIKO Development');
    
    // Create additional organizations
    const additionalOrgs = await factory.createRealisticOrganizations(3);
    
    const organizations = [mainOrg, ...additionalOrgs];
    console.log(`   Created ${organizations.length} organizations`);
    
    return organizations;
  }

  /**
   * Seed stations
   */
  private async seedStations(organizations: any[]) {
    console.log('🏪 Creating stations...');
    const factory = new StationFactory(this.supabase);
    
    let allStations = [];
    
    for (const org of organizations) {
      if (org.name === 'HIKO Development') {
        // Create Montreal stations for main org
        const montrealStations = await factory.createMontrealStations(org.id);
        allStations.push(...montrealStations);
      } else {
        // Create random stations for other orgs
        const orgStations = await factory.createForOrganization(org.id, 2);
        allStations.push(...orgStations);
      }
    }
    
    console.log(`   Created ${allStations.length} stations`);
    return allStations;
  }

  /**
   * Seed station locations
   */
  private async seedStationLocations(stations: any[]) {
    console.log('📍 Creating station locations...');
    const factory = new StationLocationFactory(this.supabase);
    
    // Find Montreal stations (from main org)
    const montrealStations = stations.slice(0, 5); // First 5 are Montreal stations
    const otherStations = stations.slice(5);
    
    // Create Montreal locations
    if (montrealStations.length > 0) {
      await factory.createMontrealLocations(montrealStations);
    }
    
    // Create random locations for other stations
    if (otherStations.length > 0) {
      await factory.createForStations(otherStations);
    }
    
    console.log(`   Created ${stations.length} station locations`);
  }

  /**
   * Seed station slots
   */
  private async seedStationSlots(stations: any[]) {
    console.log('🔌 Creating station slots...');
    const factory = new StationSlotFactory(this.supabase);
    
    const slots = await factory.createForStations(stations);
    console.log(`   Created ${slots.length} station slots`);
    
    return slots;
  }

  /**
   * Seed users
   */
  private async seedUsers() {
    console.log('👥 Creating users...');
    const factory = new UserFactory(this.supabase);
    
    // Create test users for development
    const testUsers = await factory.createTestUsers();
    
    // Create additional customer users
    const customers = await factory.createCustomers(7);
    
    const allUsers = [...testUsers, ...customers];
    console.log(`   Created ${allUsers.length} users`);
    console.log(`   Test logins: <EMAIL>, <EMAIL>, <EMAIL>`);
    
    return allUsers;
  }

  /**
   * Seed user roles
   */
  private async seedUserRoles(users: any[], mainOrganization: any) {
    console.log('👔 Creating user roles...');
    const factory = new UserRoleFactory(this.supabase);
    
    // Create roles for test users
    const testUsers = users.slice(0, 3); // First 3 are test users
    await factory.createTestUserRoles(testUsers, mainOrganization.id);
    
    console.log(`   Created roles for ${testUsers.length} test users`);
  }

  /**
   * Seed orders
   */
  private async seedOrders(users: any[], stations: any[]) {
    console.log('📦 Creating orders...');
    const factory = new OrderFactory(this.supabase);
    
    // Create realistic order history
    const orders = await factory.createRealisticOrders(users, stations, 150);
    
    console.log(`   Created ${orders.length} orders`);
    return orders;
  }

  /**
   * Seed coupons
   */
  private async seedCoupons(organizations: any[]) {
    console.log('🎫 Creating coupons...');
    const factory = new CouponFactory(this.supabase);
    
    const coupons = await factory.createForOrganizations(organizations);
    
    console.log(`   Created ${coupons.length} coupons`);
    return coupons;
  }

  /**
   * Seed favorite stations
   */
  private async seedFavoriteStations(users: any[], stations: any[]) {
    console.log('⭐ Creating favorite stations...');
    const factory = new FavoriteStationFactory(this.supabase);
    
    const favorites = await factory.createForUsers(users, stations);
    
    console.log(`   Created ${favorites.length} favorite stations`);
    return favorites;
  }
}

// Run seeder if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    const seeder = new DevelopmentSeeder();
    await seeder.seed();
  })();
}