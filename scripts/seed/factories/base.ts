import { faker } from '@faker-js/faker';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../../../src/types/supabase';

export type Tables = Database['public']['Tables'];
export type SupabaseClientType = SupabaseClient<Database>;

/**
 * Base factory class for generating test data
 */
export abstract class BaseFactory<T extends keyof Tables> {
  protected supabase: SupabaseClientType;
  protected tableName: T;

  constructor(supabase: SupabaseClientType, tableName: T) {
    this.supabase = supabase;
    this.tableName = tableName;
  }

  /**
   * Generate fake data for the model
   */
  abstract make(overrides?: Partial<Tables[T]['Insert']>): Tables[T]['Insert'];

  /**
   * Create and insert a single record
   */
  async create(overrides?: Partial<Tables[T]['Insert']>): Promise<Tables[T]['Row']> {
    const data = this.make(overrides);
    const { data: result, error } = await this.supabase
      .from(this.tableName)
      .insert(data)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create ${this.tableName}: ${error.message}`);
    }

    return result;
  }

  /**
   * Create multiple records
   */
  async createMany(count: number, overrides?: Partial<Tables[T]['Insert']>): Promise<Tables[T]['Row'][]> {
    const records = Array.from({ length: count }, () => this.make(overrides));
    const { data, error } = await this.supabase
      .from(this.tableName)
      .insert(records)
      .select();

    if (error) {
      throw new Error(`Failed to create ${count} ${this.tableName} records: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Create multiple records with individual overrides
   */
  async createManyWithOverrides(
    definitions: Array<Partial<Tables[T]['Insert']>>
  ): Promise<Tables[T]['Row'][]> {
    const records = definitions.map(override => this.make(override));
    const { data, error } = await this.supabase
      .from(this.tableName)
      .insert(records)
      .select();

    if (error) {
      throw new Error(`Failed to create ${this.tableName} records: ${error.message}`);
    }

    return data || [];
  }
}

/**
 * Utility functions for common fake data patterns
 */
export class FakeDataUtils {
  static phoneNumber(): string {
    // Generate a valid North American phone number in E.164 format
    const areaCode = faker.number.int({ min: 200, max: 999 });
    const exchange = faker.number.int({ min: 200, max: 999 });
    const number = faker.number.int({ min: 1000, max: 9999 });
    return `+1${areaCode}${exchange}${number}`;
  }

  static email(): string {
    return faker.internet.email();
  }

  static address() {
    return {
      address: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state({ abbreviated: true }),
      country: faker.location.countryCode('alpha-2'),
      postal_code: faker.location.zipCode(),
    };
  }

  static coordinates() {
    return {
      latitude: faker.location.latitude({ min: 45.4, max: 45.6 }), // Montreal area
      longitude: faker.location.longitude({ min: -73.8, max: -73.4 }),
    };
  }

  static pastDate(days: number = 30): string {
    return faker.date.recent({ days }).toISOString();
  }

  static futureDate(days: number = 30): string {
    return faker.date.soon({ days }).toISOString();
  }

  static randomStatus<T extends string>(statuses: T[]): T {
    return faker.helpers.arrayElement(statuses);
  }

  static uuid(): string {
    return faker.string.uuid();
  }

  static cableType(): number {
    return faker.helpers.arrayElement([1, 2, 3]); // USB-C, Lightning, Micro-USB
  }

  static batteryId(): string {
    return faker.string.alphanumeric({ length: 8, casing: 'upper' });
  }
}