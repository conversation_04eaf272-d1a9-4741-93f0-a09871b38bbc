import { faker } from '@faker-js/faker';
import { BaseFactory, FakeDataUtils, type SupabaseClientType } from './base';

export class UserFactory {
  protected supabase: SupabaseClientType;

  constructor(supabase: SupabaseClientType) {
    this.supabase = supabase;
  }

  make(overrides = {}) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName });
    const phone = FakeDataUtils.phoneNumber();
    
    return {
      email,
      phone,
      password: 'password123', // Default password for all test users
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
      },
      ...overrides,
    };
  }

  /**
   * Create and insert a single user through auth
   */
  async create(overrides = {}) {
    const userData = this.make(overrides);
    
    const { data, error } = await this.supabase.auth.admin.createUser({
      email: userData.email,
      phone: userData.phone,
      password: userData.password,
      user_metadata: userData.user_metadata,
      email_confirm: true,
      phone_confirm: true,
    });

    if (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }

    return data.user;
  }

  /**
   * Create multiple users
   */
  async createMany(count: number, overrides = {}) {
    const users = [];
    for (let i = 0; i < count; i++) {
      const user = await this.create(overrides);
      users.push(user);
    }
    return users;
  }

  /**
   * Create multiple users with individual overrides
   */
  async createManyWithOverrides(definitions: Array<any>) {
    const users = [];
    for (const override of definitions) {
      const user = await this.create(override);
      users.push(user);
    }
    return users;
  }

  /**
   * Create test users with specific roles
   */
  async createTestUsers() {
    const testUsers = [
      {
        email: '<EMAIL>',
        user_metadata: {
          first_name: 'Admin',
          last_name: 'User',
          stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
        },
        phone: '+15141234567',
      },
      {
        email: '<EMAIL>', 
        user_metadata: {
          first_name: 'Demo',
          last_name: 'User',
          stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
        },
        phone: '+15141234568',
      },
      {
        email: '<EMAIL>',
        user_metadata: {
          first_name: 'Test',
          last_name: 'User',
          stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
        },
        phone: '+15141234569',
      },
    ];

    return this.createManyWithOverrides(testUsers);
  }

  /**
   * Create realistic customer users
   */
  async createCustomers(count: number = 10) {
    return this.createMany(count);
  }
}

export class UserRoleFactory extends BaseFactory<'user_roles'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'user_roles');
  }

  make(overrides = {}) {
    const roles = ['superuser', 'admin', 'employee'];
    
    return {
      id: FakeDataUtils.uuid(),
      user_id: '', // Must be provided
      organization_id: '', // Must be provided
      role: faker.helpers.arrayElement(roles),
      created_at: FakeDataUtils.pastDate(180),
      ...overrides,
    };
  }

  /**
   * Create admin role for user in organization
   */
  async createAdminRole(userId: string, organizationId: string) {
    return this.create({
      user_id: userId,
      organization_id: organizationId,
      role: 'admin',
    });
  }

  /**
   * Create roles for test users
   */
  async createTestUserRoles(users: any[], organizationId: string) {
    return this.createManyWithOverrides([
      {
        user_id: users[0].id, // <EMAIL>
        organization_id: organizationId,
        role: 'admin',
      },
      {
        user_id: users[1].id, // <EMAIL>
        organization_id: organizationId,
        role: 'employee',
      },
      {
        user_id: users[2].id, // <EMAIL>
        organization_id: organizationId,
        role: 'employee',
      },
    ]);
  }
}