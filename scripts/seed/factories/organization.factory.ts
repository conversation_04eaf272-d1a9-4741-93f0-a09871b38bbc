import { faker } from '@faker-js/faker';
import { BaseFactory, FakeDataUtils, type SupabaseClientType } from './base';

export class OrganizationFactory extends BaseFactory<'organizations'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'organizations');
  }

  make(overrides = {}) {
    return {
      id: FakeDataUtils.uuid(),
      name: faker.company.name(),
      created_at: FakeDataUtils.pastDate(365),
      ...overrides,
    };
  }

  /**
   * Create a test organization with a specific name
   */
  async createTestOrganization(name: string = 'HIKO Development Corp') {
    return this.create({ name });
  }

  /**
   * Create multiple organizations with realistic names
   */
  async createRealisticOrganizations(count: number = 5) {
    const organizations = [
      'HIKO Montreal East',
      'HIKO Downtown Hub',
      'HIKO West Island',
      'HIKO Student Services',
      'HIKO Business Center',
      'HIKO Shopping Districts',
      'HIKO Transport Hubs',
    ];

    const selected = faker.helpers.arrayElements(organizations, count);
    return this.createManyWithOverrides(
      selected.map(name => ({ name }))
    );
  }
}