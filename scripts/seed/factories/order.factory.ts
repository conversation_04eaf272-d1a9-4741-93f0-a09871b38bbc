import { faker } from '@faker-js/faker';
import { BaseFactory, FakeDataUtils, type SupabaseClientType, type Tables } from './base';

export class OrderFactory extends BaseFactory<'orders'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'orders');
  }

  make(overrides = {}) {
    const type = faker.helpers.arrayElement(['rental', 'purchase']);
    const status = type === 'rental' 
      ? FakeDataUtils.randomStatus(['pending', 'ongoing', 'completed', 'cancelled'])
      : FakeDataUtils.randomStatus(['pending', 'completed', 'cancelled']);

    const createdAt = FakeDataUtils.pastDate(90);
    const startedAt = status !== 'pending' ? createdAt : null;
    const endedAt = status === 'completed' ? FakeDataUtils.pastDate(30) : null;

    return {
      id: FakeDataUtils.uuid(),
      user_id: '', // Must be provided
      station_id: '', // Must be provided
      battery_id: faker.datatype.boolean({ probability: 0.8 }) 
        ? FakeDataUtils.batteryId() 
        : null,
      station_slot_id: faker.number.int({ min: 1, max: 8 }),
      type,
      status,
      cable_type: FakeDataUtils.cableType(),
      amount_charged_cents: type === 'rental' 
        ? faker.number.int({ min: 200, max: 1500 }) 
        : faker.number.int({ min: 2000, max: 5000 }),
      hourly_price_cents: type === 'rental' ? faker.number.int({ min: 200, max: 800 }) : null,
      carku_order_id: faker.string.alphanumeric({ length: 12, casing: 'upper' }),
      stripe_payment_intent_id: `pi_${faker.string.alphanumeric({ length: 24 })}`,
      stripe_customer_id: `cus_${faker.string.alphanumeric({ length: 14 })}`,
      promo_code: faker.datatype.boolean({ probability: 0.2 }) 
        ? faker.string.alphanumeric({ length: 8, casing: 'upper' })
        : null,
      created_at: createdAt,
      started_at: startedAt,
      ended_at: endedAt,
      ...overrides,
    };
  }

  /**
   * Create orders for users and stations
   */
  async createForUsersAndStations(
    users: any[],
    stations: Tables['stations']['Row'][],
    ordersPerUser: number = 5
  ) {
    const allOrders = [];
    
    for (const user of users) {
      const userOrders = Array.from({ length: ordersPerUser }, () => {
        const station = faker.helpers.arrayElement(stations);
        const stripeCustomerId = user.user_metadata?.stripe_customer_id || `cus_${faker.string.alphanumeric({ length: 14 })}`;
        return this.make({
          user_id: user.id,
          station_id: station.id,
          stripe_customer_id: stripeCustomerId,
        });
      });
      
      const { data, error } = await this.supabase
        .from('orders')
        .insert(userOrders)
        .select();

      if (error) {
        throw new Error(`Failed to create orders for user ${user.email}: ${error.message}`);
      }

      allOrders.push(...(data || []));
    }

    return allOrders;
  }

  /**
   * Create realistic order history with temporal patterns
   */
  async createRealisticOrders(
    users: any[],
    stations: Tables['stations']['Row'][],
    totalOrders: number = 100
  ) {
    const orders = [];
    
    // Create temporal patterns (more recent orders)
    for (let i = 0; i < totalOrders; i++) {
      const user = faker.helpers.arrayElement(users);
      const station = faker.helpers.arrayElement(stations);
      
      // Weight towards recent dates
      const daysAgo = Math.pow(faker.number.float({ min: 0, max: 1 }), 2) * 90;
      const createdAt = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000).toISOString();
      
      const stripeCustomerId = user.user_metadata?.stripe_customer_id || `cus_${faker.string.alphanumeric({ length: 14 })}`;
      
      const order = this.make({
        user_id: user.id,
        station_id: station.id,
        stripe_customer_id: stripeCustomerId,
        created_at: createdAt,
      });
      
      orders.push(order);
    }

    const { data, error } = await this.supabase
      .from('orders')
      .insert(orders)
      .select();

    if (error) {
      throw new Error(`Failed to create realistic orders: ${error.message}`);
    }

    return data || [];
  }
}

export class CouponFactory extends BaseFactory<'coupons'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'coupons');
  }

  make(overrides = {}) {
    const discountType = faker.helpers.arrayElement(['percentage', 'fixed']);
    const discountAmount = discountType === 'percentage' 
      ? faker.number.int({ min: 5, max: 50 })
      : faker.number.int({ min: 100, max: 1000 }); // cents

    return {
      id: FakeDataUtils.uuid(),
      organization_id: '', // Must be provided
      code: faker.string.alphanumeric({ length: 8, casing: 'upper' }),
      discount_type: discountType,
      discount_amount: discountAmount,
      stripe_promotion_code_id: `promo_${faker.string.alphanumeric({ length: 16 })}`,
      stripe_customer_id: null,
      usage_limit_per_user: faker.helpers.arrayElement([null, 1, 3, 5]),
      expires_at: faker.datatype.boolean({ probability: 0.7 }) 
        ? FakeDataUtils.futureDate(60)
        : null,
      created_at: FakeDataUtils.pastDate(30),
      ...overrides,
    };
  }

  /**
   * Create promotional coupons for organizations
   */
  async createForOrganizations(organizations: Tables['organizations']['Row'][]) {
    const allCoupons = [];
    
    for (const org of organizations) {
      const coupons = [
        {
          organization_id: org.id,
          code: 'WELCOME10',
          discount_type: 'percentage',
          discount_amount: 10,
          usage_limit_per_user: 1,
        },
        {
          organization_id: org.id,
          code: 'STUDENT15',
          discount_type: 'percentage', 
          discount_amount: 15,
          usage_limit_per_user: null,
        },
        {
          organization_id: org.id,
          code: 'SAVE5',
          discount_type: 'fixed',
          discount_amount: 500, // $5.00
          usage_limit_per_user: 3,
        },
      ];

      const orgCoupons = await this.createManyWithOverrides(coupons);
      allCoupons.push(...orgCoupons);
    }

    return allCoupons;
  }
}

export class FavoriteStationFactory extends BaseFactory<'favorite_stations'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'favorite_stations');
  }

  make(overrides = {}) {
    return {
      id: FakeDataUtils.uuid(),
      user_id: '', // Must be provided
      station_id: '', // Must be provided
      created_at: FakeDataUtils.pastDate(60),
      ...overrides,
    };
  }

  /**
   * Create favorite stations for users
   */
  async createForUsers(
    users: any[],
    stations: Tables['stations']['Row'][]
  ) {
    const favorites = [];
    
    for (const user of users) {
      // Each user favorites 1-4 stations
      const favoriteCount = faker.number.int({ min: 1, max: 4 });
      const userFavorites = faker.helpers.arrayElements(stations, favoriteCount);
      
      for (const station of userFavorites) {
        favorites.push(this.make({
          user_id: user.id,
          station_id: station.id,
        }));
      }
    }

    const { data, error } = await this.supabase
      .from('favorite_stations')
      .insert(favorites)
      .select();

    if (error) {
      throw new Error(`Failed to create favorite stations: ${error.message}`);
    }

    return data || [];
  }
}