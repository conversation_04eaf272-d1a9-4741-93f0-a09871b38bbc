import { faker } from '@faker-js/faker';
import { BaseFactory, FakeDataUtils, type SupabaseClientType, type Tables } from './base';

export class StationFactory extends BaseFactory<'stations'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'stations');
  }

  make(overrides = {}) {
    return {
      id: FakeDataUtils.uuid(),
      organization_id: '', // Must be provided
      hourly_price_cents: faker.number.int({ min: 200, max: 800 }), // $2-8 per hour
      empty_slots: faker.number.int({ min: 0, max: 4 }),
      total_batteries: faker.number.int({ min: 4, max: 12 }),
      usable_batteries: faker.number.int({ min: 3, max: 10 }),
      status: faker.helpers.arrayElement([0, 1, 2]), // 0=offline, 1=online, 2=maintenance
      lock_mode: faker.helpers.arrayElement([0, 1]),
      update_status: faker.datatype.boolean({ probability: 0.8 }),
      signal_strength: faker.number.int({ min: 1, max: 5 }),
      apn: faker.helpers.arrayElement(['telus.isp', 'rogers.com', 'bell.ca']),
      latitude: null, // Set via station_locations table
      longitude: null, // Set via station_locations table
      last_heartbeat_at: FakeDataUtils.pastDate(1),
      created_at: FakeDataUtils.pastDate(180),
      ...overrides,
    };
  }

  /**
   * Create stations for an organization
   */
  async createForOrganization(organizationId: string, count: number = 3) {
    return this.createMany(count, { organization_id: organizationId });
  }

  /**
   * Create realistic stations with Montreal locations
   */
  async createMontrealStations(organizationId: string) {
    const montrealStations = [
      { 
        hourly_price_cents: 300,
        total_batteries: 8,
        usable_batteries: 7,
        empty_slots: 1,
      },
      { 
        hourly_price_cents: 400,
        total_batteries: 12,
        usable_batteries: 10,
        empty_slots: 2,
      },
      { 
        hourly_price_cents: 250,
        total_batteries: 6,
        usable_batteries: 5,
        empty_slots: 1,
      },
      { 
        hourly_price_cents: 350,
        total_batteries: 10,
        usable_batteries: 8,
        empty_slots: 2,
      },
      { 
        hourly_price_cents: 500,
        total_batteries: 15,
        usable_batteries: 12,
        empty_slots: 3,
      },
    ];

    return this.createManyWithOverrides(
      montrealStations.map(station => ({
        ...station,
        organization_id: organizationId,
      }))
    );
  }
}

export class StationLocationFactory extends BaseFactory<'station_locations'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'station_locations');
  }

  make(overrides = {}) {
    const coords = FakeDataUtils.coordinates();
    const address = FakeDataUtils.address();
    
    return {
      station_id: '', // Must be provided
      latitude: coords.latitude,
      longitude: coords.longitude,
      address: address.address,
      city: address.city,
      state: address.state,
      country: address.country,
      postal_code: address.postal_code,
      google_place_id: null,
      created_at: FakeDataUtils.pastDate(180),
      ...overrides,
    };
  }

  /**
   * Create locations for stations
   */
  async createForStations(stations: Tables['stations']['Row'][]) {
    return this.createManyWithOverrides(
      stations.map(station => ({
        station_id: station.id,
      }))
    );
  }

  /**
   * Create realistic Montreal locations
   */
  async createMontrealLocations(stations: Tables['stations']['Row'][]) {
    const montrealLocations = [
      {
        latitude: 45.5017,
        longitude: -73.5673,
        address: '1000 Rue de la Gauchetière O',
        city: 'Montreal',
        state: 'QC',
        country: 'CA',
        postal_code: 'H3B 0A5',
      },
      {
        latitude: 45.5088,
        longitude: -73.5711,
        address: '677 Rue Sainte-Catherine O',
        city: 'Montreal', 
        state: 'QC',
        country: 'CA',
        postal_code: 'H3A 2M7',
      },
      {
        latitude: 45.5048,
        longitude: -73.5772,
        address: '845 Rue Sherbrooke O',
        city: 'Montreal',
        state: 'QC', 
        country: 'CA',
        postal_code: 'H3A 0G4',
      },
      {
        latitude: 45.5086,
        longitude: -73.5547,
        address: '333 Rue de la Commune O',
        city: 'Montreal',
        state: 'QC',
        country: 'CA', 
        postal_code: 'H2Y 2E2',
      },
      {
        latitude: 45.5017,
        longitude: -73.5709,
        address: '1 Place Ville Marie',
        city: 'Montreal',
        state: 'QC',
        country: 'CA',
        postal_code: 'H3B 2C4',
      },
    ];

    return this.createManyWithOverrides(
      stations.map((station, index) => ({
        station_id: station.id,
        ...montrealLocations[index % montrealLocations.length],
      }))
    );
  }
}

export class StationSlotFactory extends BaseFactory<'station_slots'> {
  constructor(supabase: SupabaseClientType) {
    super(supabase, 'station_slots');
  }

  make(overrides = {}) {
    return {
      id: FakeDataUtils.uuid(),
      station_id: '', // Must be provided
      slot_id: 1, // Will be overridden
      battery_id: faker.datatype.boolean({ probability: 0.7 }) 
        ? FakeDataUtils.batteryId() 
        : null,
      is_charging: faker.datatype.boolean({ probability: 0.3 }),
      power: faker.number.int({ min: 5, max: 15 }), // Watts
      voltage: faker.number.int({ min: 3, max: 5 }), // Volts
      amperage: faker.number.int({ min: 1, max: 3 }), // Amps
      synced_at: FakeDataUtils.pastDate(1),
      created_at: FakeDataUtils.pastDate(30),
      updated_at: FakeDataUtils.pastDate(1),
      ...overrides,
    };
  }

  /**
   * Create slots for a station
   */
  async createForStation(stationId: string, slotCount: number = 8) {
    return this.createManyWithOverrides(
      Array.from({ length: slotCount }, (_, index) => ({
        station_id: stationId,
        slot_id: index + 1,
      }))
    );
  }

  /**
   * Create slots for multiple stations
   */
  async createForStations(stations: Tables['stations']['Row'][]) {
    const allSlots = [];
    for (const station of stations) {
      const slotCount = faker.number.int({ min: 4, max: 12 });
      const slots = await this.createForStation(station.id, slotCount);
      allSlots.push(...slots);
    }
    return allSlots;
  }
}