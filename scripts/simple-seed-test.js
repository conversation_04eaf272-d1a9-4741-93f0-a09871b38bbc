#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { faker } from '@faker-js/faker';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables from .env file
function loadEnv() {
  try {
    const envPath = join(process.cwd(), '.env');
    const envFile = readFileSync(envPath, 'utf8');
    
    envFile.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0 && !key.startsWith('#')) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.warn('⚠️  Could not load .env file:', error.message);
  }
}

// Load environment variables
loadEnv();

// Environment protection
if (process.env.NODE_ENV === 'production') {
  throw new Error('🚫 Database seeding is not allowed in production environment!');
}

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration:');
  console.error('   PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   PUBLIC_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Set' : '❌ Missing');
  throw new Error('Missing Supabase configuration. Please check your .env file.');
}

console.log('🌱 Running simple seed test...');

const supabase = createClient(supabaseUrl, supabaseKey);

try {
  // Test 1: Create a simple organization
  console.log('📝 Creating test organization...');
  const { data: org, error: orgError } = await supabase
    .from('organizations')
    .insert({
      id: faker.string.uuid(),
      name: 'Test Organization',
      created_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (orgError) {
    console.error('❌ Failed to create organization:', orgError.message);
    process.exit(1);
  }

  console.log('✅ Organization created:', org.name);

  // Test 2: Check if we can read it back
  const { data: readOrg, error: readError } = await supabase
    .from('organizations')
    .select('*')
    .eq('id', org.id)
    .single();

  if (readError) {
    console.error('❌ Failed to read organization:', readError.message);
    process.exit(1);
  }

  console.log('✅ Organization read back successfully');

  // Test 3: Clean up
  const { error: deleteError } = await supabase
    .from('organizations')
    .delete()
    .eq('id', org.id);

  if (deleteError) {
    console.error('❌ Failed to delete organization:', deleteError.message);
  } else {
    console.log('✅ Test organization cleaned up');
  }

  console.log('\n🎉 Simple seed test completed successfully!');
  console.log('✅ Supabase connection working');
  console.log('✅ faker.js working');
  console.log('✅ Database permissions working');
  console.log('\nReady to run full seeder!');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
}