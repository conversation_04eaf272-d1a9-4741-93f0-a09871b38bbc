#!/usr/bin/env node

import { readFileSync } from "fs";
import { join } from "path";

// Load environment variables from .env file
function loadEnv() {
  try {
    const envPath = join(process.cwd(), ".env");
    const envFile = readFileSync(envPath, "utf8");
    
    envFile.split("\n").forEach(line => {
      const [key, ...valueParts] = line.split("=");
      if (key && valueParts.length > 0 && !key.startsWith("#")) {
        const value = valueParts.join("=").trim();
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.warn("⚠️  Could not load .env file:", error.message);
  }
}

// Simple test script to verify seeder works
async function testSeeder() {
  try {
    console.log("🧪 Testing seeder dependencies...");

    // Load environment variables
    loadEnv();

    // Test imports
    const { faker } = await import("@faker-js/faker");
    console.log("✅ @faker-js/faker imported successfully");

    const { createClient } = await import("@supabase/supabase-js");
    console.log("✅ @supabase/supabase-js imported successfully");

    // Test environment
    const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.log("❌ Missing Supabase environment variables");
      console.log("   PUBLIC_SUPABASE_URL:", supabaseUrl ? "✅ Set" : "❌ Missing");
      console.log("   PUBLIC_SUPABASE_ANON_KEY:", supabaseKey ? "✅ Set" : "❌ Missing");
      return;
    }

    console.log("✅ Supabase environment variables found");

    // Test Supabase connection
    const supabase = createClient(supabaseUrl, supabaseKey);
    const { data, error } = await supabase.from("organizations").select("count", { count: "exact", head: true });

    if (error) {
      console.log("❌ Supabase connection failed:", error.message);
      return;
    }

    console.log("✅ Supabase connection successful");
    console.log(`📊 Current organizations count: ${data?.length || 0}`);

    console.log("\n🎉 All dependencies working! Ready to run seeder.");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

testSeeder();